<?php
// app/Services/GitHubAnchorService.php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\GitHubAnchorInterface;

class GitHubAnchorService implements GitHubAnchorInterface
{
    /**
     * Generate GitHub-style anchor from heading text.
     */
    public function generateAnchor(string $headingText): string
    {
        // Remove markdown formatting
        $text = preg_replace('/[*_`~]/', '', $headingText);

        // Convert to lowercase
        $text = strtolower($text);

        // Replace spaces and special characters with hyphens
        $text = preg_replace('/[^a-z0-9\-]/', '-', $text);

        // Remove multiple consecutive hyphens
        $text = preg_replace('/-+/', '-', $text);

        // Remove leading and trailing hyphens
        return trim($text, '-');
    }

    /**
     * Extract all anchors from markdown content.
     */
    public function extractAnchors(string $content): array
    {
        $anchors = [];

        // Extract headings (# ## ### etc.)
        preg_match_all('/^(#{1,6})\s+(.+)$/m', $content, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $level = strlen($match[1]);
            $text = trim($match[2]);
            $anchor = $this->generateAnchor($text);

            $anchors[] = [
                'text' => $text,
                'anchor' => $anchor,
                'level' => $level,
                'line' => substr_count($content, "\n", 0, strpos($content, $match[0])) + 1
            ];
        }

        return $anchors;
    }

    /**
     * Validate anchor exists in content.
     */
    public function validateAnchor(string $anchor, string $content): bool
    {
        $anchors = $this->extractAnchors($content);
        $normalizedAnchor = $this->normalizeAnchor($anchor);

        foreach ($anchors as $anchorData) {
            if ($this->normalizeAnchor($anchorData['anchor']) === $normalizedAnchor) {
                return true;
            }
        }

        return false;
    }

    /**
     * Normalize anchor for comparison.
     */
    public function normalizeAnchor(string $anchor): string
    {
        // Remove leading # if present
        $anchor = ltrim($anchor, '#');

        // Convert to lowercase for comparison
        return strtolower($anchor);
    }

    /**
     * Check if anchor follows GitHub conventions.
     */
    public function isValidGitHubAnchor(string $anchor): bool
    {
        // Remove leading # if present
        $anchor = ltrim($anchor, '#');

        // GitHub anchors should only contain lowercase letters, numbers, and hyphens
        return preg_match('/^[a-z0-9\-]+$/', $anchor) === 1;
    }

    /**
     * Generate anchor map for content.
     */
    public function generateAnchorMap(string $content): array
    {
        $anchors = $this->extractAnchors($content);
        $map = [];

        foreach ($anchors as $anchorData) {
            $map[$anchorData['anchor']] = [
                'text' => $anchorData['text'],
                'level' => $anchorData['level'],
                'line' => $anchorData['line']
            ];
        }

        return $map;
    }

    /**
     * Find duplicate anchors in content.
     */
    public function findDuplicateAnchors(string $content): array
    {
        $anchors = $this->extractAnchors($content);
        $anchorCounts = [];
        $duplicates = [];

        foreach ($anchors as $anchorData) {
            $anchor = $anchorData['anchor'];

            if (!isset($anchorCounts[$anchor])) {
                $anchorCounts[$anchor] = [];
            }

            $anchorCounts[$anchor][] = $anchorData;
        }

        foreach ($anchorCounts as $anchor => $occurrences) {
            if (count($occurrences) > 1) {
                $duplicates[$anchor] = $occurrences;
            }
        }

        return $duplicates;
    }
}
