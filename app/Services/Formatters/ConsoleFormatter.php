<?php

declare(strict_types=1);

namespace App\Services\Formatters;

use App\Services\ValueObjects\ValidationResult;

final class ConsoleFormatter
{
    /**
     * Format validation result for console output.
     */
    public function format(ValidationResult $result, array $statistics): string
    {
        $output = [];

        // Header
        $output[] = $this->formatHeader($result);

        // Summary
        $output[] = $this->formatSummary($result->getSummary());

        // Broken links details
        if (!empty($result->broken)) {
            $output[] = $this->formatBrokenLinks($result->broken);
        }

        // Statistics
        $output[] = $this->formatStatistics($statistics);

        return implode("\n\n", $output);
    }

    private function formatHeader(ValidationResult $result): string
    {
        $status = $result->isSuccessful() ? '✅ PASSED' : '❌ FAILED';
        $color = $result->isSuccessful() ? 'green' : 'red';

        return "<fg={$color};options=bold>🔗 Link Validation {$status}</>";
    }

    private function formatSummary(array $summary): string
    {
        $lines = [
            '<fg=blue;options=bold>📊 Validation Summary</>',
            "📁 Files processed: {$summary['files_processed']}",
            "🔗 Total links: {$summary['total_links']}",
            "❌ Broken links: {$summary['broken_links']}",
            "📈 Success rate: {$summary['success_rate']}%",
            "⏱️  Duration: {$summary['duration']}s"
        ];

        return implode("\n", $lines);
    }

    private function formatBrokenLinks(array $broken): string
    {
        $lines = ['<fg=red;options=bold>💥 Broken Links Details</>'];

        foreach ($broken as $link) {
            $lines[] = "  🔴 {$link['url']}";
            $lines[] = "     📄 File: {$link['file']}";
            $lines[] = "     ❗ Reason: {$link['reason']}";
            $lines[] = '';
        }

        return implode("\n", $lines);
    }

    private function formatStatistics(array $statistics): string
    {
        $lines = ['<fg=cyan;options=bold>📈 Detailed Statistics</>'];

        foreach ($statistics as $key => $value) {
            if (is_array($value)) {
                $lines[] = "  {$key}:";
                foreach ($value as $subKey => $subValue) {
                    $lines[] = "    {$subKey}: {$subValue}";
                }
            } else {
                $lines[] = "  {$key}: {$value}";
            }
        }

        return implode("\n", $lines);
    }
}
