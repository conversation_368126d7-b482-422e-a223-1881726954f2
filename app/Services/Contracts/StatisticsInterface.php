<?php

declare(strict_types=1);

namespace App\Services\Contracts;

interface StatisticsInterface
{
    /**
     * Reset all statistics counters.
     */
    public function reset(): void;

    /**
     * Increment file processing counter.
     */
    public function incrementFilesProcessed(): void;

    /**
     * Add link statistics for a specific type.
     */
    public function addLinkStats(string $type, int $total, int $broken): void;

    /**
     * Record a broken link with details.
     */
    public function recordBrokenLink(string $link, string $file, string $reason, string $type): void;

    /**
     * Get complete statistics array.
     */
    public function getStatistics(): array;

    /**
     * Get total broken links count.
     */
    public function getTotalBrokenLinks(): int;

    /**
     * Get processed files list.
     */
    public function getProcessedFiles(): array;
}
