<?php

declare(strict_types=1);

namespace App\Services\Contracts;

use App\Services\ValueObjects\ValidationConfig;
use App\Services\ValueObjects\ValidationResult;

interface LinkValidationInterface
{
    /**
     * Validate all links in a file.
     */
    public function validateFile(string $filePath, array $scope): array;

    /**
     * Validate a collection of links.
     */
    public function validateLinks(array $links, array $scope): array;

    /**
     * Validate external HTTP/HTTPS links.
     */
    public function validateExternalLinks(array $links): array;

    /**
     * Validate internal file and directory links.
     */
    public function validateInternalLinks(array $links, string $basePath): array;

    /**
     * Validate anchor links within content.
     */
    public function validateAnchorLinks(array $links, string $content): array;

    /**
     * Validate cross-references between files.
     */
    public function validateCrossReferences(array $files): array;

    /**
     * Extract all links from content.
     */
    public function extractLinks(string $content): array;

    /**
     * Categorize links by type (internal, external, anchor).
     */
    public function categorizeLinks(array $links): array;
}
