<?php

declare(strict_types=1);

namespace App\Services\Contracts;

interface GitHubAnchorInterface
{
    /**
     * Generate GitHub-style anchor from heading text.
     */
    public function generateAnchor(string $headingText): string;

    /**
     * Extract all anchors from markdown content.
     */
    public function extractAnchors(string $content): array;

    /**
     * Validate anchor exists in content.
     */
    public function validateAnchor(string $anchor, string $content): bool;

    /**
     * Normalize anchor for comparison.
     */
    public function normalizeAnchor(string $anchor): string;

    /**
     * Check if anchor follows GitHub conventions.
     */
    public function isValidGitHubAnchor(string $anchor): bool;

    /**
     * Generate anchor map for content.
     */
    public function generateAnchorMap(string $content): array;

    /**
     * Find duplicate anchors in content.
     */
    public function findDuplicateAnchors(string $content): array;
}
