<?php

declare(strict_types=1);

namespace App\Services\Contracts;

interface SecurityValidationInterface
{
    /**
     * Validate file path for security issues.
     */
    public function validatePath(string $path): bool;

    /**
     * Validate URL for security issues.
     */
    public function validateUrl(string $url): bool;

    /**
     * Validate file size limits.
     */
    public function validateFileSize(string $filePath): bool;

    /**
     * Sanitize path for safe usage.
     */
    public function sanitizePath(string $path): string;

    /**
     * Check for path traversal attempts.
     */
    public function isPathTraversalAttempt(string $path): bool;
}
