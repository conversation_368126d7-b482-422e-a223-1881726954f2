<?php

declare(strict_types=1);

namespace App\Services\Contracts;

use App\Services\ValueObjects\ValidationResult;

interface ReportingInterface
{
    /**
     * Generate report in specified format.
     */
    public function generateReport(array $results, string $format): string;

    /**
     * Save report to file.
     */
    public function saveReport(string $report, string $filePath): bool;

    /**
     * Generate summary statistics from results.
     */
    public function generateSummary(array $results): array;

    /**
     * Format validation results for console output.
     */
    public function formatForConsole(array $results): string;

    /**
     * Format validation results as JSON.
     */
    public function formatAsJson(array $results): string;

    /**
     * Format validation results as HTML.
     */
    public function formatAsHtml(array $results): string;

    /**
     * Format validation results as Markdown.
     */
    public function formatAsMarkdown(array $results): string;

    /**
     * Get available output formats.
     */
    public function getAvailableFormats(): array;

    /**
     * Check if format is supported.
     */
    public function supportsFormat(string $format): bool;
}
