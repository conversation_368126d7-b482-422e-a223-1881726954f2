<?php

declare(strict_types=1);

namespace App\Services\ValueObjects;

final readonly class ValidationConfig
{
    public function __construct(
        public array $paths,
        public array $scope,
        public int $maxDepth,
        public bool $includeHidden,
        public array $excludePatterns,
        public bool $checkExternal,
        public int $timeout,
        public string $format,
        public ?string $output,
        public int $maxBroken,
        public bool $dryRun,
        public bool $fix,
        public bool $interactive,
        public bool $cache,
        public bool $showProgress,
        public bool $verbose
    ) {}

    /**
     * Create configuration from command options.
     */
    public static function fromCommandOptions(array $options, array $arguments): self
    {
        return new self(
            paths: $arguments['path'] ?? [],
            scope: $options['scope'] ?? ['internal', 'anchor'],
            maxDepth: (int) ($options['max-depth'] ?? 0),
            includeHidden: (bool) ($options['include-hidden'] ?? false),
            excludePatterns: $options['exclude'] ?? [],
            checkExternal: (bool) ($options['check-external'] ?? false),
            timeout: (int) ($options['timeout'] ?? 30),
            format: $options['format'] ?? 'console',
            output: $options['output'] ?? null,
            maxBroken: (int) ($options['max-broken'] ?? 50),
            dryRun: (bool) ($options['dry-run'] ?? false),
            fix: (bool) ($options['fix'] ?? false),
            interactive: (bool) ($options['interactive'] ?? false),
            cache: (bool) ($options['cache'] ?? false),
            showProgress: !($options['no-progress'] ?? false),
            verbose: (bool) ($options['verbose'] ?? false)
        );
    }

    /**
     * Create configuration with default values.
     */
    public static function withDefaults(array $overrides = []): self
    {
        $defaults = [
            'paths' => [],
            'scope' => ['internal', 'anchor'],
            'maxDepth' => 0,
            'includeHidden' => false,
            'excludePatterns' => [],
            'checkExternal' => false,
            'timeout' => 30,
            'format' => 'console',
            'output' => null,
            'maxBroken' => 50,
            'dryRun' => false,
            'fix' => false,
            'interactive' => false,
            'cache' => false,
            'showProgress' => true,
            'verbose' => false
        ];

        $config = array_merge($defaults, $overrides);

        return new self(...$config);
    }

    /**
     * Create a copy with modified values.
     */
    public function with(array $changes): self
    {
        $current = [
            'paths' => $this->paths,
            'scope' => $this->scope,
            'maxDepth' => $this->maxDepth,
            'includeHidden' => $this->includeHidden,
            'excludePatterns' => $this->excludePatterns,
            'checkExternal' => $this->checkExternal,
            'timeout' => $this->timeout,
            'format' => $this->format,
            'output' => $this->output,
            'maxBroken' => $this->maxBroken,
            'dryRun' => $this->dryRun,
            'fix' => $this->fix,
            'interactive' => $this->interactive,
            'cache' => $this->cache,
            'showProgress' => $this->showProgress,
            'verbose' => $this->verbose
        ];

        return new self(...array_merge($current, $changes));
    }
}
