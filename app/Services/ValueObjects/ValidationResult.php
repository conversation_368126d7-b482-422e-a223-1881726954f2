<?php

declare(strict_types=1);

namespace App\Services\ValueObjects;

final readonly class ValidationResult
{
    public function __construct(
        public array $files,
        public array $links,
        public array $broken,
        public array $statistics,
        public float $duration,
        public bool $success
    ) {}

    /**
     * Create successful result.
     */
    public static function success(
        array $files,
        array $links,
        array $broken,
        array $statistics,
        float $duration
    ): self {
        return new self($files, $links, $broken, $statistics, $duration, empty($broken));
    }

    /**
     * Create failed result.
     */
    public static function failure(
        array $files,
        array $links,
        array $broken,
        array $statistics,
        float $duration,
        string $error = null
    ): self {
        $stats = $statistics;
        if ($error) {
            $stats['error'] = $error;
        }

        return new self($files, $links, $broken, $stats, $duration, false);
    }

    /**
     * Get total link count.
     */
    public function getTotalLinks(): int
    {
        return count($this->links);
    }

    /**
     * Get broken link count.
     */
    public function getBrokenCount(): int
    {
        return count($this->broken);
    }

    /**
     * Get success rate as percentage.
     */
    public function getSuccessRate(): float
    {
        $total = $this->getTotalLinks();
        if ($total === 0) {
            return 100.0;
        }

        return (($total - $this->getBrokenCount()) / $total) * 100;
    }

    /**
     * Check if validation passed.
     */
    public function isSuccessful(): bool
    {
        return $this->success && $this->getBrokenCount() === 0;
    }

    /**
     * Get formatted summary.
     */
    public function getSummary(): array
    {
        return [
            'files_processed' => count($this->files),
            'total_links' => $this->getTotalLinks(),
            'broken_links' => $this->getBrokenCount(),
            'success_rate' => round($this->getSuccessRate(), 2),
            'duration' => round($this->duration, 2),
            'status' => $this->isSuccessful() ? 'passed' : 'failed'
        ];
    }
}
