<?php
// app/Services/ReportingService.php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\ReportingInterface;
use App\Services\Contracts\StatisticsInterface;
use App\Services\Formatters\ConsoleFormatter;
use App\Services\Formatters\JsonFormatter;
use App\Services\Formatters\MarkdownFormatter;
use App\Services\Formatters\HtmlFormatter;
use App\Services\ValueObjects\ValidationResult;
use Illuminate\Console\Command;
use InvalidArgumentException;

class ReportingService implements ReportingInterface
{
    private array $formatters;
    private StatisticsInterface $statistics;

    public function __construct(StatisticsInterface $statistics)
    {
        $this->statistics = $statistics;
        $this->formatters = [
            'console' => new ConsoleFormatter(),
            'json' => new JsonFormatter(),
            'markdown' => new MarkdownFormatter(),
            'html' => new HtmlFormatter(),
        ];
    }

    public function generateReport(array $results, string $format): string
    {
        // Validate the format
        if (!isset($this->formatters[$format])) {
            throw new InvalidArgumentException("Unsupported format: {$format}");
        }

        $formatter = $this->formatters[$format];
        $statistics = $this->statistics->getStatistics();

        // Generate the report using the specified formatter
        return $formatter->format($results, $statistics);
    }

    public function formatForConsole(array $results): string
    {
        return $this->formatters['console']->format($results, $this->statistics->getStatistics());
    }

    public function formatAsJson(array $results): string
    {
        return $this->formatters['json']->format($results, $this->statistics->getStatistics());
    }

    public function formatAsHtml(array $results): string
    {
        return $this->formatters['html']->format($results, $this->statistics->getStatistics());
    }

    public function formatAsMarkdown(array $results): string
    {
        return $this->formatters['markdown']->format($results, $this->statistics->getStatistics());
    }

    public function saveReport(string $content, string $filePath): bool
    {
        $directory = dirname($filePath);

        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        return file_put_contents($filePath, $content) !== false;
    }

    public function getAvailableFormats(): array
    {
        return array_keys($this->formatters);
    }

    public function supportsFormat(string $format): bool
    {
        return isset($this->formatters[$format]);
    }

    public function addFormatter(string $name, object $formatter): void
    {
        $this->formatters[$name] = $formatter;
    }

    public function generateSummary(array $results): array
    {
        $summary = [
            'total_files' => 0,
            'total_links' => 0,
            'broken_links' => 0,
            'external_links' => 0,
            'internal_links' => 0,
            'anchor_links' => 0,
        ];

        foreach ($results as $fileResult) {
            $summary['total_files']++;
            $summary['total_links'] += count($fileResult['links'] ?? []);

            foreach ($fileResult['links'] ?? [] as $link) {
                if ($link['status'] === 'broken') {
                    $summary['broken_links']++;
                }

                match ($link['type']) {
                    'external' => $summary['external_links']++,
                    'internal' => $summary['internal_links']++,
                    'anchor' => $summary['anchor_links']++,
                    default => null,
                };
            }
        }

        return $summary;
    }


}
