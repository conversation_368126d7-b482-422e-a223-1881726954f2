<?php
// app/Services/StatisticsService.php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\StatisticsInterface;

class StatisticsService implements StatisticsInterface
{
    private array $statistics;
    private array $brokenLinks;
    private array $processedFiles;

    public function __construct()
    {
        $this->reset();
    }

    /**
     * Reset all statistics counters.
     */
    public function reset(): void
    {
        $this->statistics = [
            'files_processed' => 0,
            'total_links' => 0,
            'broken_links' => 0,
            'external_links' => 0,
            'internal_links' => 0,
            'anchor_links' => 0,
            'start_time' => microtime(true),
            'end_time' => null,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
        ];

        $this->brokenLinks = [];
        $this->processedFiles = [];
    }

    /**
     * Increment file processing counter.
     */
    public function incrementFilesProcessed(): void
    {
        $this->statistics['files_processed']++;
    }

    /**
     * Add link statistics for a specific type.
     */
    public function addLinkStats(string $type, int $total, int $broken): void
    {
        $this->statistics['total_links'] += $total;
        $this->statistics['broken_links'] += $broken;

        match ($type) {
            'external' => $this->statistics['external_links'] += $total,
            'internal' => $this->statistics['internal_links'] += $total,
            'anchor' => $this->statistics['anchor_links'] += $total,
            default => null,
        };
    }

    /**
     * Record a broken link with details.
     */
    public function recordBrokenLink(string $link, string $file, string $reason, string $type): void
    {
        $this->brokenLinks[] = [
            'url' => $link,
            'file' => $file,
            'reason' => $reason,
            'type' => $type,
            'timestamp' => microtime(true),
        ];
    }

    /**
     * Get complete statistics array.
     */
    public function getStatistics(): array
    {
        $stats = $this->statistics;

        // Calculate execution time if not already set
        if ($stats['end_time'] === null) {
            $stats['execution_time'] = microtime(true) - $stats['start_time'];
        } else {
            $stats['execution_time'] = $stats['end_time'] - $stats['start_time'];
        }

        // Update memory usage
        $stats['current_memory'] = memory_get_usage(true);
        $stats['peak_memory'] = memory_get_peak_usage(true);

        // Calculate success rate
        if ($stats['total_links'] > 0) {
            $stats['success_rate'] = round(
                (($stats['total_links'] - $stats['broken_links']) / $stats['total_links']) * 100,
                2
            );
        } else {
            $stats['success_rate'] = 100.0;
        }

        // Add performance metrics
        if ($stats['execution_time'] > 0) {
            $stats['files_per_second'] = round($stats['files_processed'] / $stats['execution_time'], 2);
            $stats['links_per_second'] = round($stats['total_links'] / $stats['execution_time'], 2);
        } else {
            $stats['files_per_second'] = 0;
            $stats['links_per_second'] = 0;
        }

        return $stats;
    }

    /**
     * Get total broken links count.
     */
    public function getTotalBrokenLinks(): int
    {
        return $this->statistics['broken_links'];
    }

    /**
     * Get processed files list.
     */
    public function getProcessedFiles(): array
    {
        return $this->processedFiles;
    }

    /**
     * Record a processed file.
     */
    public function recordProcessedFile(string $filePath, array $results): void
    {
        $this->processedFiles[] = [
            'path' => $filePath,
            'results' => $results,
            'timestamp' => microtime(true),
        ];

        $this->incrementFilesProcessed();
    }

    /**
     * Mark statistics collection as complete.
     */
    public function markComplete(): void
    {
        $this->statistics['end_time'] = microtime(true);
    }

    /**
     * Get broken links details.
     */
    public function getBrokenLinks(): array
    {
        return $this->brokenLinks;
    }

    /**
     * Get summary statistics for reporting.
     */
    public function getSummary(): array
    {
        $stats = $this->getStatistics();

        return [
            'total_files' => $stats['files_processed'],
            'total_links' => $stats['total_links'],
            'broken_links' => $stats['broken_links'],
            'success_rate' => $stats['success_rate'],
            'execution_time' => $stats['execution_time'],
            'performance' => [
                'files_per_second' => $stats['files_per_second'],
                'links_per_second' => $stats['links_per_second'],
                'memory_usage' => $this->formatBytes($stats['current_memory']),
                'peak_memory' => $this->formatBytes($stats['peak_memory']),
            ],
        ];
    }

    /**
     * Format bytes into human-readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor(log($bytes, 1024));

        return sprintf('%.2f %s', $bytes / (1024 ** $factor), $units[$factor]);
    }
}
