<?php

declare(strict_types=1);

namespace App\Commands;

use App\Services\ValueObjects\ValidationConfig;
use LaravelZero\Framework\Commands\Command;

abstract class BaseValidationCommand extends Command
{
    /**
     * Create validation configuration from command options.
     */
    protected function createConfigFromOptions(): ValidationConfig
    {
        return ValidationConfig::fromCommandOptions(
            $this->options(),
            $this->arguments()
        );
    }

    /**
     * Display validation configuration summary.
     */
    protected function displayValidationSummary(ValidationConfig $config, array $paths): void
    {
        $this->info('🔗 Link Validation Configuration');
        $this->newLine();

        $this->line('📁 Paths: ' . implode(', ', $paths));
        $this->line('🎯 Scope: ' . implode(', ', $config->scope));
        $this->line('🌐 External links: ' . ($config->checkExternal ? '✅ Yes' : '❌ No'));
        $this->line("📊 Output format: {$config->format}");

        if ($config->maxBroken > 0) {
            $this->line("🛑 Max broken links: {$config->maxBroken}");
        }

        if ($config->dryRun) {
            $this->warn('🔍 DRY RUN MODE - No actual validation');
        }

        $this->newLine();
    }

    /**
     * Collect files from paths based on configuration.
     */
    protected function collectFiles(array $paths, ValidationConfig $config): array
    {
        $this->info('📋 Collecting files...');

        $files = [];
        $patterns = config('validate-links.files.default_patterns', ['**/*.md']);
        $excludePatterns = array_merge(
            config('validate-links.files.exclude_patterns', []),
            $config->excludePatterns
        );

        foreach ($paths as $path) {
            if (is_file($path)) {
                $files[] = $path;
            } elseif (is_dir($path)) {
                $files = array_merge($files, $this->findFilesInDirectory(
                    $path,
                    $patterns,
                    $excludePatterns,
                    $config
                ));
            }
        }

        return array_unique($files);
    }

    /**
     * Find files in directory matching patterns.
     */
    private function findFilesInDirectory(
        string           $directory,
        array            $patterns,
        array            $excludePatterns,
        ValidationConfig $config
    ): array
    {
        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if (!$file->isFile()) {
                continue;
            }

            $filePath = $file->getPathname();
            $relativePath = str_replace($directory . '/', '', $filePath);

            // Check depth limit
            if ($config->maxDepth > 0) {
                $depth = substr_count($relativePath, '/');
                if ($depth > $config->maxDepth) {
                    continue;
                }
            }

            // Check hidden files
            if (!$config->includeHidden && $this->isHiddenFile($relativePath)) {
                continue;
            }

            // Check exclude patterns
            if ($this->matchesPatterns($relativePath, $excludePatterns)) {
                continue;
            }

            // Check include patterns
            if ($this->matchesPatterns($relativePath, $patterns)) {
                $files[] = $filePath;
            }
        }

        return $files;
    }

    /**
     * Check if file is hidden.
     */
    private function isHiddenFile(string $path): bool
    {
        $parts = explode('/', $path);
        foreach ($parts as $part) {
            if (str_starts_with($part, '.')) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if path matches any of the given patterns.
     */
    private function matchesPatterns(string $path, array $patterns): bool
    {
        foreach ($patterns as $pattern) {
            if (fnmatch($pattern, $path)) {
                return true;
            }
        }
        return false;
    }
}
