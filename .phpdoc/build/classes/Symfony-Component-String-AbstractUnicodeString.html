<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
            <title>Documentation</title>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="../">
    <link rel="icon" href="images/favicon.ico"/>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/base.css">
            <link rel="preconnect" href="https://fonts.gstatic.com">
        <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@100;200;300;400;600;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Source+Code+Pro:wght@400;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="css/template.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/css/all.min.css" integrity="sha256-ybRkN9dBjhcS2qrW1z+hfCxq+1aBdwyQM5wlQoQVt/0=" crossorigin="anonymous" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/themes/prism-okaidia.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-numbers/prism-line-numbers.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-highlight/prism-line-highlight.css">
                <script src="https://cdn.jsdelivr.net/npm/fuse.js@3.4.6"></script>
        <script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2"></script>
        <script src="js/template.js"></script>
        <script src="js/search.js"></script>
        <script defer src="js/searchIndex.js"></script>
    </head>
<body id="top">
    <header class="phpdocumentor-header phpdocumentor-section">
    <h1 class="phpdocumentor-title"><a href="" class="phpdocumentor-title__link">Documentation</a></h1>
    <input class="phpdocumentor-header__menu-button" type="checkbox" id="menu-button" name="menu-button" />
    <label class="phpdocumentor-header__menu-icon" for="menu-button">
        <i class="fas fa-bars"></i>
    </label>
    <section data-search-form class="phpdocumentor-search">
    <label>
        <span class="visually-hidden">Search for</span>
        <svg class="phpdocumentor-search__icon" width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="7.5" cy="7.5" r="6.5" stroke="currentColor" stroke-width="2"/>
            <line x1="12.4892" y1="12.2727" x2="19.1559" y2="18.9393" stroke="currentColor" stroke-width="3"/>
        </svg>
        <input type="search" class="phpdocumentor-field phpdocumentor-search__field" placeholder="Loading .." disabled />
    </label>
</section>

    <nav class="phpdocumentor-topnav">
    <ul class="phpdocumentor-topnav__menu">
        </ul>
</nav>
</header>

    <main class="phpdocumentor">
        <div class="phpdocumentor-section">
            <input class="phpdocumentor-sidebar__menu-button" type="checkbox" id="sidebar-button" name="sidebar-button" />
<label class="phpdocumentor-sidebar__menu-icon" for="sidebar-button">
    Menu
</label>
<aside class="phpdocumentor-column -three phpdocumentor-sidebar">
                    <section class="phpdocumentor-sidebar__category -namespaces">
            <h2 class="phpdocumentor-sidebar__category-header">Namespaces</h2>
                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/app.html" class="">App</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/app-commands.html" class="">Commands</a>
                
            </li>
                    <li>
                <a href="namespaces/app-providers.html" class="">Providers</a>
                
            </li>
                    <li>
                <a href="namespaces/app-services.html" class="">Services</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/tests.html" class="">Tests</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/illuminate.html" class="">Illuminate</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/illuminate-bus.html" class="">Bus</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-console.html" class="">Console</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-redis.html" class="">Redis</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-cache.html" class="">Cache</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-support.html" class="">Support</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-config.html" class="">Config</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-container.html" class="">Container</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-contracts.html" class="">Contracts</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-database.html" class="">Database</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-events.html" class="">Events</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-filesystem.html" class="">Filesystem</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-http.html" class="">Http</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-pipeline.html" class="">Pipeline</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-process.html" class="">Process</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-session.html" class="">Session</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-testing.html" class="">Testing</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-view.html" class="">View</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-foundation.html" class="">Foundation</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/paratest.html" class="">ParaTest</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/brick.html" class="">Brick</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/brick-math.html" class="">Math</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/composer.html" class="">Composer</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/composer-autoload.html" class="">Autoload</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/doctrine.html" class="">Doctrine</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/doctrine-deprecations.html" class="">Deprecations</a>
                
            </li>
                    <li>
                <a href="namespaces/doctrine-inflector.html" class="">Inflector</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/cron.html" class="">Cron</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rectorlaravel.html" class="">RectorLaravel</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/rectorlaravel-commands.html" class="">Commands</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-contract.html" class="">Contract</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-nodeanalyzer.html" class="">NodeAnalyzer</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-nodefactory.html" class="">NodeFactory</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-rector.html" class="">Rector</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-set.html" class="">Set</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-valueobject.html" class="">ValueObject</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/ergebnis.html" class="">Ergebnis</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/ergebnis-composer.html" class="">Composer</a>
                
            </li>
                    <li>
                <a href="namespaces/ergebnis-json.html" class="">Json</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/fidry.html" class="">Fidry</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/fidry-cpucorecounter.html" class="">CpuCoreCounter</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/whoops.html" class="">Whoops</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/whoops-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/whoops-handler.html" class="">Handler</a>
                
            </li>
                    <li>
                <a href="namespaces/whoops-inspector.html" class="">Inspector</a>
                
            </li>
                    <li>
                <a href="namespaces/whoops-util.html" class="">Util</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/fruitcake.html" class="">Fruitcake</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/fruitcake-cors.html" class="">Cors</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/grahamcampbell.html" class="">GrahamCampbell</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/grahamcampbell-resulttype.html" class="">ResultType</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/guzzlehttp.html" class="">GuzzleHttp</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/guzzlehttp-cookie.html" class="">Cookie</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-handler.html" class="">Handler</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-promise.html" class="">Promise</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-psr7.html" class="">Psr7</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-uritemplate.html" class="">UriTemplate</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/hamcrest.html" class="">Hamcrest</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/hamcrest-arrays.html" class="">Arrays</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-collection.html" class="">Collection</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-core.html" class="">Core</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-internal.html" class="">Internal</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-number.html" class="">Number</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-text.html" class="">Text</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-type.html" class="">Type</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-xml.html" class="">Xml</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/iamcal.html" class="">iamcal</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/jean85.html" class="">Jean85</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/jean85-exception.html" class="">Exception</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/joli.html" class="">Joli</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/joli-jolinotif.html" class="">JoliNotif</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phar.html" class="">phar</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/qa.html" class="">qa</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/qa-cs.html" class="">cs</a>
                
            </li>
                    <li>
                <a href="namespaces/qa-phpstan.html" class="">phpstan</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/jolicode.html" class="">JoliCode</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/jolicode-phposhelper.html" class="">PhpOsHelper</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/jsonschema.html" class="">JsonSchema</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/jsonschema-constraints.html" class="">Constraints</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-entity.html" class="">Entity</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-iterator.html" class="">Iterator</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-tool.html" class="">Tool</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-uri.html" class="">Uri</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/larastan.html" class="">Larastan</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/larastan-larastan.html" class="">Larastan</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/laravel.html" class="">Laravel</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/laravel-prompts.html" class="">Prompts</a>
                
            </li>
                    <li>
                <a href="namespaces/laravel-serializableclosure.html" class="">SerializableClosure</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/database.html" class="">Database</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/database-seeders.html" class="">Seeders</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/laravelzero.html" class="">LaravelZero</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/laravelzero-framework.html" class="">Framework</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/league.html" class="">League</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/league-flysystem.html" class="">Flysystem</a>
                
            </li>
                    <li>
                <a href="namespaces/league-mimetypedetection.html" class="">MimeTypeDetection</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/localheinz.html" class="">Localheinz</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/localheinz-diff.html" class="">Diff</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/mabeenum.html" class="">MabeEnum</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/masterminds.html" class="">Masterminds</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/masterminds-html5.html" class="">HTML5</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/mockery.html" class="">Mockery</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/mockery-adapter.html" class="">Adapter</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-countvalidator.html" class="">CountValidator</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-generator.html" class="">Generator</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-loader.html" class="">Loader</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-matcher.html" class="">Matcher</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/deepcopy.html" class="">DeepCopy</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/deepcopy-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-filter.html" class="">Filter</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-matcher.html" class="">Matcher</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-reflection.html" class="">Reflection</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-typefilter.html" class="">TypeFilter</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-typematcher.html" class="">TypeMatcher</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/carbon.html" class="">Carbon</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/carbon-doctrine.html" class="">Doctrine</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-messageformatter.html" class="">MessageFormatter</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-cli.html" class="">Cli</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-exceptions.html" class="">Exceptions</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-laravel.html" class="">Laravel</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-phpstan.html" class="">PHPStan</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-traits.html" class="">Traits</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/nette.html" class="">Nette</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/nette-utils.html" class="">Utils</a>
                
            </li>
                    <li>
                <a href="namespaces/nette-localization.html" class="">Localization</a>
                
            </li>
                    <li>
                <a href="namespaces/nette-iterators.html" class="">Iterators</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpparser.html" class="">PhpParser</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpparser-builder.html" class="">Builder</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-comment.html" class="">Comment</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-errorhandler.html" class="">ErrorHandler</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-lexer.html" class="">Lexer</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-node.html" class="">Node</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-nodevisitor.html" class="">NodeVisitor</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-parser.html" class="">Parser</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-prettyprinter.html" class="">PrettyPrinter</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/nunomaduro.html" class="">NunoMaduro</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/nunomaduro-collision.html" class="">Collision</a>
                
            </li>
                    <li>
                <a href="namespaces/nunomaduro-laravelconsolesummary.html" class="">LaravelConsoleSummary</a>
                
            </li>
                    <li>
                <a href="namespaces/nunomaduro-laravelconsoletask.html" class="">LaravelConsoleTask</a>
                
            </li>
                    <li>
                <a href="namespaces/nunomaduro-laraveldesktopnotifier.html" class="">LaravelDesktopNotifier</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/termwind.html" class="">Termwind</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/termwind-components.html" class="">Components</a>
                
            </li>
                    <li>
                <a href="namespaces/termwind-enums.html" class="">Enums</a>
                
            </li>
                    <li>
                <a href="namespaces/termwind-laravel.html" class="">Laravel</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/peck.html" class="">Peck</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/peck-plugins.html" class="">Plugins</a>
                
            </li>
                    <li>
                <a href="namespaces/peck-services.html" class="">Services</a>
                
            </li>
                    <li>
                <a href="namespaces/peck-support.html" class="">Support</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/pest.html" class="">Pest</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/pest-configuration.html" class="">Configuration</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-contracts.html" class="">Contracts</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-exceptions.html" class="">Exceptions</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-factories.html" class="">Factories</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-plugins.html" class="">Plugins</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-support.html" class="">Support</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-testcasefilters.html" class="">TestCaseFilters</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-testcasemethodfilters.html" class="">TestCaseMethodFilters</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-arch.html" class="">Arch</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-mutate.html" class="">Mutate</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-stressless.html" class="">Stressless</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phario.html" class="">PharIo</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phario-manifest.html" class="">Manifest</a>
                
            </li>
                    <li>
                <a href="namespaces/phario-csfixer.html" class="">CSFixer</a>
                
            </li>
                    <li>
                <a href="namespaces/phario-version.html" class="">Version</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpdocumentor.html" class="">phpDocumentor</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpdocumentor-reflection.html" class="">Reflection</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpoption.html" class="">PhpOption</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpstan.html" class="">PHPStan</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpstan-phpdocparser.html" class="">PhpDocParser</a>
                
            </li>
                    <li>
                <a href="namespaces/phpstan-dependencyinjection.html" class="">DependencyInjection</a>
                
            </li>
                    <li>
                <a href="namespaces/phpstan-rules.html" class="">Rules</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpunit.html" class="">PHPUnit</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpunit-runner.html" class="">Runner</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-textui.html" class="">TextUI</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-event.html" class="">Event</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-framework.html" class="">Framework</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-metadata.html" class="">Metadata</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-util.html" class="">Util</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-architecture.html" class="">Architecture</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/psr.html" class="">Psr</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/psr-clock.html" class="">Clock</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-container.html" class="">Container</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-eventdispatcher.html" class="">EventDispatcher</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-http.html" class="">Http</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-log.html" class="">Log</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-simplecache.html" class="">SimpleCache</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/ramsey.html" class="">Ramsey</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/ramsey-collection.html" class="">Collection</a>
                
            </li>
                    <li>
                <a href="namespaces/ramsey-uuid.html" class="">Uuid</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rectorprefix202507.html" class="">RectorPrefix202507</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/rectorprefix202507-clue.html" class="">Clue</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-composer.html" class="">Composer</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-doctrine.html" class="">Doctrine</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-evenement.html" class="">Evenement</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-fidry.html" class="">Fidry</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-illuminate.html" class="">Illuminate</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-nette.html" class="">Nette</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-ondram.html" class="">OndraM</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-psr.html" class="">Psr</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-react.html" class="">React</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-symfony.html" class="">Symfony</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-sebastianbergmann.html" class="">SebastianBergmann</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-symplify.html" class="">Symplify</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-webmozart.html" class="">Webmozart</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rectorprefix20220529.html" class="">RectorPrefix20220529</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rector.html" class="">Rector</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/rector-arguments.html" class="">Arguments</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-carbon.html" class="">Carbon</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-codequality.html" class="">CodeQuality</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-codingstyle.html" class="">CodingStyle</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-deadcode.html" class="">DeadCode</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-earlyreturn.html" class="">EarlyReturn</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-instanceof.html" class="">Instanceof_</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-naming.html" class="">Naming</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-netteutils.html" class="">NetteUtils</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php52.html" class="">Php52</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php53.html" class="">Php53</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php54.html" class="">Php54</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php55.html" class="">Php55</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php56.html" class="">Php56</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php70.html" class="">Php70</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php71.html" class="">Php71</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php72.html" class="">Php72</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php73.html" class="">Php73</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php74.html" class="">Php74</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php80.html" class="">Php80</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php81.html" class="">Php81</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php82.html" class="">Php82</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php83.html" class="">Php83</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php84.html" class="">Php84</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php85.html" class="">Php85</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-privatization.html" class="">Privatization</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-removing.html" class="">Removing</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-renaming.html" class="">Renaming</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-strict.html" class="">Strict</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-transform.html" class="">Transform</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-typedeclaration.html" class="">TypeDeclaration</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-visibility.html" class="">Visibility</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-application.html" class="">Application</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-autoloading.html" class="">Autoloading</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-betterphpdocparser.html" class="">BetterPhpDocParser</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-bootstrap.html" class="">Bootstrap</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-bridge.html" class="">Bridge</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-caching.html" class="">Caching</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-changesreporting.html" class="">ChangesReporting</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-comments.html" class="">Comments</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-composer.html" class="">Composer</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-config.html" class="">Config</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-configuration.html" class="">Configuration</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-console.html" class="">Console</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-contract.html" class="">Contract</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-customrules.html" class="">CustomRules</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-dependencyinjection.html" class="">DependencyInjection</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-differ.html" class="">Differ</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-enum.html" class="">Enum</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-familytree.html" class="">FamilyTree</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-filesystem.html" class="">FileSystem</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-git.html" class="">Git</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodeanalyzer.html" class="">NodeAnalyzer</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodecollector.html" class="">NodeCollector</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodedecorator.html" class="">NodeDecorator</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodemanipulator.html" class="">NodeManipulator</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodenameresolver.html" class="">NodeNameResolver</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodenestingscope.html" class="">NodeNestingScope</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodetyperesolver.html" class="">NodeTypeResolver</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-parallel.html" class="">Parallel</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php.html" class="">Php</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpattribute.html" class="">PhpAttribute</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpdocparser.html" class="">PhpDocParser</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpparser.html" class="">PhpParser</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpstan.html" class="">PHPStan</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpstanstatictypemapper.html" class="">PHPStanStaticTypeMapper</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-postrector.html" class="">PostRector</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-processanalyzer.html" class="">ProcessAnalyzer</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-rector.html" class="">Rector</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-reflection.html" class="">Reflection</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-reporting.html" class="">Reporting</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-set.html" class="">Set</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-skipper.html" class="">Skipper</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-staticreflection.html" class="">StaticReflection</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-statictypemapper.html" class="">StaticTypeMapper</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-testing.html" class="">Testing</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-util.html" class="">Util</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-validation.html" class="">Validation</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-valueobject.html" class="">ValueObject</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-vendorlocker.html" class="">VendorLocker</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-versionbonding.html" class="">VersionBonding</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-tests.html" class="">Tests</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-rectorinstaller.html" class="">RectorInstaller</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-doctrine.html" class="">Doctrine</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp72.html" class="">DowngradePhp72</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp73.html" class="">DowngradePhp73</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp74.html" class="">DowngradePhp74</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp80.html" class="">DowngradePhp80</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp81.html" class="">DowngradePhp81</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp82.html" class="">DowngradePhp82</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp83.html" class="">DowngradePhp83</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp84.html" class="">DowngradePhp84</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp85.html" class="">DowngradePhp85</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodefactory.html" class="">NodeFactory</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpdocdecorator.html" class="">PhpDocDecorator</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpunit.html" class="">PHPUnit</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-symfony.html" class="">Symfony</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-typeperfect.html" class="">TypePerfect</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/utils.html" class="">Utils</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/utils-rector.html" class="">Rector</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/symfony.html" class="">Symfony</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/symfony-component.html" class="">Component</a>
                
            </li>
                    <li>
                <a href="namespaces/symfony-contracts.html" class="">Contracts</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/symplify.html" class="">Symplify</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/symplify-ruledocgenerator.html" class="">RuleDocGenerator</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/sebastianbergmann.html" class="">SebastianBergmann</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/sebastianbergmann-codecoverage.html" class="">CodeCoverage</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-fileiterator.html" class="">FileIterator</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-invoker.html" class="">Invoker</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-template.html" class="">Template</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-timer.html" class="">Timer</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-cliparser.html" class="">CliParser</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-codeunit.html" class="">CodeUnit</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-codeunitreverselookup.html" class="">CodeUnitReverseLookup</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-comparator.html" class="">Comparator</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-complexity.html" class="">Complexity</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-diff.html" class="">Diff</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-environment.html" class="">Environment</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-exporter.html" class="">Exporter</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-globalstate.html" class="">GlobalState</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-linesofcode.html" class="">LinesOfCode</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-objectenumerator.html" class="">ObjectEnumerator</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-objectreflector.html" class="">ObjectReflector</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-recursioncontext.html" class="">RecursionContext</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-type.html" class="">Type</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/staabm.html" class="">staabm</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/staabm-sideeffectsdetector.html" class="">SideEffectsDetector</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/theseer.html" class="">TheSeer</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/theseer-tokenizer.html" class="">Tokenizer</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/tomasvotruba.html" class="">TomasVotruba</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/tomasvotruba-typecoverage.html" class="">TypeCoverage</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/dotenv.html" class="">Dotenv</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/dotenv-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-loader.html" class="">Loader</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-parser.html" class="">Parser</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-repository.html" class="">Repository</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-store.html" class="">Store</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/voku.html" class="">voku</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/voku-helper.html" class="">helper</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/webmozart.html" class="">Webmozart</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/webmozart-assert.html" class="">Assert</a>
                
            </li>
            </ul>

                        </section>
                <section class="phpdocumentor-sidebar__category -packages">
            <h2 class="phpdocumentor-sidebar__category-header">Packages</h2>
                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="packages/Application.html" class="">Application</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="packages/JsonSchema.html" class="">JsonSchema</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="packages/JsonSchema-Entity.html" class="">Entity</a>
                
            </li>
                    <li>
                <a href="packages/JsonSchema-Exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="packages/JsonSchema-Iterator.html" class="">Iterator</a>
                
            </li>
            </ul>

                        </section>
            
    <section class="phpdocumentor-sidebar__category -reports">
        <h2 class="phpdocumentor-sidebar__category-header">Reports</h2>
                <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/deprecated.html">Deprecated</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/errors.html">Errors</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/markers.html">Markers</a></h3>
    </section>

    <section class="phpdocumentor-sidebar__category -indices">
        <h2 class="phpdocumentor-sidebar__category-header">Indices</h2>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="indices/files.html">Files</a></h3>
    </section>
</aside>

            <div class="phpdocumentor-column -nine phpdocumentor-content">
                                <section>
                                        <ul class="phpdocumentor-breadcrumbs">
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/symfony.html">Symfony</a></li>
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/symfony-component.html">Component</a></li>
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/symfony-component-string.html">String</a></li>
    </ul>

    <article class="phpdocumentor-element -class">
        <h2 class="phpdocumentor-content__title">
    AbstractUnicodeString

        <span class="phpdocumentor-element__extends">
        extends <a href="classes/Symfony-Component-String-AbstractString.html"><abbr title="\Symfony\Component\String\AbstractString">AbstractString</abbr></a>
    </span>
    
            <div class="phpdocumentor-element__package">
            in package
            <ul class="phpdocumentor-breadcrumbs">
                                    <li class="phpdocumentor-breadcrumb"><a href="packages/Application.html">Application</a></li>
                            </ul>
        </div>
    
    
    </h2>

<div class="phpdocumentor-label-line">


    <div class="phpdocumentor-label phpdocumentor-label--success"><span>Abstract</span><span>Yes</span></div>

</div>

        <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">29</span>

    </aside>

            <p class="phpdocumentor-summary">Represents a string of abstract Unicode characters.</p>


    <section class="phpdocumentor-description"><p>Unicode defines 3 types of &quot;characters&quot; (bytes, code points and grapheme clusters).
This class is the abstract type to use as a type-hint when the logic you want to
implement is Unicode-aware but doesn't care about code points vs grapheme clusters.</p>
</section>


    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#tags" class="headerlink"><i class="fas fa-link"></i></a>

    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">author</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>Nicolas Grekas <a href="mailto:<EMAIL>"><EMAIL></a></p>
</section>

                                    </dd>
                                                <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">throws</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                <span class="phpdocumentor-tag-link"><a href="classes/Symfony-Component-String-Exception-ExceptionInterface.html"><abbr title="\Symfony\Component\String\Exception\ExceptionInterface">ExceptionInterface</abbr></a></span>
                                                            
                                             
                                    </dd>
                                            </dl>





<h3 id="toc">
    Table of Contents
    <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#toc" class="headerlink"><i class="fas fa-link"></i></a>

</h3>







<h4 id="toc-constants">
    Constants
    <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#toc-constants" class="headerlink"><i class="fas fa-link"></i></a>

</h4>
<dl class="phpdocumentor-table-of-contents">
            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFC">NFC</a>
    <span>
        &nbsp;= \Normalizer::NFC                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFD">NFD</a>
    <span>
        &nbsp;= \Normalizer::NFD                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFKC">NFKC</a>
    <span>
        &nbsp;= \Normalizer::NFKC                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFKD">NFKD</a>
    <span>
        &nbsp;= \Normalizer::NFKD                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_OFFSET_CAPTURE">PREG_OFFSET_CAPTURE</a>
    <span>
        &nbsp;= \PREG_OFFSET_CAPTURE                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_PATTERN_ORDER">PREG_PATTERN_ORDER</a>
    <span>
        &nbsp;= \PREG_PATTERN_ORDER                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SET_ORDER">PREG_SET_ORDER</a>
    <span>
        &nbsp;= \PREG_SET_ORDER                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT">PREG_SPLIT</a>
    <span>
        &nbsp;= 0                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT_DELIM_CAPTURE">PREG_SPLIT_DELIM_CAPTURE</a>
    <span>
        &nbsp;= \PREG_SPLIT_DELIM_CAPTURE                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT_NO_EMPTY">PREG_SPLIT_NO_EMPTY</a>
    <span>
        &nbsp;= \PREG_SPLIT_NO_EMPTY                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT_OFFSET_CAPTURE">PREG_SPLIT_OFFSET_CAPTURE</a>
    <span>
        &nbsp;= \PREG_SPLIT_OFFSET_CAPTURE                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_UNMATCHED_AS_NULL">PREG_UNMATCHED_AS_NULL</a>
    <span>
        &nbsp;= \PREG_UNMATCHED_AS_NULL                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_ASCII">ASCII</a>
    <span>
        &nbsp;= &quot; eiasntrolud][cmp&#039;\ng|hv.fb,:=-q10C2*yx)(L9AS/P\&quot;EjMIk3&gt;5T&lt;D4}B{8FwR67UGN;JzV#HOW_&amp;!K?XQ%Y\\\tZ+~^\$@`\x00\x01\x02\x03\x04\x05\x06\x07\x08\v\f\r\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\x1a\x1b\x1c\x1d\x1e\x1f&quot;                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_FOLD_FROM">FOLD_FROM</a>
    <span>
        &nbsp;= [&#039;İ&#039;, &#039;µ&#039;, &#039;ſ&#039;, &quot;ͅ&quot;, &#039;ς&#039;, &#039;ϐ&#039;, &#039;ϑ&#039;, &#039;ϕ&#039;, &#039;ϖ&#039;, &#039;ϰ&#039;, &#039;ϱ&#039;, &#039;ϵ&#039;, &#039;ẛ&#039;, &quot;ι&quot;, &#039;ß&#039;, &#039;ŉ&#039;, &#039;ǰ&#039;, &#039;ΐ&#039;, &#039;ΰ&#039;, &#039;և&#039;, &#039;ẖ&#039;, &#039;ẗ&#039;, &#039;ẘ&#039;, &#039;ẙ&#039;, &#039;ẚ&#039;, &#039;ẞ&#039;, &#039;ὐ&#039;, &#039;ὒ&#039;, &#039;ὔ&#039;, &#039;ὖ&#039;, &#039;ᾀ&#039;, &#039;ᾁ&#039;, &#039;ᾂ&#039;, &#039;ᾃ&#039;, &#039;ᾄ&#039;, &#039;ᾅ&#039;, &#039;ᾆ&#039;, &#039;ᾇ&#039;, &#039;ᾈ&#039;, &#039;ᾉ&#039;, &#039;ᾊ&#039;, &#039;ᾋ&#039;, &#039;ᾌ&#039;, &#039;ᾍ&#039;, &#039;ᾎ&#039;, &#039;ᾏ&#039;, &#039;ᾐ&#039;, &#039;ᾑ&#039;, &#039;ᾒ&#039;, &#039;ᾓ&#039;, &#039;ᾔ&#039;, &#039;ᾕ&#039;, &#039;ᾖ&#039;, &#039;ᾗ&#039;, &#039;ᾘ&#039;, &#039;ᾙ&#039;, &#039;ᾚ&#039;, &#039;ᾛ&#039;, &#039;ᾜ&#039;, &#039;ᾝ&#039;, &#039;ᾞ&#039;, &#039;ᾟ&#039;, &#039;ᾠ&#039;, &#039;ᾡ&#039;, &#039;ᾢ&#039;, &#039;ᾣ&#039;, &#039;ᾤ&#039;, &#039;ᾥ&#039;, &#039;ᾦ&#039;, &#039;ᾧ&#039;, &#039;ᾨ&#039;, &#039;ᾩ&#039;, &#039;ᾪ&#039;, &#039;ᾫ&#039;, &#039;ᾬ&#039;, &#039;ᾭ&#039;, &#039;ᾮ&#039;, &#039;ᾯ&#039;, &#039;ᾲ&#039;, &#039;ᾳ&#039;, &#039;ᾴ&#039;, &#039;ᾶ&#039;, &#039;ᾷ&#039;, &#039;ᾼ&#039;, &#039;ῂ&#039;, &#039;ῃ&#039;, &#039;ῄ&#039;, &#039;ῆ&#039;, &#039;ῇ&#039;, &#039;ῌ&#039;, &#039;ῒ&#039;, &#039;ῖ&#039;, &#039;ῗ&#039;, &#039;ῢ&#039;, &#039;ῤ&#039;, &#039;ῦ&#039;, &#039;ῧ&#039;, &#039;ῲ&#039;, &#039;ῳ&#039;, &#039;ῴ&#039;, &#039;ῶ&#039;, &#039;ῷ&#039;, &#039;ῼ&#039;, &#039;ﬀ&#039;, &#039;ﬁ&#039;, &#039;ﬂ&#039;, &#039;ﬃ&#039;, &#039;ﬄ&#039;, &#039;ﬅ&#039;, &#039;ﬆ&#039;, &#039;ﬓ&#039;, &#039;ﬔ&#039;, &#039;ﬕ&#039;, &#039;ﬖ&#039;, &#039;ﬗ&#039;]                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_FOLD_TO">FOLD_TO</a>
    <span>
        &nbsp;= [&#039;i̇&#039;, &#039;μ&#039;, &#039;s&#039;, &#039;ι&#039;, &#039;σ&#039;, &#039;β&#039;, &#039;θ&#039;, &#039;φ&#039;, &#039;π&#039;, &#039;κ&#039;, &#039;ρ&#039;, &#039;ε&#039;, &#039;ṡ&#039;, &#039;ι&#039;, &#039;ss&#039;, &#039;ʼn&#039;, &#039;ǰ&#039;, &#039;ΐ&#039;, &#039;ΰ&#039;, &#039;եւ&#039;, &#039;ẖ&#039;, &#039;ẗ&#039;, &#039;ẘ&#039;, &#039;ẙ&#039;, &#039;aʾ&#039;, &#039;ss&#039;, &#039;ὐ&#039;, &#039;ὒ&#039;, &#039;ὔ&#039;, &#039;ὖ&#039;, &#039;ἀι&#039;, &#039;ἁι&#039;, &#039;ἂι&#039;, &#039;ἃι&#039;, &#039;ἄι&#039;, &#039;ἅι&#039;, &#039;ἆι&#039;, &#039;ἇι&#039;, &#039;ἀι&#039;, &#039;ἁι&#039;, &#039;ἂι&#039;, &#039;ἃι&#039;, &#039;ἄι&#039;, &#039;ἅι&#039;, &#039;ἆι&#039;, &#039;ἇι&#039;, &#039;ἠι&#039;, &#039;ἡι&#039;, &#039;ἢι&#039;, &#039;ἣι&#039;, &#039;ἤι&#039;, &#039;ἥι&#039;, &#039;ἦι&#039;, &#039;ἧι&#039;, &#039;ἠι&#039;, &#039;ἡι&#039;, &#039;ἢι&#039;, &#039;ἣι&#039;, &#039;ἤι&#039;, &#039;ἥι&#039;, &#039;ἦι&#039;, &#039;ἧι&#039;, &#039;ὠι&#039;, &#039;ὡι&#039;, &#039;ὢι&#039;, &#039;ὣι&#039;, &#039;ὤι&#039;, &#039;ὥι&#039;, &#039;ὦι&#039;, &#039;ὧι&#039;, &#039;ὠι&#039;, &#039;ὡι&#039;, &#039;ὢι&#039;, &#039;ὣι&#039;, &#039;ὤι&#039;, &#039;ὥι&#039;, &#039;ὦι&#039;, &#039;ὧι&#039;, &#039;ὰι&#039;, &#039;αι&#039;, &#039;άι&#039;, &#039;ᾶ&#039;, &#039;ᾶι&#039;, &#039;αι&#039;, &#039;ὴι&#039;, &#039;ηι&#039;, &#039;ήι&#039;, &#039;ῆ&#039;, &#039;ῆι&#039;, &#039;ηι&#039;, &#039;ῒ&#039;, &#039;ῖ&#039;, &#039;ῗ&#039;, &#039;ῢ&#039;, &#039;ῤ&#039;, &#039;ῦ&#039;, &#039;ῧ&#039;, &#039;ὼι&#039;, &#039;ωι&#039;, &#039;ώι&#039;, &#039;ῶ&#039;, &#039;ῶι&#039;, &#039;ωι&#039;, &#039;ff&#039;, &#039;fi&#039;, &#039;fl&#039;, &#039;ffi&#039;, &#039;ffl&#039;, &#039;st&#039;, &#039;st&#039;, &#039;մն&#039;, &#039;մե&#039;, &#039;մի&#039;, &#039;վն&#039;, &#039;մխ&#039;]                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_TRANSLIT_FROM">TRANSLIT_FROM</a>
    <span>
        &nbsp;= [&#039;Æ&#039;, &#039;Ð&#039;, &#039;Ø&#039;, &#039;Þ&#039;, &#039;ß&#039;, &#039;æ&#039;, &#039;ð&#039;, &#039;ø&#039;, &#039;þ&#039;, &#039;Đ&#039;, &#039;đ&#039;, &#039;Ħ&#039;, &#039;ħ&#039;, &#039;ı&#039;, &#039;ĸ&#039;, &#039;Ŀ&#039;, &#039;ŀ&#039;, &#039;Ł&#039;, &#039;ł&#039;, &#039;ŉ&#039;, &#039;Ŋ&#039;, &#039;ŋ&#039;, &#039;Œ&#039;, &#039;œ&#039;, &#039;Ŧ&#039;, &#039;ŧ&#039;, &#039;ƀ&#039;, &#039;Ɓ&#039;, &#039;Ƃ&#039;, &#039;ƃ&#039;, &#039;Ƈ&#039;, &#039;ƈ&#039;, &#039;Ɖ&#039;, &#039;Ɗ&#039;, &#039;Ƌ&#039;, &#039;ƌ&#039;, &#039;Ɛ&#039;, &#039;Ƒ&#039;, &#039;ƒ&#039;, &#039;Ɠ&#039;, &#039;ƕ&#039;, &#039;Ɩ&#039;, &#039;Ɨ&#039;, &#039;Ƙ&#039;, &#039;ƙ&#039;, &#039;ƚ&#039;, &#039;Ɲ&#039;, &#039;ƞ&#039;, &#039;Ƣ&#039;, &#039;ƣ&#039;, &#039;Ƥ&#039;, &#039;ƥ&#039;, &#039;ƫ&#039;, &#039;Ƭ&#039;, &#039;ƭ&#039;, &#039;Ʈ&#039;, &#039;Ʋ&#039;, &#039;Ƴ&#039;, &#039;ƴ&#039;, &#039;Ƶ&#039;, &#039;ƶ&#039;, &#039;Ǆ&#039;, &#039;ǅ&#039;, &#039;ǆ&#039;, &#039;Ǥ&#039;, &#039;ǥ&#039;, &#039;ȡ&#039;, &#039;Ȥ&#039;, &#039;ȥ&#039;, &#039;ȴ&#039;, &#039;ȵ&#039;, &#039;ȶ&#039;, &#039;ȷ&#039;, &#039;ȸ&#039;, &#039;ȹ&#039;, &#039;Ⱥ&#039;, &#039;Ȼ&#039;, &#039;ȼ&#039;, &#039;Ƚ&#039;, &#039;Ⱦ&#039;, &#039;ȿ&#039;, &#039;ɀ&#039;, &#039;Ƀ&#039;, &#039;Ʉ&#039;, &#039;Ɇ&#039;, &#039;ɇ&#039;, &#039;Ɉ&#039;, &#039;ɉ&#039;, &#039;Ɍ&#039;, &#039;ɍ&#039;, &#039;Ɏ&#039;, &#039;ɏ&#039;, &#039;ɓ&#039;, &#039;ɕ&#039;, &#039;ɖ&#039;, &#039;ɗ&#039;, &#039;ɛ&#039;, &#039;ɟ&#039;, &#039;ɠ&#039;, &#039;ɡ&#039;, &#039;ɢ&#039;, &#039;ɦ&#039;, &#039;ɧ&#039;, &#039;ɨ&#039;, &#039;ɪ&#039;, &#039;ɫ&#039;, &#039;ɬ&#039;, &#039;ɭ&#039;, &#039;ɱ&#039;, &#039;ɲ&#039;, &#039;ɳ&#039;, &#039;ɴ&#039;, &#039;ɶ&#039;, &#039;ɼ&#039;, &#039;ɽ&#039;, &#039;ɾ&#039;, &#039;ʀ&#039;, &#039;ʂ&#039;, &#039;ʈ&#039;, &#039;ʉ&#039;, &#039;ʋ&#039;, &#039;ʏ&#039;, &#039;ʐ&#039;, &#039;ʑ&#039;, &#039;ʙ&#039;, &#039;ʛ&#039;, &#039;ʜ&#039;, &#039;ʝ&#039;, &#039;ʟ&#039;, &#039;ʠ&#039;, &#039;ʣ&#039;, &#039;ʥ&#039;, &#039;ʦ&#039;, &#039;ʪ&#039;, &#039;ʫ&#039;, &#039;ᴀ&#039;, &#039;ᴁ&#039;, &#039;ᴃ&#039;, &#039;ᴄ&#039;, &#039;ᴅ&#039;, &#039;ᴆ&#039;, &#039;ᴇ&#039;, &#039;ᴊ&#039;, &#039;ᴋ&#039;, &#039;ᴌ&#039;, &#039;ᴍ&#039;, &#039;ᴏ&#039;, &#039;ᴘ&#039;, &#039;ᴛ&#039;, &#039;ᴜ&#039;, &#039;ᴠ&#039;, &#039;ᴡ&#039;, &#039;ᴢ&#039;, &#039;ᵫ&#039;, &#039;ᵬ&#039;, &#039;ᵭ&#039;, &#039;ᵮ&#039;, &#039;ᵯ&#039;, &#039;ᵰ&#039;, &#039;ᵱ&#039;, &#039;ᵲ&#039;, &#039;ᵳ&#039;, &#039;ᵴ&#039;, &#039;ᵵ&#039;, &#039;ᵶ&#039;, &#039;ᵺ&#039;, &#039;ᵻ&#039;, &#039;ᵽ&#039;, &#039;ᵾ&#039;, &#039;ᶀ&#039;, &#039;ᶁ&#039;, &#039;ᶂ&#039;, &#039;ᶃ&#039;, &#039;ᶄ&#039;, &#039;ᶅ&#039;, &#039;ᶆ&#039;, &#039;ᶇ&#039;, &#039;ᶈ&#039;, &#039;ᶉ&#039;, &#039;ᶊ&#039;, &#039;ᶌ&#039;, &#039;ᶍ&#039;, &#039;ᶎ&#039;, &#039;ᶏ&#039;, &#039;ᶑ&#039;, &#039;ᶒ&#039;, &#039;ᶓ&#039;, &#039;ᶖ&#039;, &#039;ᶙ&#039;, &#039;ẚ&#039;, &#039;ẜ&#039;, &#039;ẝ&#039;, &#039;ẞ&#039;, &#039;Ỻ&#039;, &#039;ỻ&#039;, &#039;Ỽ&#039;, &#039;ỽ&#039;, &#039;Ỿ&#039;, &#039;ỿ&#039;, &#039;©&#039;, &#039;®&#039;, &#039;₠&#039;, &#039;₢&#039;, &#039;₣&#039;, &#039;₤&#039;, &#039;₧&#039;, &#039;₺&#039;, &#039;₹&#039;, &#039;ℌ&#039;, &#039;℞&#039;, &#039;㎧&#039;, &#039;㎮&#039;, &#039;㏆&#039;, &#039;㏗&#039;, &#039;㏞&#039;, &#039;㏟&#039;, &#039;¼&#039;, &#039;½&#039;, &#039;¾&#039;, &#039;⅓&#039;, &#039;⅔&#039;, &#039;⅕&#039;, &#039;⅖&#039;, &#039;⅗&#039;, &#039;⅘&#039;, &#039;⅙&#039;, &#039;⅚&#039;, &#039;⅛&#039;, &#039;⅜&#039;, &#039;⅝&#039;, &#039;⅞&#039;, &#039;⅟&#039;, &#039;〇&#039;, &#039;‘&#039;, &#039;’&#039;, &#039;‚&#039;, &#039;‛&#039;, &#039;“&#039;, &#039;”&#039;, &#039;„&#039;, &#039;‟&#039;, &#039;′&#039;, &#039;″&#039;, &#039;〝&#039;, &#039;〞&#039;, &#039;«&#039;, &#039;»&#039;, &#039;‹&#039;, &#039;›&#039;, &#039;‐&#039;, &#039;‑&#039;, &#039;‒&#039;, &#039;–&#039;, &#039;—&#039;, &#039;―&#039;, &#039;︱&#039;, &#039;︲&#039;, &#039;﹘&#039;, &#039;‖&#039;, &#039;⁄&#039;, &#039;⁅&#039;, &#039;⁆&#039;, &#039;⁎&#039;, &#039;、&#039;, &#039;。&#039;, &#039;〈&#039;, &#039;〉&#039;, &#039;《&#039;, &#039;》&#039;, &#039;〔&#039;, &#039;〕&#039;, &#039;〘&#039;, &#039;〙&#039;, &#039;〚&#039;, &#039;〛&#039;, &#039;︑&#039;, &#039;︒&#039;, &#039;︹&#039;, &#039;︺&#039;, &#039;︽&#039;, &#039;︾&#039;, &#039;︿&#039;, &#039;﹀&#039;, &#039;﹑&#039;, &#039;﹝&#039;, &#039;﹞&#039;, &#039;｟&#039;, &#039;｠&#039;, &#039;｡&#039;, &#039;､&#039;, &#039;×&#039;, &#039;÷&#039;, &#039;−&#039;, &#039;∕&#039;, &#039;∖&#039;, &#039;∣&#039;, &#039;∥&#039;, &#039;≪&#039;, &#039;≫&#039;, &#039;⦅&#039;, &#039;⦆&#039;]                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_TRANSLIT_TO">TRANSLIT_TO</a>
    <span>
        &nbsp;= [&#039;AE&#039;, &#039;D&#039;, &#039;O&#039;, &#039;TH&#039;, &#039;ss&#039;, &#039;ae&#039;, &#039;d&#039;, &#039;o&#039;, &#039;th&#039;, &#039;D&#039;, &#039;d&#039;, &#039;H&#039;, &#039;h&#039;, &#039;i&#039;, &#039;q&#039;, &#039;L&#039;, &#039;l&#039;, &#039;L&#039;, &#039;l&#039;, &#039;\&#039;n&#039;, &#039;N&#039;, &#039;n&#039;, &#039;OE&#039;, &#039;oe&#039;, &#039;T&#039;, &#039;t&#039;, &#039;b&#039;, &#039;B&#039;, &#039;B&#039;, &#039;b&#039;, &#039;C&#039;, &#039;c&#039;, &#039;D&#039;, &#039;D&#039;, &#039;D&#039;, &#039;d&#039;, &#039;E&#039;, &#039;F&#039;, &#039;f&#039;, &#039;G&#039;, &#039;hv&#039;, &#039;I&#039;, &#039;I&#039;, &#039;K&#039;, &#039;k&#039;, &#039;l&#039;, &#039;N&#039;, &#039;n&#039;, &#039;OI&#039;, &#039;oi&#039;, &#039;P&#039;, &#039;p&#039;, &#039;t&#039;, &#039;T&#039;, &#039;t&#039;, &#039;T&#039;, &#039;V&#039;, &#039;Y&#039;, &#039;y&#039;, &#039;Z&#039;, &#039;z&#039;, &#039;DZ&#039;, &#039;Dz&#039;, &#039;dz&#039;, &#039;G&#039;, &#039;g&#039;, &#039;d&#039;, &#039;Z&#039;, &#039;z&#039;, &#039;l&#039;, &#039;n&#039;, &#039;t&#039;, &#039;j&#039;, &#039;db&#039;, &#039;qp&#039;, &#039;A&#039;, &#039;C&#039;, &#039;c&#039;, &#039;L&#039;, &#039;T&#039;, &#039;s&#039;, &#039;z&#039;, &#039;B&#039;, &#039;U&#039;, &#039;E&#039;, &#039;e&#039;, &#039;J&#039;, &#039;j&#039;, &#039;R&#039;, &#039;r&#039;, &#039;Y&#039;, &#039;y&#039;, &#039;b&#039;, &#039;c&#039;, &#039;d&#039;, &#039;d&#039;, &#039;e&#039;, &#039;j&#039;, &#039;g&#039;, &#039;g&#039;, &#039;G&#039;, &#039;h&#039;, &#039;h&#039;, &#039;i&#039;, &#039;I&#039;, &#039;l&#039;, &#039;l&#039;, &#039;l&#039;, &#039;m&#039;, &#039;n&#039;, &#039;n&#039;, &#039;N&#039;, &#039;OE&#039;, &#039;r&#039;, &#039;r&#039;, &#039;r&#039;, &#039;R&#039;, &#039;s&#039;, &#039;t&#039;, &#039;u&#039;, &#039;v&#039;, &#039;Y&#039;, &#039;z&#039;, &#039;z&#039;, &#039;B&#039;, &#039;G&#039;, &#039;H&#039;, &#039;j&#039;, &#039;L&#039;, &#039;q&#039;, &#039;dz&#039;, &#039;dz&#039;, &#039;ts&#039;, &#039;ls&#039;, &#039;lz&#039;, &#039;A&#039;, &#039;AE&#039;, &#039;B&#039;, &#039;C&#039;, &#039;D&#039;, &#039;D&#039;, &#039;E&#039;, &#039;J&#039;, &#039;K&#039;, &#039;L&#039;, &#039;M&#039;, &#039;O&#039;, &#039;P&#039;, &#039;T&#039;, &#039;U&#039;, &#039;V&#039;, &#039;W&#039;, &#039;Z&#039;, &#039;ue&#039;, &#039;b&#039;, &#039;d&#039;, &#039;f&#039;, &#039;m&#039;, &#039;n&#039;, &#039;p&#039;, &#039;r&#039;, &#039;r&#039;, &#039;s&#039;, &#039;t&#039;, &#039;z&#039;, &#039;th&#039;, &#039;I&#039;, &#039;p&#039;, &#039;U&#039;, &#039;b&#039;, &#039;d&#039;, &#039;f&#039;, &#039;g&#039;, &#039;k&#039;, &#039;l&#039;, &#039;m&#039;, &#039;n&#039;, &#039;p&#039;, &#039;r&#039;, &#039;s&#039;, &#039;v&#039;, &#039;x&#039;, &#039;z&#039;, &#039;a&#039;, &#039;d&#039;, &#039;e&#039;, &#039;e&#039;, &#039;i&#039;, &#039;u&#039;, &#039;a&#039;, &#039;s&#039;, &#039;s&#039;, &#039;SS&#039;, &#039;LL&#039;, &#039;ll&#039;, &#039;V&#039;, &#039;v&#039;, &#039;Y&#039;, &#039;y&#039;, &#039;(C)&#039;, &#039;(R)&#039;, &#039;CE&#039;, &#039;Cr&#039;, &#039;Fr.&#039;, &#039;L.&#039;, &#039;Pts&#039;, &#039;TL&#039;, &#039;Rs&#039;, &#039;x&#039;, &#039;Rx&#039;, &#039;m/s&#039;, &#039;rad/s&#039;, &#039;C/kg&#039;, &#039;pH&#039;, &#039;V/m&#039;, &#039;A/m&#039;, &#039; 1/4&#039;, &#039; 1/2&#039;, &#039; 3/4&#039;, &#039; 1/3&#039;, &#039; 2/3&#039;, &#039; 1/5&#039;, &#039; 2/5&#039;, &#039; 3/5&#039;, &#039; 4/5&#039;, &#039; 1/6&#039;, &#039; 5/6&#039;, &#039; 1/8&#039;, &#039; 3/8&#039;, &#039; 5/8&#039;, &#039; 7/8&#039;, &#039; 1/&#039;, &#039;0&#039;, &#039;\&#039;&#039;, &#039;\&#039;&#039;, &#039;,&#039;, &#039;\&#039;&#039;, &#039;&quot;&#039;, &#039;&quot;&#039;, &#039;,,&#039;, &#039;&quot;&#039;, &#039;\&#039;&#039;, &#039;&quot;&#039;, &#039;&quot;&#039;, &#039;&quot;&#039;, &#039;&lt;&lt;&#039;, &#039;&gt;&gt;&#039;, &#039;&lt;&#039;, &#039;&gt;&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;||&#039;, &#039;/&#039;, &#039;[&#039;, &#039;]&#039;, &#039;*&#039;, &#039;,&#039;, &#039;.&#039;, &#039;&lt;&#039;, &#039;&gt;&#039;, &#039;&lt;&lt;&#039;, &#039;&gt;&gt;&#039;, &#039;[&#039;, &#039;]&#039;, &#039;[&#039;, &#039;]&#039;, &#039;[&#039;, &#039;]&#039;, &#039;,&#039;, &#039;.&#039;, &#039;[&#039;, &#039;]&#039;, &#039;&lt;&lt;&#039;, &#039;&gt;&gt;&#039;, &#039;&lt;&#039;, &#039;&gt;&#039;, &#039;,&#039;, &#039;[&#039;, &#039;]&#039;, &#039;((&#039;, &#039;))&#039;, &#039;.&#039;, &#039;,&#039;, &#039;*&#039;, &#039;/&#039;, &#039;-&#039;, &#039;/&#039;, &#039;\\&#039;, &#039;|&#039;, &#039;||&#039;, &#039;&lt;&lt;&#039;, &#039;&gt;&gt;&#039;, &#039;((&#039;, &#039;))&#039;]                            </span>
</dt>

    </dl>


<h4 id="toc-properties">
    Properties
    <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#toc-properties" class="headerlink"><i class="fas fa-link"></i></a>

</h4>
<dl class="phpdocumentor-table-of-contents">
            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#property_ignoreCase">$ignoreCase</a>
    <span>
                        &nbsp;: bool|null            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#property_string">$string</a>
    <span>
                        &nbsp;: string            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -property -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#property_tableWide">$tableWide</a>
    <span>
                        &nbsp;: array&lt;string|int, mixed&gt;            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -property -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#property_tableZero">$tableZero</a>
    <span>
                        &nbsp;: array&lt;string|int, mixed&gt;            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -property -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#property_transliterators">$transliterators</a>
    <span>
                        &nbsp;: array&lt;string|int, mixed&gt;            </span>
</dt>

    </dl>

<h4 id="toc-methods">
    Methods
    <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#toc-methods" class="headerlink"><i class="fas fa-link"></i></a>

</h4>
<dl class="phpdocumentor-table-of-contents">
            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method___clone">__clone()</a>
    <span>
                                &nbsp;: mixed    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method___construct">__construct()</a>
    <span>
                                &nbsp;: mixed    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method___sleep">__sleep()</a>
    <span>
                                &nbsp;: array&lt;string|int, mixed&gt;    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method___toString">__toString()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_after">after()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_afterLast">afterLast()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_append">append()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_ascii">ascii()</a>
    <span>
                                &nbsp;: self    </span>
</dt>
<dd>Generic UTF-8 to ASCII transliteration.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_before">before()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_beforeLast">beforeLast()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_bytesAt">bytesAt()</a>
    <span>
                                &nbsp;: array&lt;string|int, int&gt;    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_camel">camel()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_chunk">chunk()</a>
    <span>
                                &nbsp;: array&lt;string|int, static&gt;    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_codePointsAt">codePointsAt()</a>
    <span>
                                &nbsp;: array&lt;string|int, int&gt;    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_collapseWhitespace">collapseWhitespace()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_containsAny">containsAny()</a>
    <span>
                                &nbsp;: bool    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_endsWith">endsWith()</a>
    <span>
                                &nbsp;: bool    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_ensureEnd">ensureEnd()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_ensureStart">ensureStart()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_equalsTo">equalsTo()</a>
    <span>
                                &nbsp;: bool    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_folded">folded()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_fromCodePoints">fromCodePoints()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_ignoreCase">ignoreCase()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_indexOf">indexOf()</a>
    <span>
                                &nbsp;: int|null    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_indexOfLast">indexOfLast()</a>
    <span>
                                &nbsp;: int|null    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_isEmpty">isEmpty()</a>
    <span>
                                &nbsp;: bool    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_join">join()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_jsonSerialize">jsonSerialize()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_kebab">kebab()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_length">length()</a>
    <span>
                                &nbsp;: int    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_localeLower">localeLower()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_localeTitle">localeTitle()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_localeUpper">localeUpper()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_lower">lower()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_match">match()</a>
    <span>
                                &nbsp;: array&lt;string|int, mixed&gt;    </span>
</dt>
<dd>Matches the string using a regular expression.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_normalize">normalize()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_padBoth">padBoth()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_padEnd">padEnd()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_padStart">padStart()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_pascal">pascal()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_prepend">prepend()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_repeat">repeat()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_replace">replace()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_replaceMatches">replaceMatches()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_reverse">reverse()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_slice">slice()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_snake">snake()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_splice">splice()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_split">split()</a>
    <span>
                                &nbsp;: array&lt;string|int, static&gt;    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_startsWith">startsWith()</a>
    <span>
                                &nbsp;: bool    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_title">title()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_toByteString">toByteString()</a>
    <span>
                                &nbsp;: <a href="classes/Symfony-Component-String-ByteString.html"><abbr title="\Symfony\Component\String\ByteString">ByteString</abbr></a>    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_toCodePointString">toCodePointString()</a>
    <span>
                                &nbsp;: <a href="classes/Symfony-Component-String-CodePointString.html"><abbr title="\Symfony\Component\String\CodePointString">CodePointString</abbr></a>    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_toString">toString()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_toUnicodeString">toUnicodeString()</a>
    <span>
                                &nbsp;: <a href="classes/Symfony-Component-String-UnicodeString.html"><abbr title="\Symfony\Component\String\UnicodeString">UnicodeString</abbr></a>    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trim">trim()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimEnd">trimEnd()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimPrefix">trimPrefix()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimStart">trimStart()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimSuffix">trimSuffix()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_truncate">truncate()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_unwrap">unwrap()</a>
    <span>
                                &nbsp;: array&lt;string|int, string&gt;|array&lt;string|int, mixed&gt;    </span>
</dt>
<dd>Unwraps instances of AbstractString back to strings.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_upper">upper()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_width">width()</a>
    <span>
                                &nbsp;: int    </span>
</dt>
<dd>Returns the printable length on a terminal.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_wordwrap">wordwrap()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/Symfony-Component-String-AbstractString.html#method_wrap">wrap()</a>
    <span>
                                &nbsp;: array&lt;string|int, static&gt;|array&lt;string|int, mixed&gt;    </span>
</dt>
<dd>Wraps (and normalizes) strings in instances of AbstractString.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_getLocaleTransliterator">getLocaleTransliterator()</a>
    <span>
                                &nbsp;: <abbr title="\Transliterator">Transliterator</abbr>|null    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_pad">pad()</a>
    <span>
                                &nbsp;: static    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -private">
    <a class="" href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_wcswidth">wcswidth()</a>
    <span>
                                &nbsp;: int    </span>
</dt>
<dd>Based on https://github.com/jquast/wcwidth, a Python implementation of https://www.cl.cam.ac.uk/~mgk25/ucs/wcwidth.c.</dd>

    </dl>



        
    <section class="phpdocumentor-constants">
        <h3 class="phpdocumentor-elements__header" id="constants">
            Constants
            <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constants" class="headerlink"><i class="fas fa-link"></i></a>

        </h3>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_NFC">
        NFC
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFC" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">31</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">NFC</span>
    = <span class="phpdocumentor-signature__default-value">\Normalizer::NFC</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_NFD">
        NFD
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFD" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">32</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">NFD</span>
    = <span class="phpdocumentor-signature__default-value">\Normalizer::NFD</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_NFKC">
        NFKC
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFKC" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">33</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">NFKC</span>
    = <span class="phpdocumentor-signature__default-value">\Normalizer::NFKC</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_NFKD">
        NFKD
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFKD" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">34</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">NFKD</span>
    = <span class="phpdocumentor-signature__default-value">\Normalizer::NFKD</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_PREG_OFFSET_CAPTURE">
        PREG_OFFSET_CAPTURE
        <a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_OFFSET_CAPTURE" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">34</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">PREG_OFFSET_CAPTURE</span>
    = <span class="phpdocumentor-signature__default-value">\PREG_OFFSET_CAPTURE</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_PREG_PATTERN_ORDER">
        PREG_PATTERN_ORDER
        <a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_PATTERN_ORDER" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">32</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">PREG_PATTERN_ORDER</span>
    = <span class="phpdocumentor-signature__default-value">\PREG_PATTERN_ORDER</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_PREG_SET_ORDER">
        PREG_SET_ORDER
        <a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SET_ORDER" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">33</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">PREG_SET_ORDER</span>
    = <span class="phpdocumentor-signature__default-value">\PREG_SET_ORDER</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_PREG_SPLIT">
        PREG_SPLIT
        <a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">37</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">PREG_SPLIT</span>
    = <span class="phpdocumentor-signature__default-value">0</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_PREG_SPLIT_DELIM_CAPTURE">
        PREG_SPLIT_DELIM_CAPTURE
        <a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT_DELIM_CAPTURE" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">39</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">PREG_SPLIT_DELIM_CAPTURE</span>
    = <span class="phpdocumentor-signature__default-value">\PREG_SPLIT_DELIM_CAPTURE</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_PREG_SPLIT_NO_EMPTY">
        PREG_SPLIT_NO_EMPTY
        <a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT_NO_EMPTY" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">38</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">PREG_SPLIT_NO_EMPTY</span>
    = <span class="phpdocumentor-signature__default-value">\PREG_SPLIT_NO_EMPTY</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_PREG_SPLIT_OFFSET_CAPTURE">
        PREG_SPLIT_OFFSET_CAPTURE
        <a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT_OFFSET_CAPTURE" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">40</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">PREG_SPLIT_OFFSET_CAPTURE</span>
    = <span class="phpdocumentor-signature__default-value">\PREG_SPLIT_OFFSET_CAPTURE</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_PREG_UNMATCHED_AS_NULL">
        PREG_UNMATCHED_AS_NULL
        <a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_UNMATCHED_AS_NULL" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">35</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">PREG_UNMATCHED_AS_NULL</span>
    = <span class="phpdocumentor-signature__default-value">\PREG_UNMATCHED_AS_NULL</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -private ">
    <h4 class="phpdocumentor-element__name" id="constant_ASCII">
        ASCII
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_ASCII" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">37</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">private</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">ASCII</span>
    = <span class="phpdocumentor-signature__default-value">&quot; eiasntrolud][cmp&#039;\ng|hv.fb,:=-q10C2*yx)(L9AS/P\&quot;EjMIk3&gt;5T&lt;D4}B{8FwR67UGN;JzV#HOW_&amp;!K?XQ%Y\\\tZ+~^\$@`\x00\x01\x02\x03\x04\x05\x06\x07\x08\v\f\r\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\x1a\x1b\x1c\x1d\x1e\x1f&quot;</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -private ">
    <h4 class="phpdocumentor-element__name" id="constant_FOLD_FROM">
        FOLD_FROM
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_FOLD_FROM" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">40</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">private</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">FOLD_FROM</span>
    = <span class="phpdocumentor-signature__default-value">[&#039;İ&#039;, &#039;µ&#039;, &#039;ſ&#039;, &quot;ͅ&quot;, &#039;ς&#039;, &#039;ϐ&#039;, &#039;ϑ&#039;, &#039;ϕ&#039;, &#039;ϖ&#039;, &#039;ϰ&#039;, &#039;ϱ&#039;, &#039;ϵ&#039;, &#039;ẛ&#039;, &quot;ι&quot;, &#039;ß&#039;, &#039;ŉ&#039;, &#039;ǰ&#039;, &#039;ΐ&#039;, &#039;ΰ&#039;, &#039;և&#039;, &#039;ẖ&#039;, &#039;ẗ&#039;, &#039;ẘ&#039;, &#039;ẙ&#039;, &#039;ẚ&#039;, &#039;ẞ&#039;, &#039;ὐ&#039;, &#039;ὒ&#039;, &#039;ὔ&#039;, &#039;ὖ&#039;, &#039;ᾀ&#039;, &#039;ᾁ&#039;, &#039;ᾂ&#039;, &#039;ᾃ&#039;, &#039;ᾄ&#039;, &#039;ᾅ&#039;, &#039;ᾆ&#039;, &#039;ᾇ&#039;, &#039;ᾈ&#039;, &#039;ᾉ&#039;, &#039;ᾊ&#039;, &#039;ᾋ&#039;, &#039;ᾌ&#039;, &#039;ᾍ&#039;, &#039;ᾎ&#039;, &#039;ᾏ&#039;, &#039;ᾐ&#039;, &#039;ᾑ&#039;, &#039;ᾒ&#039;, &#039;ᾓ&#039;, &#039;ᾔ&#039;, &#039;ᾕ&#039;, &#039;ᾖ&#039;, &#039;ᾗ&#039;, &#039;ᾘ&#039;, &#039;ᾙ&#039;, &#039;ᾚ&#039;, &#039;ᾛ&#039;, &#039;ᾜ&#039;, &#039;ᾝ&#039;, &#039;ᾞ&#039;, &#039;ᾟ&#039;, &#039;ᾠ&#039;, &#039;ᾡ&#039;, &#039;ᾢ&#039;, &#039;ᾣ&#039;, &#039;ᾤ&#039;, &#039;ᾥ&#039;, &#039;ᾦ&#039;, &#039;ᾧ&#039;, &#039;ᾨ&#039;, &#039;ᾩ&#039;, &#039;ᾪ&#039;, &#039;ᾫ&#039;, &#039;ᾬ&#039;, &#039;ᾭ&#039;, &#039;ᾮ&#039;, &#039;ᾯ&#039;, &#039;ᾲ&#039;, &#039;ᾳ&#039;, &#039;ᾴ&#039;, &#039;ᾶ&#039;, &#039;ᾷ&#039;, &#039;ᾼ&#039;, &#039;ῂ&#039;, &#039;ῃ&#039;, &#039;ῄ&#039;, &#039;ῆ&#039;, &#039;ῇ&#039;, &#039;ῌ&#039;, &#039;ῒ&#039;, &#039;ῖ&#039;, &#039;ῗ&#039;, &#039;ῢ&#039;, &#039;ῤ&#039;, &#039;ῦ&#039;, &#039;ῧ&#039;, &#039;ῲ&#039;, &#039;ῳ&#039;, &#039;ῴ&#039;, &#039;ῶ&#039;, &#039;ῷ&#039;, &#039;ῼ&#039;, &#039;ﬀ&#039;, &#039;ﬁ&#039;, &#039;ﬂ&#039;, &#039;ﬃ&#039;, &#039;ﬄ&#039;, &#039;ﬅ&#039;, &#039;ﬆ&#039;, &#039;ﬓ&#039;, &#039;ﬔ&#039;, &#039;ﬕ&#039;, &#039;ﬖ&#039;, &#039;ﬗ&#039;]</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -private ">
    <h4 class="phpdocumentor-element__name" id="constant_FOLD_TO">
        FOLD_TO
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_FOLD_TO" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">41</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">private</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">FOLD_TO</span>
    = <span class="phpdocumentor-signature__default-value">[&#039;i̇&#039;, &#039;μ&#039;, &#039;s&#039;, &#039;ι&#039;, &#039;σ&#039;, &#039;β&#039;, &#039;θ&#039;, &#039;φ&#039;, &#039;π&#039;, &#039;κ&#039;, &#039;ρ&#039;, &#039;ε&#039;, &#039;ṡ&#039;, &#039;ι&#039;, &#039;ss&#039;, &#039;ʼn&#039;, &#039;ǰ&#039;, &#039;ΐ&#039;, &#039;ΰ&#039;, &#039;եւ&#039;, &#039;ẖ&#039;, &#039;ẗ&#039;, &#039;ẘ&#039;, &#039;ẙ&#039;, &#039;aʾ&#039;, &#039;ss&#039;, &#039;ὐ&#039;, &#039;ὒ&#039;, &#039;ὔ&#039;, &#039;ὖ&#039;, &#039;ἀι&#039;, &#039;ἁι&#039;, &#039;ἂι&#039;, &#039;ἃι&#039;, &#039;ἄι&#039;, &#039;ἅι&#039;, &#039;ἆι&#039;, &#039;ἇι&#039;, &#039;ἀι&#039;, &#039;ἁι&#039;, &#039;ἂι&#039;, &#039;ἃι&#039;, &#039;ἄι&#039;, &#039;ἅι&#039;, &#039;ἆι&#039;, &#039;ἇι&#039;, &#039;ἠι&#039;, &#039;ἡι&#039;, &#039;ἢι&#039;, &#039;ἣι&#039;, &#039;ἤι&#039;, &#039;ἥι&#039;, &#039;ἦι&#039;, &#039;ἧι&#039;, &#039;ἠι&#039;, &#039;ἡι&#039;, &#039;ἢι&#039;, &#039;ἣι&#039;, &#039;ἤι&#039;, &#039;ἥι&#039;, &#039;ἦι&#039;, &#039;ἧι&#039;, &#039;ὠι&#039;, &#039;ὡι&#039;, &#039;ὢι&#039;, &#039;ὣι&#039;, &#039;ὤι&#039;, &#039;ὥι&#039;, &#039;ὦι&#039;, &#039;ὧι&#039;, &#039;ὠι&#039;, &#039;ὡι&#039;, &#039;ὢι&#039;, &#039;ὣι&#039;, &#039;ὤι&#039;, &#039;ὥι&#039;, &#039;ὦι&#039;, &#039;ὧι&#039;, &#039;ὰι&#039;, &#039;αι&#039;, &#039;άι&#039;, &#039;ᾶ&#039;, &#039;ᾶι&#039;, &#039;αι&#039;, &#039;ὴι&#039;, &#039;ηι&#039;, &#039;ήι&#039;, &#039;ῆ&#039;, &#039;ῆι&#039;, &#039;ηι&#039;, &#039;ῒ&#039;, &#039;ῖ&#039;, &#039;ῗ&#039;, &#039;ῢ&#039;, &#039;ῤ&#039;, &#039;ῦ&#039;, &#039;ῧ&#039;, &#039;ὼι&#039;, &#039;ωι&#039;, &#039;ώι&#039;, &#039;ῶ&#039;, &#039;ῶι&#039;, &#039;ωι&#039;, &#039;ff&#039;, &#039;fi&#039;, &#039;fl&#039;, &#039;ffi&#039;, &#039;ffl&#039;, &#039;st&#039;, &#039;st&#039;, &#039;մն&#039;, &#039;մե&#039;, &#039;մի&#039;, &#039;վն&#039;, &#039;մխ&#039;]</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -private ">
    <h4 class="phpdocumentor-element__name" id="constant_TRANSLIT_FROM">
        TRANSLIT_FROM
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_TRANSLIT_FROM" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">44</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">private</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">TRANSLIT_FROM</span>
    = <span class="phpdocumentor-signature__default-value">[&#039;Æ&#039;, &#039;Ð&#039;, &#039;Ø&#039;, &#039;Þ&#039;, &#039;ß&#039;, &#039;æ&#039;, &#039;ð&#039;, &#039;ø&#039;, &#039;þ&#039;, &#039;Đ&#039;, &#039;đ&#039;, &#039;Ħ&#039;, &#039;ħ&#039;, &#039;ı&#039;, &#039;ĸ&#039;, &#039;Ŀ&#039;, &#039;ŀ&#039;, &#039;Ł&#039;, &#039;ł&#039;, &#039;ŉ&#039;, &#039;Ŋ&#039;, &#039;ŋ&#039;, &#039;Œ&#039;, &#039;œ&#039;, &#039;Ŧ&#039;, &#039;ŧ&#039;, &#039;ƀ&#039;, &#039;Ɓ&#039;, &#039;Ƃ&#039;, &#039;ƃ&#039;, &#039;Ƈ&#039;, &#039;ƈ&#039;, &#039;Ɖ&#039;, &#039;Ɗ&#039;, &#039;Ƌ&#039;, &#039;ƌ&#039;, &#039;Ɛ&#039;, &#039;Ƒ&#039;, &#039;ƒ&#039;, &#039;Ɠ&#039;, &#039;ƕ&#039;, &#039;Ɩ&#039;, &#039;Ɨ&#039;, &#039;Ƙ&#039;, &#039;ƙ&#039;, &#039;ƚ&#039;, &#039;Ɲ&#039;, &#039;ƞ&#039;, &#039;Ƣ&#039;, &#039;ƣ&#039;, &#039;Ƥ&#039;, &#039;ƥ&#039;, &#039;ƫ&#039;, &#039;Ƭ&#039;, &#039;ƭ&#039;, &#039;Ʈ&#039;, &#039;Ʋ&#039;, &#039;Ƴ&#039;, &#039;ƴ&#039;, &#039;Ƶ&#039;, &#039;ƶ&#039;, &#039;Ǆ&#039;, &#039;ǅ&#039;, &#039;ǆ&#039;, &#039;Ǥ&#039;, &#039;ǥ&#039;, &#039;ȡ&#039;, &#039;Ȥ&#039;, &#039;ȥ&#039;, &#039;ȴ&#039;, &#039;ȵ&#039;, &#039;ȶ&#039;, &#039;ȷ&#039;, &#039;ȸ&#039;, &#039;ȹ&#039;, &#039;Ⱥ&#039;, &#039;Ȼ&#039;, &#039;ȼ&#039;, &#039;Ƚ&#039;, &#039;Ⱦ&#039;, &#039;ȿ&#039;, &#039;ɀ&#039;, &#039;Ƀ&#039;, &#039;Ʉ&#039;, &#039;Ɇ&#039;, &#039;ɇ&#039;, &#039;Ɉ&#039;, &#039;ɉ&#039;, &#039;Ɍ&#039;, &#039;ɍ&#039;, &#039;Ɏ&#039;, &#039;ɏ&#039;, &#039;ɓ&#039;, &#039;ɕ&#039;, &#039;ɖ&#039;, &#039;ɗ&#039;, &#039;ɛ&#039;, &#039;ɟ&#039;, &#039;ɠ&#039;, &#039;ɡ&#039;, &#039;ɢ&#039;, &#039;ɦ&#039;, &#039;ɧ&#039;, &#039;ɨ&#039;, &#039;ɪ&#039;, &#039;ɫ&#039;, &#039;ɬ&#039;, &#039;ɭ&#039;, &#039;ɱ&#039;, &#039;ɲ&#039;, &#039;ɳ&#039;, &#039;ɴ&#039;, &#039;ɶ&#039;, &#039;ɼ&#039;, &#039;ɽ&#039;, &#039;ɾ&#039;, &#039;ʀ&#039;, &#039;ʂ&#039;, &#039;ʈ&#039;, &#039;ʉ&#039;, &#039;ʋ&#039;, &#039;ʏ&#039;, &#039;ʐ&#039;, &#039;ʑ&#039;, &#039;ʙ&#039;, &#039;ʛ&#039;, &#039;ʜ&#039;, &#039;ʝ&#039;, &#039;ʟ&#039;, &#039;ʠ&#039;, &#039;ʣ&#039;, &#039;ʥ&#039;, &#039;ʦ&#039;, &#039;ʪ&#039;, &#039;ʫ&#039;, &#039;ᴀ&#039;, &#039;ᴁ&#039;, &#039;ᴃ&#039;, &#039;ᴄ&#039;, &#039;ᴅ&#039;, &#039;ᴆ&#039;, &#039;ᴇ&#039;, &#039;ᴊ&#039;, &#039;ᴋ&#039;, &#039;ᴌ&#039;, &#039;ᴍ&#039;, &#039;ᴏ&#039;, &#039;ᴘ&#039;, &#039;ᴛ&#039;, &#039;ᴜ&#039;, &#039;ᴠ&#039;, &#039;ᴡ&#039;, &#039;ᴢ&#039;, &#039;ᵫ&#039;, &#039;ᵬ&#039;, &#039;ᵭ&#039;, &#039;ᵮ&#039;, &#039;ᵯ&#039;, &#039;ᵰ&#039;, &#039;ᵱ&#039;, &#039;ᵲ&#039;, &#039;ᵳ&#039;, &#039;ᵴ&#039;, &#039;ᵵ&#039;, &#039;ᵶ&#039;, &#039;ᵺ&#039;, &#039;ᵻ&#039;, &#039;ᵽ&#039;, &#039;ᵾ&#039;, &#039;ᶀ&#039;, &#039;ᶁ&#039;, &#039;ᶂ&#039;, &#039;ᶃ&#039;, &#039;ᶄ&#039;, &#039;ᶅ&#039;, &#039;ᶆ&#039;, &#039;ᶇ&#039;, &#039;ᶈ&#039;, &#039;ᶉ&#039;, &#039;ᶊ&#039;, &#039;ᶌ&#039;, &#039;ᶍ&#039;, &#039;ᶎ&#039;, &#039;ᶏ&#039;, &#039;ᶑ&#039;, &#039;ᶒ&#039;, &#039;ᶓ&#039;, &#039;ᶖ&#039;, &#039;ᶙ&#039;, &#039;ẚ&#039;, &#039;ẜ&#039;, &#039;ẝ&#039;, &#039;ẞ&#039;, &#039;Ỻ&#039;, &#039;ỻ&#039;, &#039;Ỽ&#039;, &#039;ỽ&#039;, &#039;Ỿ&#039;, &#039;ỿ&#039;, &#039;©&#039;, &#039;®&#039;, &#039;₠&#039;, &#039;₢&#039;, &#039;₣&#039;, &#039;₤&#039;, &#039;₧&#039;, &#039;₺&#039;, &#039;₹&#039;, &#039;ℌ&#039;, &#039;℞&#039;, &#039;㎧&#039;, &#039;㎮&#039;, &#039;㏆&#039;, &#039;㏗&#039;, &#039;㏞&#039;, &#039;㏟&#039;, &#039;¼&#039;, &#039;½&#039;, &#039;¾&#039;, &#039;⅓&#039;, &#039;⅔&#039;, &#039;⅕&#039;, &#039;⅖&#039;, &#039;⅗&#039;, &#039;⅘&#039;, &#039;⅙&#039;, &#039;⅚&#039;, &#039;⅛&#039;, &#039;⅜&#039;, &#039;⅝&#039;, &#039;⅞&#039;, &#039;⅟&#039;, &#039;〇&#039;, &#039;‘&#039;, &#039;’&#039;, &#039;‚&#039;, &#039;‛&#039;, &#039;“&#039;, &#039;”&#039;, &#039;„&#039;, &#039;‟&#039;, &#039;′&#039;, &#039;″&#039;, &#039;〝&#039;, &#039;〞&#039;, &#039;«&#039;, &#039;»&#039;, &#039;‹&#039;, &#039;›&#039;, &#039;‐&#039;, &#039;‑&#039;, &#039;‒&#039;, &#039;–&#039;, &#039;—&#039;, &#039;―&#039;, &#039;︱&#039;, &#039;︲&#039;, &#039;﹘&#039;, &#039;‖&#039;, &#039;⁄&#039;, &#039;⁅&#039;, &#039;⁆&#039;, &#039;⁎&#039;, &#039;、&#039;, &#039;。&#039;, &#039;〈&#039;, &#039;〉&#039;, &#039;《&#039;, &#039;》&#039;, &#039;〔&#039;, &#039;〕&#039;, &#039;〘&#039;, &#039;〙&#039;, &#039;〚&#039;, &#039;〛&#039;, &#039;︑&#039;, &#039;︒&#039;, &#039;︹&#039;, &#039;︺&#039;, &#039;︽&#039;, &#039;︾&#039;, &#039;︿&#039;, &#039;﹀&#039;, &#039;﹑&#039;, &#039;﹝&#039;, &#039;﹞&#039;, &#039;｟&#039;, &#039;｠&#039;, &#039;｡&#039;, &#039;､&#039;, &#039;×&#039;, &#039;÷&#039;, &#039;−&#039;, &#039;∕&#039;, &#039;∖&#039;, &#039;∣&#039;, &#039;∥&#039;, &#039;≪&#039;, &#039;≫&#039;, &#039;⦅&#039;, &#039;⦆&#039;]</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -private ">
    <h4 class="phpdocumentor-element__name" id="constant_TRANSLIT_TO">
        TRANSLIT_TO
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_TRANSLIT_TO" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">45</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">private</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">TRANSLIT_TO</span>
    = <span class="phpdocumentor-signature__default-value">[&#039;AE&#039;, &#039;D&#039;, &#039;O&#039;, &#039;TH&#039;, &#039;ss&#039;, &#039;ae&#039;, &#039;d&#039;, &#039;o&#039;, &#039;th&#039;, &#039;D&#039;, &#039;d&#039;, &#039;H&#039;, &#039;h&#039;, &#039;i&#039;, &#039;q&#039;, &#039;L&#039;, &#039;l&#039;, &#039;L&#039;, &#039;l&#039;, &#039;\&#039;n&#039;, &#039;N&#039;, &#039;n&#039;, &#039;OE&#039;, &#039;oe&#039;, &#039;T&#039;, &#039;t&#039;, &#039;b&#039;, &#039;B&#039;, &#039;B&#039;, &#039;b&#039;, &#039;C&#039;, &#039;c&#039;, &#039;D&#039;, &#039;D&#039;, &#039;D&#039;, &#039;d&#039;, &#039;E&#039;, &#039;F&#039;, &#039;f&#039;, &#039;G&#039;, &#039;hv&#039;, &#039;I&#039;, &#039;I&#039;, &#039;K&#039;, &#039;k&#039;, &#039;l&#039;, &#039;N&#039;, &#039;n&#039;, &#039;OI&#039;, &#039;oi&#039;, &#039;P&#039;, &#039;p&#039;, &#039;t&#039;, &#039;T&#039;, &#039;t&#039;, &#039;T&#039;, &#039;V&#039;, &#039;Y&#039;, &#039;y&#039;, &#039;Z&#039;, &#039;z&#039;, &#039;DZ&#039;, &#039;Dz&#039;, &#039;dz&#039;, &#039;G&#039;, &#039;g&#039;, &#039;d&#039;, &#039;Z&#039;, &#039;z&#039;, &#039;l&#039;, &#039;n&#039;, &#039;t&#039;, &#039;j&#039;, &#039;db&#039;, &#039;qp&#039;, &#039;A&#039;, &#039;C&#039;, &#039;c&#039;, &#039;L&#039;, &#039;T&#039;, &#039;s&#039;, &#039;z&#039;, &#039;B&#039;, &#039;U&#039;, &#039;E&#039;, &#039;e&#039;, &#039;J&#039;, &#039;j&#039;, &#039;R&#039;, &#039;r&#039;, &#039;Y&#039;, &#039;y&#039;, &#039;b&#039;, &#039;c&#039;, &#039;d&#039;, &#039;d&#039;, &#039;e&#039;, &#039;j&#039;, &#039;g&#039;, &#039;g&#039;, &#039;G&#039;, &#039;h&#039;, &#039;h&#039;, &#039;i&#039;, &#039;I&#039;, &#039;l&#039;, &#039;l&#039;, &#039;l&#039;, &#039;m&#039;, &#039;n&#039;, &#039;n&#039;, &#039;N&#039;, &#039;OE&#039;, &#039;r&#039;, &#039;r&#039;, &#039;r&#039;, &#039;R&#039;, &#039;s&#039;, &#039;t&#039;, &#039;u&#039;, &#039;v&#039;, &#039;Y&#039;, &#039;z&#039;, &#039;z&#039;, &#039;B&#039;, &#039;G&#039;, &#039;H&#039;, &#039;j&#039;, &#039;L&#039;, &#039;q&#039;, &#039;dz&#039;, &#039;dz&#039;, &#039;ts&#039;, &#039;ls&#039;, &#039;lz&#039;, &#039;A&#039;, &#039;AE&#039;, &#039;B&#039;, &#039;C&#039;, &#039;D&#039;, &#039;D&#039;, &#039;E&#039;, &#039;J&#039;, &#039;K&#039;, &#039;L&#039;, &#039;M&#039;, &#039;O&#039;, &#039;P&#039;, &#039;T&#039;, &#039;U&#039;, &#039;V&#039;, &#039;W&#039;, &#039;Z&#039;, &#039;ue&#039;, &#039;b&#039;, &#039;d&#039;, &#039;f&#039;, &#039;m&#039;, &#039;n&#039;, &#039;p&#039;, &#039;r&#039;, &#039;r&#039;, &#039;s&#039;, &#039;t&#039;, &#039;z&#039;, &#039;th&#039;, &#039;I&#039;, &#039;p&#039;, &#039;U&#039;, &#039;b&#039;, &#039;d&#039;, &#039;f&#039;, &#039;g&#039;, &#039;k&#039;, &#039;l&#039;, &#039;m&#039;, &#039;n&#039;, &#039;p&#039;, &#039;r&#039;, &#039;s&#039;, &#039;v&#039;, &#039;x&#039;, &#039;z&#039;, &#039;a&#039;, &#039;d&#039;, &#039;e&#039;, &#039;e&#039;, &#039;i&#039;, &#039;u&#039;, &#039;a&#039;, &#039;s&#039;, &#039;s&#039;, &#039;SS&#039;, &#039;LL&#039;, &#039;ll&#039;, &#039;V&#039;, &#039;v&#039;, &#039;Y&#039;, &#039;y&#039;, &#039;(C)&#039;, &#039;(R)&#039;, &#039;CE&#039;, &#039;Cr&#039;, &#039;Fr.&#039;, &#039;L.&#039;, &#039;Pts&#039;, &#039;TL&#039;, &#039;Rs&#039;, &#039;x&#039;, &#039;Rx&#039;, &#039;m/s&#039;, &#039;rad/s&#039;, &#039;C/kg&#039;, &#039;pH&#039;, &#039;V/m&#039;, &#039;A/m&#039;, &#039; 1/4&#039;, &#039; 1/2&#039;, &#039; 3/4&#039;, &#039; 1/3&#039;, &#039; 2/3&#039;, &#039; 1/5&#039;, &#039; 2/5&#039;, &#039; 3/5&#039;, &#039; 4/5&#039;, &#039; 1/6&#039;, &#039; 5/6&#039;, &#039; 1/8&#039;, &#039; 3/8&#039;, &#039; 5/8&#039;, &#039; 7/8&#039;, &#039; 1/&#039;, &#039;0&#039;, &#039;\&#039;&#039;, &#039;\&#039;&#039;, &#039;,&#039;, &#039;\&#039;&#039;, &#039;&quot;&#039;, &#039;&quot;&#039;, &#039;,,&#039;, &#039;&quot;&#039;, &#039;\&#039;&#039;, &#039;&quot;&#039;, &#039;&quot;&#039;, &#039;&quot;&#039;, &#039;&lt;&lt;&#039;, &#039;&gt;&gt;&#039;, &#039;&lt;&#039;, &#039;&gt;&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;-&#039;, &#039;||&#039;, &#039;/&#039;, &#039;[&#039;, &#039;]&#039;, &#039;*&#039;, &#039;,&#039;, &#039;.&#039;, &#039;&lt;&#039;, &#039;&gt;&#039;, &#039;&lt;&lt;&#039;, &#039;&gt;&gt;&#039;, &#039;[&#039;, &#039;]&#039;, &#039;[&#039;, &#039;]&#039;, &#039;[&#039;, &#039;]&#039;, &#039;,&#039;, &#039;.&#039;, &#039;[&#039;, &#039;]&#039;, &#039;&lt;&lt;&#039;, &#039;&gt;&gt;&#039;, &#039;&lt;&#039;, &#039;&gt;&#039;, &#039;,&#039;, &#039;[&#039;, &#039;]&#039;, &#039;((&#039;, &#039;))&#039;, &#039;.&#039;, &#039;,&#039;, &#039;*&#039;, &#039;/&#039;, &#039;-&#039;, &#039;/&#039;, &#039;\\&#039;, &#039;|&#039;, &#039;||&#039;, &#039;&lt;&lt;&#039;, &#039;&gt;&gt;&#039;, &#039;((&#039;, &#039;))&#039;]</span>
</code>


    
    
    

    

</article>
            </section>

        
    <section class="phpdocumentor-properties">
        <h3 class="phpdocumentor-elements__header" id="properties">
            Properties
            <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#properties" class="headerlink"><i class="fas fa-link"></i></a>

        </h3>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                                    "
>
    <h4 class="phpdocumentor-element__name" id="property_ignoreCase">
        $ignoreCase
        <a href="classes/Symfony-Component-String-AbstractString.html#property_ignoreCase" class="headerlink"><i class="fas fa-link"></i></a>

        <span class="phpdocumentor-element__modifiers">
                                            </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">43</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
        <span class="phpdocumentor-signature__visibility">protected</span>
            <span class="phpdocumentor-signature__type">bool|null</span>
    <span class="phpdocumentor-signature__name">$ignoreCase</span>
     = <span class="phpdocumentor-signature__default-value">false</span></code>

    
    
    

    

    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                                    "
>
    <h4 class="phpdocumentor-element__name" id="property_string">
        $string
        <a href="classes/Symfony-Component-String-AbstractString.html#property_string" class="headerlink"><i class="fas fa-link"></i></a>

        <span class="phpdocumentor-element__modifiers">
                                            </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">42</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
        <span class="phpdocumentor-signature__visibility">protected</span>
            <span class="phpdocumentor-signature__type">string</span>
    <span class="phpdocumentor-signature__name">$string</span>
     = <span class="phpdocumentor-signature__default-value">&#039;&#039;</span></code>

    
    
    

    

    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -private
            -static                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_tableWide">
        $tableWide
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#property_tableWide" class="headerlink"><i class="fas fa-link"></i></a>

        <span class="phpdocumentor-element__modifiers">
                                            </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">49</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
        <span class="phpdocumentor-signature__visibility">private</span>
        <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__type">array&lt;string|int, mixed&gt;</span>
    <span class="phpdocumentor-signature__name">$tableWide</span>
    </code>

    
    
    

    

    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -private
            -static                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_tableZero">
        $tableZero
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#property_tableZero" class="headerlink"><i class="fas fa-link"></i></a>

        <span class="phpdocumentor-element__modifiers">
                                            </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">48</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
        <span class="phpdocumentor-signature__visibility">private</span>
        <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__type">array&lt;string|int, mixed&gt;</span>
    <span class="phpdocumentor-signature__name">$tableZero</span>
    </code>

    
    
    

    

    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -private
            -static                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_transliterators">
        $transliterators
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#property_transliterators" class="headerlink"><i class="fas fa-link"></i></a>

        <span class="phpdocumentor-element__modifiers">
                                            </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">47</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
        <span class="phpdocumentor-signature__visibility">private</span>
        <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__type">array&lt;string|int, mixed&gt;</span>
    <span class="phpdocumentor-signature__name">$transliterators</span>
     = <span class="phpdocumentor-signature__default-value">[]</span></code>

    
    
    

    

    

</article>
            </section>

            <section class="phpdocumentor-methods">
        <h3 class="phpdocumentor-elements__header" id="methods">
            Methods
            <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#methods" class="headerlink"><i class="fas fa-link"></i></a>

        </h3>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method___clone">
        __clone()
        <a href="classes/Symfony-Component-String-AbstractString.html#method___clone" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">714</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">__clone</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                        -abstract                                "
>
    <h4 class="phpdocumentor-element__name" id="method___construct">
        __construct()
        <a href="classes/Symfony-Component-String-AbstractString.html#method___construct" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">45</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
    <span class="phpdocumentor-signature__abstract">abstract</span>                <span class="phpdocumentor-signature__name">__construct</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&#039;&#039;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&#039;&#039;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method___sleep">
        __sleep()
        <a href="classes/Symfony-Component-String-AbstractString.html#method___sleep" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">709</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">__sleep</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method___toString">
        __toString()
        <a href="classes/Symfony-Component-String-AbstractString.html#method___toString" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">719</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">__toString</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_after">
        after()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_after" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">96</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">after</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool&nbsp;</span><span class="phpdocumentor-signature__argument__name">$includeNeedle</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$includeNeedle</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_afterLast">
        afterLast()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_afterLast" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">129</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">afterLast</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool&nbsp;</span><span class="phpdocumentor-signature__argument__name">$includeNeedle</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$includeNeedle</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                        -abstract                                "
>
    <h4 class="phpdocumentor-element__name" id="method_append">
        append()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_append" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">159</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
    <span class="phpdocumentor-signature__abstract">abstract</span>                <span class="phpdocumentor-signature__name">append</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__variadic-operator">...</span><span class="phpdocumentor-signature__argument__name">$suffix</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$suffix</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_ascii">
        ascii()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_ascii" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">77</span>

    </aside>

        <p class="phpdocumentor-summary">Generic UTF-8 to ASCII transliteration.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">ascii</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, string&gt;|array&lt;string|int, <abbr title="\Transliterator">Transliterator</abbr>&gt;|array&lt;string|int, <abbr title="\Closure">Closure</abbr>&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$rules</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">self</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
        <section class="phpdocumentor-description"><p>Install the intl extension for best results.</p>
</section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$rules</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, string&gt;|array&lt;string|int, <abbr title="\Transliterator">Transliterator</abbr>&gt;|array&lt;string|int, <abbr title="\Closure">Closure</abbr>&gt;</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>See &quot;*-Latin&quot; rules from Transliterator::listIDs()</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">self</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_before">
        before()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_before" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">164</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">before</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool&nbsp;</span><span class="phpdocumentor-signature__argument__name">$includeNeedle</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$includeNeedle</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_beforeLast">
        beforeLast()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_beforeLast" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">197</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">beforeLast</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool&nbsp;</span><span class="phpdocumentor-signature__argument__name">$includeNeedle</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$includeNeedle</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_bytesAt">
        bytesAt()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_bytesAt" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">230</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">bytesAt</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, int&gt;</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, int&gt;</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_camel">
        camel()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_camel" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">161</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">camel</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                        -abstract                                "
>
    <h4 class="phpdocumentor-element__name" id="method_chunk">
        chunk()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_chunk" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">242</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
    <span class="phpdocumentor-signature__abstract">abstract</span>                <span class="phpdocumentor-signature__name">chunk</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">1</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, static&gt;</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">1</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, static&gt;</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_codePointsAt">
        codePointsAt()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_codePointsAt" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">176</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">codePointsAt</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, int&gt;</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, int&gt;</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_collapseWhitespace">
        collapseWhitespace()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_collapseWhitespace" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">244</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">collapseWhitespace</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_containsAny">
        containsAny()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_containsAny" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">255</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">containsAny</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_endsWith">
        endsWith()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_endsWith" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">263</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">endsWith</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$suffix</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$suffix</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_ensureEnd">
        ensureEnd()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_ensureEnd" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">278</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">ensureEnd</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$suffix</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$suffix</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_ensureStart">
        ensureStart()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_ensureStart" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">290</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">ensureStart</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$prefix</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$prefix</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_equalsTo">
        equalsTo()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_equalsTo" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">312</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">equalsTo</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_folded">
        folded()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_folded" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">193</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">folded</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">bool&nbsp;</span><span class="phpdocumentor-signature__argument__name">$compat</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">true</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$compat</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool</span>
                 = <span class="phpdocumentor-signature__argument__default-value">true</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_fromCodePoints">
        fromCodePoints()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_fromCodePoints" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">51</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>        <span class="phpdocumentor-signature__name">fromCodePoints</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__variadic-operator">...</span><span class="phpdocumentor-signature__argument__name">$codes</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$codes</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_ignoreCase">
        ignoreCase()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_ignoreCase" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">329</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">ignoreCase</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_indexOf">
        indexOf()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_indexOf" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">340</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">indexOf</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int|null</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int|null</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_indexOfLast">
        indexOfLast()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_indexOfLast" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">362</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">indexOfLast</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int|null</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int|null</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_isEmpty">
        isEmpty()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_isEmpty" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">381</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">isEmpty</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_join">
        join()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_join" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">207</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">join</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$strings</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$lastGlue</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$strings</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$lastGlue</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_jsonSerialize">
        jsonSerialize()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_jsonSerialize" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">388</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">jsonSerialize</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_kebab">
        kebab()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_kebab" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">436</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">kebab</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                        -abstract                                "
>
    <h4 class="phpdocumentor-element__name" id="method_length">
        length()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_length" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">393</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
    <span class="phpdocumentor-signature__abstract">abstract</span>                <span class="phpdocumentor-signature__name">length</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_localeLower">
        localeLower()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_localeLower" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">232</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">localeLower</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$locale</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$locale</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>In the format language_region (e.g. tr_TR)</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_localeTitle">
        localeTitle()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_localeTitle" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">390</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">localeTitle</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$locale</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$locale</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>In the format language_region (e.g. tr_TR)</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_localeUpper">
        localeUpper()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_localeUpper" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">492</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">localeUpper</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$locale</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$locale</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>In the format language_region (e.g. tr_TR)</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_lower">
        lower()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_lower" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">221</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">lower</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_match">
        match()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_match" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">244</span>

    </aside>

        <p class="phpdocumentor-summary">Matches the string using a regular expression.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">match</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$regexp</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$flags</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
        <section class="phpdocumentor-description"><p>Pass PREG_PATTERN_ORDER or PREG_SET_ORDER as $flags to get all occurrences matching the regular expression.</p>
</section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$regexp</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$flags</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span>
                    &mdash;
                <section class="phpdocumentor-description"><p>All matches in a multi-dimensional array ordered according to flags</p>
</section>

            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_normalize">
        normalize()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_normalize" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">265</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">normalize</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$form</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">self::NFC</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$form</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">self::NFC</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_padBoth">
        padBoth()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_padBoth" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">277</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">padBoth</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$padStr</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&#039; &#039;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$padStr</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&#039; &#039;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_padEnd">
        padEnd()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_padEnd" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">289</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">padEnd</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$padStr</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&#039; &#039;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$padStr</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&#039; &#039;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_padStart">
        padStart()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_padStart" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">301</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">padStart</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$padStr</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&#039; &#039;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$padStr</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&#039; &#039;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_pascal">
        pascal()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_pascal" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">441</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">pascal</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                        -abstract                                "
>
    <h4 class="phpdocumentor-element__name" id="method_prepend">
        prepend()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_prepend" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">412</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
    <span class="phpdocumentor-signature__abstract">abstract</span>                <span class="phpdocumentor-signature__name">prepend</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__variadic-operator">...</span><span class="phpdocumentor-signature__argument__name">$prefix</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$prefix</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_repeat">
        repeat()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_repeat" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">414</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">repeat</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$multiplier</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$multiplier</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                        -abstract                                "
>
    <h4 class="phpdocumentor-element__name" id="method_replace">
        replace()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_replace" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">426</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
    <span class="phpdocumentor-signature__abstract">abstract</span>                <span class="phpdocumentor-signature__name">replace</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$from</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$to</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$from</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$to</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_replaceMatches">
        replaceMatches()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_replaceMatches" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">313</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">replaceMatches</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$fromRegexp</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|callable&nbsp;</span><span class="phpdocumentor-signature__argument__name">$to</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$fromRegexp</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$to</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|callable</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_reverse">
        reverse()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_reverse" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">360</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">reverse</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                        -abstract                                "
>
    <h4 class="phpdocumentor-element__name" id="method_slice">
        slice()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_slice" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">432</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
    <span class="phpdocumentor-signature__abstract">abstract</span>                <span class="phpdocumentor-signature__name">slice</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$start</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$start</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_snake">
        snake()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_snake" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">368</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">snake</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                        -abstract                                "
>
    <h4 class="phpdocumentor-element__name" id="method_splice">
        splice()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_splice" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">446</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
    <span class="phpdocumentor-signature__abstract">abstract</span>                <span class="phpdocumentor-signature__name">splice</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$replacement</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$start</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$replacement</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$start</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_split">
        split()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_split" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">451</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">split</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$delimiter</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$limit</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$flags</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, static&gt;</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$delimiter</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$limit</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$flags</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, static&gt;</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_startsWith">
        startsWith()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_startsWith" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">491</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">startsWith</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$prefix</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$prefix</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|array&lt;string|int, string&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_title">
        title()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_title" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">376</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">title</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">bool&nbsp;</span><span class="phpdocumentor-signature__argument__name">$allWords</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$allWords</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_toByteString">
        toByteString()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_toByteString" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">508</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">toByteString</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$toEncoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type"><a href="classes/Symfony-Component-String-ByteString.html"><abbr title="\Symfony\Component\String\ByteString">ByteString</abbr></a></span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$toEncoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type"><a href="classes/Symfony-Component-String-ByteString.html"><abbr title="\Symfony\Component\String\ByteString">ByteString</abbr></a></span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_toCodePointString">
        toCodePointString()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_toCodePointString" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">533</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">toCodePointString</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type"><a href="classes/Symfony-Component-String-CodePointString.html"><abbr title="\Symfony\Component\String\CodePointString">CodePointString</abbr></a></span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type"><a href="classes/Symfony-Component-String-CodePointString.html"><abbr title="\Symfony\Component\String\CodePointString">CodePointString</abbr></a></span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_toString">
        toString()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_toString" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">538</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">toString</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_toUnicodeString">
        toUnicodeString()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_toUnicodeString" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">543</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">toUnicodeString</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type"><a href="classes/Symfony-Component-String-UnicodeString.html"><abbr title="\Symfony\Component\String\UnicodeString">UnicodeString</abbr></a></span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type"><a href="classes/Symfony-Component-String-UnicodeString.html"><abbr title="\Symfony\Component\String\UnicodeString">UnicodeString</abbr></a></span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_trim">
        trim()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trim" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">402</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">trim</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$chars</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&quot; 	

  ﻿&quot;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$chars</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&quot; 	

  ﻿&quot;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_trimEnd">
        trimEnd()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimEnd" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">415</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">trimEnd</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$chars</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&quot; 	

  ﻿&quot;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$chars</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&quot; 	

  ﻿&quot;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_trimPrefix">
        trimPrefix()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimPrefix" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">428</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">trimPrefix</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$prefix</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$prefix</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_trimStart">
        trimStart()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimStart" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">448</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">trimStart</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$chars</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&quot; 	

  ﻿&quot;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$chars</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&quot; 	

  ﻿&quot;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_trimSuffix">
        trimSuffix()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimSuffix" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">461</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">trimSuffix</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$suffix</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$suffix</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_truncate">
        truncate()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_truncate" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">618</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">truncate</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$ellipsis</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&#039;&#039;</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool|<a href="classes/Symfony-Component-String-TruncateMode.html"><abbr title="\Symfony\Component\String\TruncateMode">TruncateMode</abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$cut</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">TruncateMode::Char</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$ellipsis</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&#039;&#039;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$cut</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool|<a href="classes/Symfony-Component-String-TruncateMode.html"><abbr title="\Symfony\Component\String\TruncateMode">TruncateMode</abbr></a></span>
                 = <span class="phpdocumentor-signature__argument__default-value">TruncateMode::Char</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_unwrap">
        unwrap()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_unwrap" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">52</span>

    </aside>

        <p class="phpdocumentor-summary">Unwraps instances of AbstractString back to strings.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>        <span class="phpdocumentor-signature__name">unwrap</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$values</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, string&gt;|array&lt;string|int, mixed&gt;</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$values</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, string&gt;|array&lt;string|int, mixed&gt;</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_upper">
        upper()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_upper" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">481</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">upper</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
    
    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_width">
        width()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_width" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">504</span>

    </aside>

        <p class="phpdocumentor-summary">Returns the printable length on a terminal.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">width</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">bool&nbsp;</span><span class="phpdocumentor-signature__argument__name">$ignoreAnsiDecoration</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">true</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$ignoreAnsiDecoration</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool</span>
                 = <span class="phpdocumentor-signature__argument__default-value">true</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_wordwrap">
        wordwrap()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_wordwrap" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">663</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">wordwrap</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$width</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">75</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$break</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&quot;
&quot;</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool&nbsp;</span><span class="phpdocumentor-signature__argument__name">$cut</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$width</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">75</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$break</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&quot;
&quot;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$cut</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_wrap">
        wrap()
        <a href="classes/Symfony-Component-String-AbstractString.html#method_wrap" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractString.php"><a href="files/vendor-symfony-string-abstractstring.html"><abbr title="vendor/symfony/string/AbstractString.php">AbstractString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">70</span>

    </aside>

        <p class="phpdocumentor-summary">Wraps (and normalizes) strings in instances of AbstractString.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>        <span class="phpdocumentor-signature__name">wrap</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$values</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, static&gt;|array&lt;string|int, mixed&gt;</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$values</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, static&gt;|array&lt;string|int, mixed&gt;</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -private
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_getLocaleTransliterator">
        getLocaleTransliterator()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_getLocaleTransliterator" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">642</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">private</span>
                    <span class="phpdocumentor-signature__name">getLocaleTransliterator</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$locale</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$id</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type"><abbr title="\Transliterator">Transliterator</abbr>|null</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$locale</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$id</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type"><abbr title="\Transliterator">Transliterator</abbr>|null</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -private
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_pad">
        pad()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_pad" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">536</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">private</span>
                    <span class="phpdocumentor-signature__name">pad</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$len</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">self&nbsp;</span><span class="phpdocumentor-signature__argument__name">$pad</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$type</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">static</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$len</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$pad</span>
                : <span class="phpdocumentor-signature__argument__return-type">self</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$type</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">static</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -private
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_wcswidth">
        wcswidth()
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_wcswidth" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/symfony/string/AbstractUnicodeString.php"><a href="files/vendor-symfony-string-abstractunicodestring.html"><abbr title="vendor/symfony/string/AbstractUnicodeString.php">AbstractUnicodeString.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">575</span>

    </aside>

        <p class="phpdocumentor-summary">Based on https://github.com/jquast/wcwidth, a Python implementation of https://www.cl.cam.ac.uk/~mgk25/ucs/wcwidth.c.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">private</span>
                    <span class="phpdocumentor-signature__name">wcswidth</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int</span>
            </section>

</article>
            </section>

        <div class="phpdocumentor-modal" id="source-view">
    <div class="phpdocumentor-modal-bg" data-exit-button></div>
    <div class="phpdocumentor-modal-container">
        <div class="phpdocumentor-modal-content">
            <pre style="max-height: 500px; overflow-y: scroll" data-src="files/vendor/symfony/string/AbstractUnicodeString.php.txt" class="language-php line-numbers linkable-line-numbers"></pre>
        </div>
        <button data-exit-button class="phpdocumentor-modal__close">&times;</button>
    </div>
</div>

    <script type="text/javascript">
        (function () {
            function loadExternalCodeSnippet(el, url, line) {
                Array.prototype.slice.call(el.querySelectorAll('pre[data-src]')).forEach((pre) => {
                    const src = url || pre.getAttribute('data-src').replace(/\\/g, '/');
                    const language = 'php';

                    const code = document.createElement('code');
                    code.className = 'language-' + language;
                    pre.textContent = '';
                    pre.setAttribute('data-line', line)
                    code.textContent = 'Loading…';
                    pre.appendChild(code);

                    var xhr = new XMLHttpRequest();

                    xhr.open('GET', src, true);

                    xhr.onreadystatechange = function () {
                        if (xhr.readyState !== 4) {
                            return;
                        }

                        if (xhr.status < 400 && xhr.responseText) {
                            code.textContent = xhr.responseText;
                            Prism.highlightElement(code);
                            d=document.getElementsByClassName("line-numbers");
                            d[0].scrollTop = d[0].children[1].offsetTop;
                            return;
                        }

                        if (xhr.status === 404) {
                            code.textContent = '✖ Error: File could not be found';
                            return;
                        }

                        if (xhr.status >= 400) {
                            code.textContent = '✖ Error ' + xhr.status + ' while fetching file: ' + xhr.statusText;
                            return;
                        }

                        code.textContent = '✖ Error: An unknown error occurred';
                    };

                    xhr.send(null);
                });
            }

            const modalButtons = document.querySelectorAll("[data-modal]");
            const openedAsLocalFile = window.location.protocol === 'file:';
            if (modalButtons.length > 0 && openedAsLocalFile) {
                console.warn(
                    'Viewing the source code is unavailable because you are opening this page from the file:// scheme; ' +
                    'browsers block XHR requests when a page is opened this way'
                );
            }

            modalButtons.forEach(function (trigger) {
                if (openedAsLocalFile) {
                    trigger.setAttribute("hidden", "hidden");
                }

                trigger.addEventListener("click", function (event) {
                    event.preventDefault();
                    const modal = document.getElementById(trigger.dataset.modal);
                    if (!modal) {
                        console.error(`Modal with id "${trigger.dataset.modal}" could not be found`);
                        return;
                    }
                    modal.classList.add("phpdocumentor-modal__open");

                    loadExternalCodeSnippet(modal, trigger.dataset.src || null, trigger.dataset.line)
                    const exits = modal.querySelectorAll("[data-exit-button]");
                    exits.forEach(function (exit) {
                        exit.addEventListener("click", function (event) {
                            event.preventDefault();
                            modal.classList.remove("phpdocumentor-modal__open");
                        });
                    });
                });
            });
        })();
    </script>

    </article>
                                </section>
                <section class="phpdocumentor-on-this-page__sidebar">
                                
    <section class="phpdocumentor-on-this-page__content">
        <strong class="phpdocumentor-on-this-page__title">On this page</strong>

        <ul class="phpdocumentor-list -clean">
            <li class="phpdocumentor-on-this-page-section__title">Table Of Contents</li>
            <li>
                <ul class="phpdocumentor-list -clean">
                                        <li><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#toc-constants">Constants</a></li>
                                                            <li><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#toc-properties">Properties</a></li>
                                                            <li><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#toc-methods">Methods</a></li>
                                    </ul>
            </li>
                            <li class="phpdocumentor-on-this-page-section__title">Constants</li>
                <li>
                    <ul class="phpdocumentor-list -clean">
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFC">NFC</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFD">NFD</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFKC">NFKC</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_NFKD">NFKD</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_OFFSET_CAPTURE">PREG_OFFSET_CAPTURE</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_PATTERN_ORDER">PREG_PATTERN_ORDER</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SET_ORDER">PREG_SET_ORDER</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT">PREG_SPLIT</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT_DELIM_CAPTURE">PREG_SPLIT_DELIM_CAPTURE</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT_NO_EMPTY">PREG_SPLIT_NO_EMPTY</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_SPLIT_OFFSET_CAPTURE">PREG_SPLIT_OFFSET_CAPTURE</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#constant_PREG_UNMATCHED_AS_NULL">PREG_UNMATCHED_AS_NULL</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_ASCII">ASCII</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_FOLD_FROM">FOLD_FROM</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_FOLD_TO">FOLD_TO</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_TRANSLIT_FROM">TRANSLIT_FROM</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#constant_TRANSLIT_TO">TRANSLIT_TO</a></li>
                                            </ul>
                </li>
            
                            <li class="phpdocumentor-on-this-page-section__title">Properties</li>
                <li>
                    <ul class="phpdocumentor-list -clean">
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#property_ignoreCase">$ignoreCase</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#property_string">$string</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#property_tableWide">$tableWide</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#property_tableZero">$tableZero</a></li>
                                                    <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#property_transliterators">$transliterators</a></li>
                                            </ul>
                </li>
            
                        <li class="phpdocumentor-on-this-page-section__title">Methods</li>
            <li>
                <ul class="phpdocumentor-list -clean">
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method___clone">__clone()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method___construct">__construct()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method___sleep">__sleep()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method___toString">__toString()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_after">after()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_afterLast">afterLast()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_append">append()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_ascii">ascii()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_before">before()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_beforeLast">beforeLast()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_bytesAt">bytesAt()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_camel">camel()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_chunk">chunk()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_codePointsAt">codePointsAt()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_collapseWhitespace">collapseWhitespace()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_containsAny">containsAny()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_endsWith">endsWith()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_ensureEnd">ensureEnd()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_ensureStart">ensureStart()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_equalsTo">equalsTo()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_folded">folded()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_fromCodePoints">fromCodePoints()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_ignoreCase">ignoreCase()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_indexOf">indexOf()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_indexOfLast">indexOfLast()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_isEmpty">isEmpty()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_join">join()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_jsonSerialize">jsonSerialize()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_kebab">kebab()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_length">length()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_localeLower">localeLower()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_localeTitle">localeTitle()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_localeUpper">localeUpper()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_lower">lower()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_match">match()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_normalize">normalize()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_padBoth">padBoth()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_padEnd">padEnd()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_padStart">padStart()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_pascal">pascal()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_prepend">prepend()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_repeat">repeat()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_replace">replace()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_replaceMatches">replaceMatches()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_reverse">reverse()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_slice">slice()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_snake">snake()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_splice">splice()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_split">split()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_startsWith">startsWith()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_title">title()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_toByteString">toByteString()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_toCodePointString">toCodePointString()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_toString">toString()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_toUnicodeString">toUnicodeString()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trim">trim()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimEnd">trimEnd()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimPrefix">trimPrefix()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimStart">trimStart()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_trimSuffix">trimSuffix()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_truncate">truncate()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_unwrap">unwrap()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_upper">upper()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_width">width()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_wordwrap">wordwrap()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractString.html#method_wrap">wrap()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_getLocaleTransliterator">getLocaleTransliterator()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_pad">pad()</a></li>
                                            <li class=""><a href="classes/Symfony-Component-String-AbstractUnicodeString.html#method_wcswidth">wcswidth()</a></li>
                                    </ul>
            </li>
                    </ul>
    </section>

                </section>
                            </div>
            <section data-search-results class="phpdocumentor-search-results phpdocumentor-search-results--hidden">
    <section class="phpdocumentor-search-results__dialog">
        <header class="phpdocumentor-search-results__header">
            <h2 class="phpdocumentor-search-results__title">Search results</h2>
            <button class="phpdocumentor-search-results__close"><i class="fas fa-times"></i></button>
        </header>
        <section class="phpdocumentor-search-results__body">
            <ul class="phpdocumentor-search-results__entries"></ul>
        </section>
    </section>
</section>
        </div>
        <a href="classes/Symfony-Component-String-AbstractUnicodeString.html#top" class="phpdocumentor-back-to-top"><i class="fas fa-chevron-circle-up"></i></a>

    </main>

    <script>
        cssVars({});
    </script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-highlight/prism-line-highlight.min.js"></script>
</body>
</html>
