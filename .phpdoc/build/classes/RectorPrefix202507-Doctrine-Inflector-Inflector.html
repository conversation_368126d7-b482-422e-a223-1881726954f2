<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
            <title>Documentation</title>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="../">
    <link rel="icon" href="images/favicon.ico"/>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/base.css">
            <link rel="preconnect" href="https://fonts.gstatic.com">
        <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@100;200;300;400;600;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Source+Code+Pro:wght@400;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="css/template.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/css/all.min.css" integrity="sha256-ybRkN9dBjhcS2qrW1z+hfCxq+1aBdwyQM5wlQoQVt/0=" crossorigin="anonymous" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/themes/prism-okaidia.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-numbers/prism-line-numbers.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-highlight/prism-line-highlight.css">
                <script src="https://cdn.jsdelivr.net/npm/fuse.js@3.4.6"></script>
        <script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2"></script>
        <script src="js/template.js"></script>
        <script src="js/search.js"></script>
        <script defer src="js/searchIndex.js"></script>
    </head>
<body id="top">
    <header class="phpdocumentor-header phpdocumentor-section">
    <h1 class="phpdocumentor-title"><a href="" class="phpdocumentor-title__link">Documentation</a></h1>
    <input class="phpdocumentor-header__menu-button" type="checkbox" id="menu-button" name="menu-button" />
    <label class="phpdocumentor-header__menu-icon" for="menu-button">
        <i class="fas fa-bars"></i>
    </label>
    <section data-search-form class="phpdocumentor-search">
    <label>
        <span class="visually-hidden">Search for</span>
        <svg class="phpdocumentor-search__icon" width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="7.5" cy="7.5" r="6.5" stroke="currentColor" stroke-width="2"/>
            <line x1="12.4892" y1="12.2727" x2="19.1559" y2="18.9393" stroke="currentColor" stroke-width="3"/>
        </svg>
        <input type="search" class="phpdocumentor-field phpdocumentor-search__field" placeholder="Loading .." disabled />
    </label>
</section>

    <nav class="phpdocumentor-topnav">
    <ul class="phpdocumentor-topnav__menu">
        </ul>
</nav>
</header>

    <main class="phpdocumentor">
        <div class="phpdocumentor-section">
            <input class="phpdocumentor-sidebar__menu-button" type="checkbox" id="sidebar-button" name="sidebar-button" />
<label class="phpdocumentor-sidebar__menu-icon" for="sidebar-button">
    Menu
</label>
<aside class="phpdocumentor-column -three phpdocumentor-sidebar">
                    <section class="phpdocumentor-sidebar__category -namespaces">
            <h2 class="phpdocumentor-sidebar__category-header">Namespaces</h2>
                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/app.html" class="">App</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/app-commands.html" class="">Commands</a>
                
            </li>
                    <li>
                <a href="namespaces/app-providers.html" class="">Providers</a>
                
            </li>
                    <li>
                <a href="namespaces/app-services.html" class="">Services</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/tests.html" class="">Tests</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/illuminate.html" class="">Illuminate</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/illuminate-bus.html" class="">Bus</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-console.html" class="">Console</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-redis.html" class="">Redis</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-cache.html" class="">Cache</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-support.html" class="">Support</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-config.html" class="">Config</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-container.html" class="">Container</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-contracts.html" class="">Contracts</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-database.html" class="">Database</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-events.html" class="">Events</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-filesystem.html" class="">Filesystem</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-http.html" class="">Http</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-pipeline.html" class="">Pipeline</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-process.html" class="">Process</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-session.html" class="">Session</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-testing.html" class="">Testing</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-view.html" class="">View</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-foundation.html" class="">Foundation</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/paratest.html" class="">ParaTest</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/brick.html" class="">Brick</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/brick-math.html" class="">Math</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/composer.html" class="">Composer</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/composer-autoload.html" class="">Autoload</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/doctrine.html" class="">Doctrine</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/doctrine-deprecations.html" class="">Deprecations</a>
                
            </li>
                    <li>
                <a href="namespaces/doctrine-inflector.html" class="">Inflector</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/cron.html" class="">Cron</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rectorlaravel.html" class="">RectorLaravel</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/rectorlaravel-commands.html" class="">Commands</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-contract.html" class="">Contract</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-nodeanalyzer.html" class="">NodeAnalyzer</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-nodefactory.html" class="">NodeFactory</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-rector.html" class="">Rector</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-set.html" class="">Set</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-valueobject.html" class="">ValueObject</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/ergebnis.html" class="">Ergebnis</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/ergebnis-composer.html" class="">Composer</a>
                
            </li>
                    <li>
                <a href="namespaces/ergebnis-json.html" class="">Json</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/fidry.html" class="">Fidry</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/fidry-cpucorecounter.html" class="">CpuCoreCounter</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/whoops.html" class="">Whoops</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/whoops-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/whoops-handler.html" class="">Handler</a>
                
            </li>
                    <li>
                <a href="namespaces/whoops-inspector.html" class="">Inspector</a>
                
            </li>
                    <li>
                <a href="namespaces/whoops-util.html" class="">Util</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/fruitcake.html" class="">Fruitcake</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/fruitcake-cors.html" class="">Cors</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/grahamcampbell.html" class="">GrahamCampbell</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/grahamcampbell-resulttype.html" class="">ResultType</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/guzzlehttp.html" class="">GuzzleHttp</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/guzzlehttp-cookie.html" class="">Cookie</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-handler.html" class="">Handler</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-promise.html" class="">Promise</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-psr7.html" class="">Psr7</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-uritemplate.html" class="">UriTemplate</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/hamcrest.html" class="">Hamcrest</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/hamcrest-arrays.html" class="">Arrays</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-collection.html" class="">Collection</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-core.html" class="">Core</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-internal.html" class="">Internal</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-number.html" class="">Number</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-text.html" class="">Text</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-type.html" class="">Type</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-xml.html" class="">Xml</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/iamcal.html" class="">iamcal</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/jean85.html" class="">Jean85</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/jean85-exception.html" class="">Exception</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/joli.html" class="">Joli</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/joli-jolinotif.html" class="">JoliNotif</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phar.html" class="">phar</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/qa.html" class="">qa</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/qa-cs.html" class="">cs</a>
                
            </li>
                    <li>
                <a href="namespaces/qa-phpstan.html" class="">phpstan</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/jolicode.html" class="">JoliCode</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/jolicode-phposhelper.html" class="">PhpOsHelper</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/jsonschema.html" class="">JsonSchema</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/jsonschema-constraints.html" class="">Constraints</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-entity.html" class="">Entity</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-iterator.html" class="">Iterator</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-tool.html" class="">Tool</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-uri.html" class="">Uri</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/larastan.html" class="">Larastan</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/larastan-larastan.html" class="">Larastan</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/laravel.html" class="">Laravel</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/laravel-prompts.html" class="">Prompts</a>
                
            </li>
                    <li>
                <a href="namespaces/laravel-serializableclosure.html" class="">SerializableClosure</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/database.html" class="">Database</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/database-seeders.html" class="">Seeders</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/laravelzero.html" class="">LaravelZero</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/laravelzero-framework.html" class="">Framework</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/league.html" class="">League</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/league-flysystem.html" class="">Flysystem</a>
                
            </li>
                    <li>
                <a href="namespaces/league-mimetypedetection.html" class="">MimeTypeDetection</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/localheinz.html" class="">Localheinz</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/localheinz-diff.html" class="">Diff</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/mabeenum.html" class="">MabeEnum</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/masterminds.html" class="">Masterminds</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/masterminds-html5.html" class="">HTML5</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/mockery.html" class="">Mockery</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/mockery-adapter.html" class="">Adapter</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-countvalidator.html" class="">CountValidator</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-generator.html" class="">Generator</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-loader.html" class="">Loader</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-matcher.html" class="">Matcher</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/deepcopy.html" class="">DeepCopy</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/deepcopy-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-filter.html" class="">Filter</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-matcher.html" class="">Matcher</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-reflection.html" class="">Reflection</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-typefilter.html" class="">TypeFilter</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-typematcher.html" class="">TypeMatcher</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/carbon.html" class="">Carbon</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/carbon-doctrine.html" class="">Doctrine</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-messageformatter.html" class="">MessageFormatter</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-cli.html" class="">Cli</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-exceptions.html" class="">Exceptions</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-laravel.html" class="">Laravel</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-phpstan.html" class="">PHPStan</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-traits.html" class="">Traits</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/nette.html" class="">Nette</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/nette-utils.html" class="">Utils</a>
                
            </li>
                    <li>
                <a href="namespaces/nette-localization.html" class="">Localization</a>
                
            </li>
                    <li>
                <a href="namespaces/nette-iterators.html" class="">Iterators</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpparser.html" class="">PhpParser</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpparser-builder.html" class="">Builder</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-comment.html" class="">Comment</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-errorhandler.html" class="">ErrorHandler</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-lexer.html" class="">Lexer</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-node.html" class="">Node</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-nodevisitor.html" class="">NodeVisitor</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-parser.html" class="">Parser</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-prettyprinter.html" class="">PrettyPrinter</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/nunomaduro.html" class="">NunoMaduro</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/nunomaduro-collision.html" class="">Collision</a>
                
            </li>
                    <li>
                <a href="namespaces/nunomaduro-laravelconsolesummary.html" class="">LaravelConsoleSummary</a>
                
            </li>
                    <li>
                <a href="namespaces/nunomaduro-laravelconsoletask.html" class="">LaravelConsoleTask</a>
                
            </li>
                    <li>
                <a href="namespaces/nunomaduro-laraveldesktopnotifier.html" class="">LaravelDesktopNotifier</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/termwind.html" class="">Termwind</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/termwind-components.html" class="">Components</a>
                
            </li>
                    <li>
                <a href="namespaces/termwind-enums.html" class="">Enums</a>
                
            </li>
                    <li>
                <a href="namespaces/termwind-laravel.html" class="">Laravel</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/peck.html" class="">Peck</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/peck-plugins.html" class="">Plugins</a>
                
            </li>
                    <li>
                <a href="namespaces/peck-services.html" class="">Services</a>
                
            </li>
                    <li>
                <a href="namespaces/peck-support.html" class="">Support</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/pest.html" class="">Pest</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/pest-configuration.html" class="">Configuration</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-contracts.html" class="">Contracts</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-exceptions.html" class="">Exceptions</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-factories.html" class="">Factories</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-plugins.html" class="">Plugins</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-support.html" class="">Support</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-testcasefilters.html" class="">TestCaseFilters</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-testcasemethodfilters.html" class="">TestCaseMethodFilters</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-arch.html" class="">Arch</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-mutate.html" class="">Mutate</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-stressless.html" class="">Stressless</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phario.html" class="">PharIo</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phario-manifest.html" class="">Manifest</a>
                
            </li>
                    <li>
                <a href="namespaces/phario-csfixer.html" class="">CSFixer</a>
                
            </li>
                    <li>
                <a href="namespaces/phario-version.html" class="">Version</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpdocumentor.html" class="">phpDocumentor</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpdocumentor-reflection.html" class="">Reflection</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpoption.html" class="">PhpOption</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpstan.html" class="">PHPStan</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpstan-phpdocparser.html" class="">PhpDocParser</a>
                
            </li>
                    <li>
                <a href="namespaces/phpstan-dependencyinjection.html" class="">DependencyInjection</a>
                
            </li>
                    <li>
                <a href="namespaces/phpstan-rules.html" class="">Rules</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpunit.html" class="">PHPUnit</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpunit-runner.html" class="">Runner</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-textui.html" class="">TextUI</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-event.html" class="">Event</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-framework.html" class="">Framework</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-metadata.html" class="">Metadata</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-util.html" class="">Util</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-architecture.html" class="">Architecture</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/psr.html" class="">Psr</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/psr-clock.html" class="">Clock</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-container.html" class="">Container</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-eventdispatcher.html" class="">EventDispatcher</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-http.html" class="">Http</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-log.html" class="">Log</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-simplecache.html" class="">SimpleCache</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/ramsey.html" class="">Ramsey</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/ramsey-collection.html" class="">Collection</a>
                
            </li>
                    <li>
                <a href="namespaces/ramsey-uuid.html" class="">Uuid</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rectorprefix202507.html" class="">RectorPrefix202507</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/rectorprefix202507-clue.html" class="">Clue</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-composer.html" class="">Composer</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-doctrine.html" class="">Doctrine</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-evenement.html" class="">Evenement</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-fidry.html" class="">Fidry</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-illuminate.html" class="">Illuminate</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-nette.html" class="">Nette</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-ondram.html" class="">OndraM</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-psr.html" class="">Psr</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-react.html" class="">React</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-symfony.html" class="">Symfony</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-sebastianbergmann.html" class="">SebastianBergmann</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-symplify.html" class="">Symplify</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-webmozart.html" class="">Webmozart</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rectorprefix20220529.html" class="">RectorPrefix20220529</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rector.html" class="">Rector</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/rector-arguments.html" class="">Arguments</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-carbon.html" class="">Carbon</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-codequality.html" class="">CodeQuality</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-codingstyle.html" class="">CodingStyle</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-deadcode.html" class="">DeadCode</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-earlyreturn.html" class="">EarlyReturn</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-instanceof.html" class="">Instanceof_</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-naming.html" class="">Naming</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-netteutils.html" class="">NetteUtils</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php52.html" class="">Php52</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php53.html" class="">Php53</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php54.html" class="">Php54</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php55.html" class="">Php55</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php56.html" class="">Php56</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php70.html" class="">Php70</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php71.html" class="">Php71</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php72.html" class="">Php72</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php73.html" class="">Php73</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php74.html" class="">Php74</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php80.html" class="">Php80</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php81.html" class="">Php81</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php82.html" class="">Php82</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php83.html" class="">Php83</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php84.html" class="">Php84</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php85.html" class="">Php85</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-privatization.html" class="">Privatization</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-removing.html" class="">Removing</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-renaming.html" class="">Renaming</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-strict.html" class="">Strict</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-transform.html" class="">Transform</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-typedeclaration.html" class="">TypeDeclaration</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-visibility.html" class="">Visibility</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-application.html" class="">Application</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-autoloading.html" class="">Autoloading</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-betterphpdocparser.html" class="">BetterPhpDocParser</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-bootstrap.html" class="">Bootstrap</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-bridge.html" class="">Bridge</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-caching.html" class="">Caching</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-changesreporting.html" class="">ChangesReporting</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-comments.html" class="">Comments</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-composer.html" class="">Composer</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-config.html" class="">Config</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-configuration.html" class="">Configuration</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-console.html" class="">Console</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-contract.html" class="">Contract</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-customrules.html" class="">CustomRules</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-dependencyinjection.html" class="">DependencyInjection</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-differ.html" class="">Differ</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-enum.html" class="">Enum</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-familytree.html" class="">FamilyTree</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-filesystem.html" class="">FileSystem</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-git.html" class="">Git</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodeanalyzer.html" class="">NodeAnalyzer</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodecollector.html" class="">NodeCollector</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodedecorator.html" class="">NodeDecorator</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodemanipulator.html" class="">NodeManipulator</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodenameresolver.html" class="">NodeNameResolver</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodenestingscope.html" class="">NodeNestingScope</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodetyperesolver.html" class="">NodeTypeResolver</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-parallel.html" class="">Parallel</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php.html" class="">Php</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpattribute.html" class="">PhpAttribute</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpdocparser.html" class="">PhpDocParser</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpparser.html" class="">PhpParser</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpstan.html" class="">PHPStan</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpstanstatictypemapper.html" class="">PHPStanStaticTypeMapper</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-postrector.html" class="">PostRector</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-processanalyzer.html" class="">ProcessAnalyzer</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-rector.html" class="">Rector</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-reflection.html" class="">Reflection</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-reporting.html" class="">Reporting</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-set.html" class="">Set</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-skipper.html" class="">Skipper</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-staticreflection.html" class="">StaticReflection</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-statictypemapper.html" class="">StaticTypeMapper</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-testing.html" class="">Testing</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-util.html" class="">Util</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-validation.html" class="">Validation</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-valueobject.html" class="">ValueObject</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-vendorlocker.html" class="">VendorLocker</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-versionbonding.html" class="">VersionBonding</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-tests.html" class="">Tests</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-rectorinstaller.html" class="">RectorInstaller</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-doctrine.html" class="">Doctrine</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp72.html" class="">DowngradePhp72</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp73.html" class="">DowngradePhp73</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp74.html" class="">DowngradePhp74</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp80.html" class="">DowngradePhp80</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp81.html" class="">DowngradePhp81</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp82.html" class="">DowngradePhp82</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp83.html" class="">DowngradePhp83</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp84.html" class="">DowngradePhp84</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp85.html" class="">DowngradePhp85</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodefactory.html" class="">NodeFactory</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpdocdecorator.html" class="">PhpDocDecorator</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpunit.html" class="">PHPUnit</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-symfony.html" class="">Symfony</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-typeperfect.html" class="">TypePerfect</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/utils.html" class="">Utils</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/utils-rector.html" class="">Rector</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/symfony.html" class="">Symfony</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/symfony-component.html" class="">Component</a>
                
            </li>
                    <li>
                <a href="namespaces/symfony-contracts.html" class="">Contracts</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/symplify.html" class="">Symplify</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/symplify-ruledocgenerator.html" class="">RuleDocGenerator</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/sebastianbergmann.html" class="">SebastianBergmann</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/sebastianbergmann-codecoverage.html" class="">CodeCoverage</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-fileiterator.html" class="">FileIterator</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-invoker.html" class="">Invoker</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-template.html" class="">Template</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-timer.html" class="">Timer</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-cliparser.html" class="">CliParser</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-codeunit.html" class="">CodeUnit</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-codeunitreverselookup.html" class="">CodeUnitReverseLookup</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-comparator.html" class="">Comparator</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-complexity.html" class="">Complexity</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-diff.html" class="">Diff</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-environment.html" class="">Environment</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-exporter.html" class="">Exporter</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-globalstate.html" class="">GlobalState</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-linesofcode.html" class="">LinesOfCode</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-objectenumerator.html" class="">ObjectEnumerator</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-objectreflector.html" class="">ObjectReflector</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-recursioncontext.html" class="">RecursionContext</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-type.html" class="">Type</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/staabm.html" class="">staabm</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/staabm-sideeffectsdetector.html" class="">SideEffectsDetector</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/theseer.html" class="">TheSeer</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/theseer-tokenizer.html" class="">Tokenizer</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/tomasvotruba.html" class="">TomasVotruba</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/tomasvotruba-typecoverage.html" class="">TypeCoverage</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/dotenv.html" class="">Dotenv</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/dotenv-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-loader.html" class="">Loader</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-parser.html" class="">Parser</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-repository.html" class="">Repository</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-store.html" class="">Store</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/voku.html" class="">voku</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/voku-helper.html" class="">helper</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/webmozart.html" class="">Webmozart</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/webmozart-assert.html" class="">Assert</a>
                
            </li>
            </ul>

                        </section>
                <section class="phpdocumentor-sidebar__category -packages">
            <h2 class="phpdocumentor-sidebar__category-header">Packages</h2>
                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="packages/Application.html" class="">Application</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="packages/JsonSchema.html" class="">JsonSchema</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="packages/JsonSchema-Entity.html" class="">Entity</a>
                
            </li>
                    <li>
                <a href="packages/JsonSchema-Exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="packages/JsonSchema-Iterator.html" class="">Iterator</a>
                
            </li>
            </ul>

                        </section>
            
    <section class="phpdocumentor-sidebar__category -reports">
        <h2 class="phpdocumentor-sidebar__category-header">Reports</h2>
                <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/deprecated.html">Deprecated</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/errors.html">Errors</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/markers.html">Markers</a></h3>
    </section>

    <section class="phpdocumentor-sidebar__category -indices">
        <h2 class="phpdocumentor-sidebar__category-header">Indices</h2>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="indices/files.html">Files</a></h3>
    </section>
</aside>

            <div class="phpdocumentor-column -nine phpdocumentor-content">
                                <section>
                                        <ul class="phpdocumentor-breadcrumbs">
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/rectorprefix202507.html">RectorPrefix202507</a></li>
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/rectorprefix202507-doctrine.html">Doctrine</a></li>
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/rectorprefix202507-doctrine-inflector.html">Inflector</a></li>
    </ul>

    <article class="phpdocumentor-element -class">
        <h2 class="phpdocumentor-content__title">
    Inflector

    
            <div class="phpdocumentor-element__package">
            in package
            <ul class="phpdocumentor-breadcrumbs">
                                    <li class="phpdocumentor-breadcrumb"><a href="packages/Application.html">Application</a></li>
                            </ul>
        </div>
    
    
    </h2>

<div class="phpdocumentor-label-line">


</div>

        <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">21</span>

    </aside>

        








<h3 id="toc">
    Table of Contents
    <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#toc" class="headerlink"><i class="fas fa-link"></i></a>

</h3>







<h4 id="toc-constants">
    Constants
    <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#toc-constants" class="headerlink"><i class="fas fa-link"></i></a>

</h4>
<dl class="phpdocumentor-table-of-contents">
            <dt class="phpdocumentor-table-of-contents__entry -constant -private">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#constant_ACCENTED_CHARACTERS">ACCENTED_CHARACTERS</a>
    <span>
        &nbsp;= [&#039;À&#039; =&gt; &#039;A&#039;, &#039;Á&#039; =&gt; &#039;A&#039;, &#039;Â&#039; =&gt; &#039;A&#039;, &#039;Ã&#039; =&gt; &#039;A&#039;, &#039;Ä&#039; =&gt; &#039;Ae&#039;, &#039;Æ&#039; =&gt; &#039;Ae&#039;, &#039;Å&#039; =&gt; &#039;Aa&#039;, &#039;æ&#039; =&gt; &#039;a&#039;, &#039;Ç&#039; =&gt; &#039;C&#039;, &#039;È&#039; =&gt; &#039;E&#039;, &#039;É&#039; =&gt; &#039;E&#039;, &#039;Ê&#039; =&gt; &#039;E&#039;, &#039;Ë&#039; =&gt; &#039;E&#039;, &#039;Ì&#039; =&gt; &#039;I&#039;, &#039;Í&#039; =&gt; &#039;I&#039;, &#039;Î&#039; =&gt; &#039;I&#039;, &#039;Ï&#039; =&gt; &#039;I&#039;, &#039;Ñ&#039; =&gt; &#039;N&#039;, &#039;Ò&#039; =&gt; &#039;O&#039;, &#039;Ó&#039; =&gt; &#039;O&#039;, &#039;Ô&#039; =&gt; &#039;O&#039;, &#039;Õ&#039; =&gt; &#039;O&#039;, &#039;Ö&#039; =&gt; &#039;Oe&#039;, &#039;Ù&#039; =&gt; &#039;U&#039;, &#039;Ú&#039; =&gt; &#039;U&#039;, &#039;Û&#039; =&gt; &#039;U&#039;, &#039;Ü&#039; =&gt; &#039;Ue&#039;, &#039;Ý&#039; =&gt; &#039;Y&#039;, &#039;ß&#039; =&gt; &#039;ss&#039;, &#039;à&#039; =&gt; &#039;a&#039;, &#039;á&#039; =&gt; &#039;a&#039;, &#039;â&#039; =&gt; &#039;a&#039;, &#039;ã&#039; =&gt; &#039;a&#039;, &#039;ä&#039; =&gt; &#039;ae&#039;, &#039;å&#039; =&gt; &#039;aa&#039;, &#039;ç&#039; =&gt; &#039;c&#039;, &#039;è&#039; =&gt; &#039;e&#039;, &#039;é&#039; =&gt; &#039;e&#039;, &#039;ê&#039; =&gt; &#039;e&#039;, &#039;ë&#039; =&gt; &#039;e&#039;, &#039;ì&#039; =&gt; &#039;i&#039;, &#039;í&#039; =&gt; &#039;i&#039;, &#039;î&#039; =&gt; &#039;i&#039;, &#039;ï&#039; =&gt; &#039;i&#039;, &#039;ñ&#039; =&gt; &#039;n&#039;, &#039;ò&#039; =&gt; &#039;o&#039;, &#039;ó&#039; =&gt; &#039;o&#039;, &#039;ô&#039; =&gt; &#039;o&#039;, &#039;õ&#039; =&gt; &#039;o&#039;, &#039;ö&#039; =&gt; &#039;oe&#039;, &#039;ù&#039; =&gt; &#039;u&#039;, &#039;ú&#039; =&gt; &#039;u&#039;, &#039;û&#039; =&gt; &#039;u&#039;, &#039;ü&#039; =&gt; &#039;ue&#039;, &#039;ý&#039; =&gt; &#039;y&#039;, &#039;ÿ&#039; =&gt; &#039;y&#039;, &#039;Ā&#039; =&gt; &#039;A&#039;, &#039;ā&#039; =&gt; &#039;a&#039;, &#039;Ă&#039; =&gt; &#039;A&#039;, &#039;ă&#039; =&gt; &#039;a&#039;, &#039;Ą&#039; =&gt; &#039;A&#039;, &#039;ą&#039; =&gt; &#039;a&#039;, &#039;Ć&#039; =&gt; &#039;C&#039;, &#039;ć&#039; =&gt; &#039;c&#039;, &#039;Ĉ&#039; =&gt; &#039;C&#039;, &#039;ĉ&#039; =&gt; &#039;c&#039;, &#039;Ċ&#039; =&gt; &#039;C&#039;, &#039;ċ&#039; =&gt; &#039;c&#039;, &#039;Č&#039; =&gt; &#039;C&#039;, &#039;č&#039; =&gt; &#039;c&#039;, &#039;Ď&#039; =&gt; &#039;D&#039;, &#039;ď&#039; =&gt; &#039;d&#039;, &#039;Đ&#039; =&gt; &#039;D&#039;, &#039;đ&#039; =&gt; &#039;d&#039;, &#039;Ē&#039; =&gt; &#039;E&#039;, &#039;ē&#039; =&gt; &#039;e&#039;, &#039;Ĕ&#039; =&gt; &#039;E&#039;, &#039;ĕ&#039; =&gt; &#039;e&#039;, &#039;Ė&#039; =&gt; &#039;E&#039;, &#039;ė&#039; =&gt; &#039;e&#039;, &#039;Ę&#039; =&gt; &#039;E&#039;, &#039;ę&#039; =&gt; &#039;e&#039;, &#039;Ě&#039; =&gt; &#039;E&#039;, &#039;ě&#039; =&gt; &#039;e&#039;, &#039;Ĝ&#039; =&gt; &#039;G&#039;, &#039;ĝ&#039; =&gt; &#039;g&#039;, &#039;Ğ&#039; =&gt; &#039;G&#039;, &#039;ğ&#039; =&gt; &#039;g&#039;, &#039;Ġ&#039; =&gt; &#039;G&#039;, &#039;ġ&#039; =&gt; &#039;g&#039;, &#039;Ģ&#039; =&gt; &#039;G&#039;, &#039;ģ&#039; =&gt; &#039;g&#039;, &#039;Ĥ&#039; =&gt; &#039;H&#039;, &#039;ĥ&#039; =&gt; &#039;h&#039;, &#039;Ħ&#039; =&gt; &#039;H&#039;, &#039;ħ&#039; =&gt; &#039;h&#039;, &#039;Ĩ&#039; =&gt; &#039;I&#039;, &#039;ĩ&#039; =&gt; &#039;i&#039;, &#039;Ī&#039; =&gt; &#039;I&#039;, &#039;ī&#039; =&gt; &#039;i&#039;, &#039;Ĭ&#039; =&gt; &#039;I&#039;, &#039;ĭ&#039; =&gt; &#039;i&#039;, &#039;Į&#039; =&gt; &#039;I&#039;, &#039;į&#039; =&gt; &#039;i&#039;, &#039;İ&#039; =&gt; &#039;I&#039;, &#039;ı&#039; =&gt; &#039;i&#039;, &#039;Ĳ&#039; =&gt; &#039;IJ&#039;, &#039;ĳ&#039; =&gt; &#039;ij&#039;, &#039;Ĵ&#039; =&gt; &#039;J&#039;, &#039;ĵ&#039; =&gt; &#039;j&#039;, &#039;Ķ&#039; =&gt; &#039;K&#039;, &#039;ķ&#039; =&gt; &#039;k&#039;, &#039;ĸ&#039; =&gt; &#039;k&#039;, &#039;Ĺ&#039; =&gt; &#039;L&#039;, &#039;ĺ&#039; =&gt; &#039;l&#039;, &#039;Ļ&#039; =&gt; &#039;L&#039;, &#039;ļ&#039; =&gt; &#039;l&#039;, &#039;Ľ&#039; =&gt; &#039;L&#039;, &#039;ľ&#039; =&gt; &#039;l&#039;, &#039;Ŀ&#039; =&gt; &#039;L&#039;, &#039;ŀ&#039; =&gt; &#039;l&#039;, &#039;Ł&#039; =&gt; &#039;L&#039;, &#039;ł&#039; =&gt; &#039;l&#039;, &#039;Ń&#039; =&gt; &#039;N&#039;, &#039;ń&#039; =&gt; &#039;n&#039;, &#039;Ņ&#039; =&gt; &#039;N&#039;, &#039;ņ&#039; =&gt; &#039;n&#039;, &#039;Ň&#039; =&gt; &#039;N&#039;, &#039;ň&#039; =&gt; &#039;n&#039;, &#039;ŉ&#039; =&gt; &#039;N&#039;, &#039;Ŋ&#039; =&gt; &#039;n&#039;, &#039;ŋ&#039; =&gt; &#039;N&#039;, &#039;Ō&#039; =&gt; &#039;O&#039;, &#039;ō&#039; =&gt; &#039;o&#039;, &#039;Ŏ&#039; =&gt; &#039;O&#039;, &#039;ŏ&#039; =&gt; &#039;o&#039;, &#039;Ő&#039; =&gt; &#039;O&#039;, &#039;ő&#039; =&gt; &#039;o&#039;, &#039;Œ&#039; =&gt; &#039;OE&#039;, &#039;œ&#039; =&gt; &#039;oe&#039;, &#039;Ø&#039; =&gt; &#039;O&#039;, &#039;ø&#039; =&gt; &#039;o&#039;, &#039;Ŕ&#039; =&gt; &#039;R&#039;, &#039;ŕ&#039; =&gt; &#039;r&#039;, &#039;Ŗ&#039; =&gt; &#039;R&#039;, &#039;ŗ&#039; =&gt; &#039;r&#039;, &#039;Ř&#039; =&gt; &#039;R&#039;, &#039;ř&#039; =&gt; &#039;r&#039;, &#039;Ś&#039; =&gt; &#039;S&#039;, &#039;ś&#039; =&gt; &#039;s&#039;, &#039;Ŝ&#039; =&gt; &#039;S&#039;, &#039;ŝ&#039; =&gt; &#039;s&#039;, &#039;Ş&#039; =&gt; &#039;S&#039;, &#039;ş&#039; =&gt; &#039;s&#039;, &#039;Š&#039; =&gt; &#039;S&#039;, &#039;š&#039; =&gt; &#039;s&#039;, &#039;Ţ&#039; =&gt; &#039;T&#039;, &#039;ţ&#039; =&gt; &#039;t&#039;, &#039;Ť&#039; =&gt; &#039;T&#039;, &#039;ť&#039; =&gt; &#039;t&#039;, &#039;Ŧ&#039; =&gt; &#039;T&#039;, &#039;ŧ&#039; =&gt; &#039;t&#039;, &#039;Ũ&#039; =&gt; &#039;U&#039;, &#039;ũ&#039; =&gt; &#039;u&#039;, &#039;Ū&#039; =&gt; &#039;U&#039;, &#039;ū&#039; =&gt; &#039;u&#039;, &#039;Ŭ&#039; =&gt; &#039;U&#039;, &#039;ŭ&#039; =&gt; &#039;u&#039;, &#039;Ů&#039; =&gt; &#039;U&#039;, &#039;ů&#039; =&gt; &#039;u&#039;, &#039;Ű&#039; =&gt; &#039;U&#039;, &#039;ű&#039; =&gt; &#039;u&#039;, &#039;Ų&#039; =&gt; &#039;U&#039;, &#039;ų&#039; =&gt; &#039;u&#039;, &#039;Ŵ&#039; =&gt; &#039;W&#039;, &#039;ŵ&#039; =&gt; &#039;w&#039;, &#039;Ŷ&#039; =&gt; &#039;Y&#039;, &#039;ŷ&#039; =&gt; &#039;y&#039;, &#039;Ÿ&#039; =&gt; &#039;Y&#039;, &#039;Ź&#039; =&gt; &#039;Z&#039;, &#039;ź&#039; =&gt; &#039;z&#039;, &#039;Ż&#039; =&gt; &#039;Z&#039;, &#039;ż&#039; =&gt; &#039;z&#039;, &#039;Ž&#039; =&gt; &#039;Z&#039;, &#039;ž&#039; =&gt; &#039;z&#039;, &#039;ſ&#039; =&gt; &#039;s&#039;, &#039;€&#039; =&gt; &#039;E&#039;, &#039;£&#039; =&gt; &#039;&#039;]                            </span>
</dt>

    </dl>


<h4 id="toc-properties">
    Properties
    <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#toc-properties" class="headerlink"><i class="fas fa-link"></i></a>

</h4>
<dl class="phpdocumentor-table-of-contents">
            <dt class="phpdocumentor-table-of-contents__entry -property -private">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#property_pluralizer">$pluralizer</a>
    <span>
                        &nbsp;: <a href="classes/RectorPrefix202507-Doctrine-Inflector-WordInflector.html"><abbr title="\RectorPrefix202507\Doctrine\Inflector\WordInflector">WordInflector</abbr></a>            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -property -private">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#property_singularizer">$singularizer</a>
    <span>
                        &nbsp;: <a href="classes/RectorPrefix202507-Doctrine-Inflector-WordInflector.html"><abbr title="\RectorPrefix202507\Doctrine\Inflector\WordInflector">WordInflector</abbr></a>            </span>
</dt>

    </dl>

<h4 id="toc-methods">
    Methods
    <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#toc-methods" class="headerlink"><i class="fas fa-link"></i></a>

</h4>
<dl class="phpdocumentor-table-of-contents">
            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method___construct">__construct()</a>
    <span>
                                &nbsp;: mixed    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_camelize">camelize()</a>
    <span>
                                &nbsp;: string    </span>
</dt>
<dd>Camelizes a word. This uses the classify() method and turns the first character to lowercase.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_capitalize">capitalize()</a>
    <span>
                                &nbsp;: string    </span>
</dt>
<dd>Uppercases words with configurable delimiters between words.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_classify">classify()</a>
    <span>
                                &nbsp;: string    </span>
</dt>
<dd>Converts a word into the format for a Doctrine class name. Converts &#039;table_name&#039; to &#039;TableName&#039;.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_pluralize">pluralize()</a>
    <span>
                                &nbsp;: string    </span>
</dt>
<dd>Returns a word in plural form.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_seemsUtf8">seemsUtf8()</a>
    <span>
                                &nbsp;: bool    </span>
</dt>
<dd>Checks if the given string seems like it has utf8 characters in it.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_singularize">singularize()</a>
    <span>
                                &nbsp;: string    </span>
</dt>
<dd>Returns a word in singular form.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_tableize">tableize()</a>
    <span>
                                &nbsp;: string    </span>
</dt>
<dd>Converts a word into the format for a Doctrine table name. Converts &#039;ModelName&#039; to &#039;model_name&#039;.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_unaccent">unaccent()</a>
    <span>
                                &nbsp;: string    </span>
</dt>
<dd>Remove any illegal characters, accents, etc.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a class="" href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_urlize">urlize()</a>
    <span>
                                &nbsp;: string    </span>
</dt>
<dd>Convert any passed string to a url friendly string.</dd>

    </dl>



        
    <section class="phpdocumentor-constants">
        <h3 class="phpdocumentor-elements__header" id="constants">
            Constants
            <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#constants" class="headerlink"><i class="fas fa-link"></i></a>

        </h3>
                    <article class="phpdocumentor-element -constant -private ">
    <h4 class="phpdocumentor-element__name" id="constant_ACCENTED_CHARACTERS">
        ACCENTED_CHARACTERS
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#constant_ACCENTED_CHARACTERS" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">23</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">private</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">ACCENTED_CHARACTERS</span>
    = <span class="phpdocumentor-signature__default-value">[&#039;À&#039; =&gt; &#039;A&#039;, &#039;Á&#039; =&gt; &#039;A&#039;, &#039;Â&#039; =&gt; &#039;A&#039;, &#039;Ã&#039; =&gt; &#039;A&#039;, &#039;Ä&#039; =&gt; &#039;Ae&#039;, &#039;Æ&#039; =&gt; &#039;Ae&#039;, &#039;Å&#039; =&gt; &#039;Aa&#039;, &#039;æ&#039; =&gt; &#039;a&#039;, &#039;Ç&#039; =&gt; &#039;C&#039;, &#039;È&#039; =&gt; &#039;E&#039;, &#039;É&#039; =&gt; &#039;E&#039;, &#039;Ê&#039; =&gt; &#039;E&#039;, &#039;Ë&#039; =&gt; &#039;E&#039;, &#039;Ì&#039; =&gt; &#039;I&#039;, &#039;Í&#039; =&gt; &#039;I&#039;, &#039;Î&#039; =&gt; &#039;I&#039;, &#039;Ï&#039; =&gt; &#039;I&#039;, &#039;Ñ&#039; =&gt; &#039;N&#039;, &#039;Ò&#039; =&gt; &#039;O&#039;, &#039;Ó&#039; =&gt; &#039;O&#039;, &#039;Ô&#039; =&gt; &#039;O&#039;, &#039;Õ&#039; =&gt; &#039;O&#039;, &#039;Ö&#039; =&gt; &#039;Oe&#039;, &#039;Ù&#039; =&gt; &#039;U&#039;, &#039;Ú&#039; =&gt; &#039;U&#039;, &#039;Û&#039; =&gt; &#039;U&#039;, &#039;Ü&#039; =&gt; &#039;Ue&#039;, &#039;Ý&#039; =&gt; &#039;Y&#039;, &#039;ß&#039; =&gt; &#039;ss&#039;, &#039;à&#039; =&gt; &#039;a&#039;, &#039;á&#039; =&gt; &#039;a&#039;, &#039;â&#039; =&gt; &#039;a&#039;, &#039;ã&#039; =&gt; &#039;a&#039;, &#039;ä&#039; =&gt; &#039;ae&#039;, &#039;å&#039; =&gt; &#039;aa&#039;, &#039;ç&#039; =&gt; &#039;c&#039;, &#039;è&#039; =&gt; &#039;e&#039;, &#039;é&#039; =&gt; &#039;e&#039;, &#039;ê&#039; =&gt; &#039;e&#039;, &#039;ë&#039; =&gt; &#039;e&#039;, &#039;ì&#039; =&gt; &#039;i&#039;, &#039;í&#039; =&gt; &#039;i&#039;, &#039;î&#039; =&gt; &#039;i&#039;, &#039;ï&#039; =&gt; &#039;i&#039;, &#039;ñ&#039; =&gt; &#039;n&#039;, &#039;ò&#039; =&gt; &#039;o&#039;, &#039;ó&#039; =&gt; &#039;o&#039;, &#039;ô&#039; =&gt; &#039;o&#039;, &#039;õ&#039; =&gt; &#039;o&#039;, &#039;ö&#039; =&gt; &#039;oe&#039;, &#039;ù&#039; =&gt; &#039;u&#039;, &#039;ú&#039; =&gt; &#039;u&#039;, &#039;û&#039; =&gt; &#039;u&#039;, &#039;ü&#039; =&gt; &#039;ue&#039;, &#039;ý&#039; =&gt; &#039;y&#039;, &#039;ÿ&#039; =&gt; &#039;y&#039;, &#039;Ā&#039; =&gt; &#039;A&#039;, &#039;ā&#039; =&gt; &#039;a&#039;, &#039;Ă&#039; =&gt; &#039;A&#039;, &#039;ă&#039; =&gt; &#039;a&#039;, &#039;Ą&#039; =&gt; &#039;A&#039;, &#039;ą&#039; =&gt; &#039;a&#039;, &#039;Ć&#039; =&gt; &#039;C&#039;, &#039;ć&#039; =&gt; &#039;c&#039;, &#039;Ĉ&#039; =&gt; &#039;C&#039;, &#039;ĉ&#039; =&gt; &#039;c&#039;, &#039;Ċ&#039; =&gt; &#039;C&#039;, &#039;ċ&#039; =&gt; &#039;c&#039;, &#039;Č&#039; =&gt; &#039;C&#039;, &#039;č&#039; =&gt; &#039;c&#039;, &#039;Ď&#039; =&gt; &#039;D&#039;, &#039;ď&#039; =&gt; &#039;d&#039;, &#039;Đ&#039; =&gt; &#039;D&#039;, &#039;đ&#039; =&gt; &#039;d&#039;, &#039;Ē&#039; =&gt; &#039;E&#039;, &#039;ē&#039; =&gt; &#039;e&#039;, &#039;Ĕ&#039; =&gt; &#039;E&#039;, &#039;ĕ&#039; =&gt; &#039;e&#039;, &#039;Ė&#039; =&gt; &#039;E&#039;, &#039;ė&#039; =&gt; &#039;e&#039;, &#039;Ę&#039; =&gt; &#039;E&#039;, &#039;ę&#039; =&gt; &#039;e&#039;, &#039;Ě&#039; =&gt; &#039;E&#039;, &#039;ě&#039; =&gt; &#039;e&#039;, &#039;Ĝ&#039; =&gt; &#039;G&#039;, &#039;ĝ&#039; =&gt; &#039;g&#039;, &#039;Ğ&#039; =&gt; &#039;G&#039;, &#039;ğ&#039; =&gt; &#039;g&#039;, &#039;Ġ&#039; =&gt; &#039;G&#039;, &#039;ġ&#039; =&gt; &#039;g&#039;, &#039;Ģ&#039; =&gt; &#039;G&#039;, &#039;ģ&#039; =&gt; &#039;g&#039;, &#039;Ĥ&#039; =&gt; &#039;H&#039;, &#039;ĥ&#039; =&gt; &#039;h&#039;, &#039;Ħ&#039; =&gt; &#039;H&#039;, &#039;ħ&#039; =&gt; &#039;h&#039;, &#039;Ĩ&#039; =&gt; &#039;I&#039;, &#039;ĩ&#039; =&gt; &#039;i&#039;, &#039;Ī&#039; =&gt; &#039;I&#039;, &#039;ī&#039; =&gt; &#039;i&#039;, &#039;Ĭ&#039; =&gt; &#039;I&#039;, &#039;ĭ&#039; =&gt; &#039;i&#039;, &#039;Į&#039; =&gt; &#039;I&#039;, &#039;į&#039; =&gt; &#039;i&#039;, &#039;İ&#039; =&gt; &#039;I&#039;, &#039;ı&#039; =&gt; &#039;i&#039;, &#039;Ĳ&#039; =&gt; &#039;IJ&#039;, &#039;ĳ&#039; =&gt; &#039;ij&#039;, &#039;Ĵ&#039; =&gt; &#039;J&#039;, &#039;ĵ&#039; =&gt; &#039;j&#039;, &#039;Ķ&#039; =&gt; &#039;K&#039;, &#039;ķ&#039; =&gt; &#039;k&#039;, &#039;ĸ&#039; =&gt; &#039;k&#039;, &#039;Ĺ&#039; =&gt; &#039;L&#039;, &#039;ĺ&#039; =&gt; &#039;l&#039;, &#039;Ļ&#039; =&gt; &#039;L&#039;, &#039;ļ&#039; =&gt; &#039;l&#039;, &#039;Ľ&#039; =&gt; &#039;L&#039;, &#039;ľ&#039; =&gt; &#039;l&#039;, &#039;Ŀ&#039; =&gt; &#039;L&#039;, &#039;ŀ&#039; =&gt; &#039;l&#039;, &#039;Ł&#039; =&gt; &#039;L&#039;, &#039;ł&#039; =&gt; &#039;l&#039;, &#039;Ń&#039; =&gt; &#039;N&#039;, &#039;ń&#039; =&gt; &#039;n&#039;, &#039;Ņ&#039; =&gt; &#039;N&#039;, &#039;ņ&#039; =&gt; &#039;n&#039;, &#039;Ň&#039; =&gt; &#039;N&#039;, &#039;ň&#039; =&gt; &#039;n&#039;, &#039;ŉ&#039; =&gt; &#039;N&#039;, &#039;Ŋ&#039; =&gt; &#039;n&#039;, &#039;ŋ&#039; =&gt; &#039;N&#039;, &#039;Ō&#039; =&gt; &#039;O&#039;, &#039;ō&#039; =&gt; &#039;o&#039;, &#039;Ŏ&#039; =&gt; &#039;O&#039;, &#039;ŏ&#039; =&gt; &#039;o&#039;, &#039;Ő&#039; =&gt; &#039;O&#039;, &#039;ő&#039; =&gt; &#039;o&#039;, &#039;Œ&#039; =&gt; &#039;OE&#039;, &#039;œ&#039; =&gt; &#039;oe&#039;, &#039;Ø&#039; =&gt; &#039;O&#039;, &#039;ø&#039; =&gt; &#039;o&#039;, &#039;Ŕ&#039; =&gt; &#039;R&#039;, &#039;ŕ&#039; =&gt; &#039;r&#039;, &#039;Ŗ&#039; =&gt; &#039;R&#039;, &#039;ŗ&#039; =&gt; &#039;r&#039;, &#039;Ř&#039; =&gt; &#039;R&#039;, &#039;ř&#039; =&gt; &#039;r&#039;, &#039;Ś&#039; =&gt; &#039;S&#039;, &#039;ś&#039; =&gt; &#039;s&#039;, &#039;Ŝ&#039; =&gt; &#039;S&#039;, &#039;ŝ&#039; =&gt; &#039;s&#039;, &#039;Ş&#039; =&gt; &#039;S&#039;, &#039;ş&#039; =&gt; &#039;s&#039;, &#039;Š&#039; =&gt; &#039;S&#039;, &#039;š&#039; =&gt; &#039;s&#039;, &#039;Ţ&#039; =&gt; &#039;T&#039;, &#039;ţ&#039; =&gt; &#039;t&#039;, &#039;Ť&#039; =&gt; &#039;T&#039;, &#039;ť&#039; =&gt; &#039;t&#039;, &#039;Ŧ&#039; =&gt; &#039;T&#039;, &#039;ŧ&#039; =&gt; &#039;t&#039;, &#039;Ũ&#039; =&gt; &#039;U&#039;, &#039;ũ&#039; =&gt; &#039;u&#039;, &#039;Ū&#039; =&gt; &#039;U&#039;, &#039;ū&#039; =&gt; &#039;u&#039;, &#039;Ŭ&#039; =&gt; &#039;U&#039;, &#039;ŭ&#039; =&gt; &#039;u&#039;, &#039;Ů&#039; =&gt; &#039;U&#039;, &#039;ů&#039; =&gt; &#039;u&#039;, &#039;Ű&#039; =&gt; &#039;U&#039;, &#039;ű&#039; =&gt; &#039;u&#039;, &#039;Ų&#039; =&gt; &#039;U&#039;, &#039;ų&#039; =&gt; &#039;u&#039;, &#039;Ŵ&#039; =&gt; &#039;W&#039;, &#039;ŵ&#039; =&gt; &#039;w&#039;, &#039;Ŷ&#039; =&gt; &#039;Y&#039;, &#039;ŷ&#039; =&gt; &#039;y&#039;, &#039;Ÿ&#039; =&gt; &#039;Y&#039;, &#039;Ź&#039; =&gt; &#039;Z&#039;, &#039;ź&#039; =&gt; &#039;z&#039;, &#039;Ż&#039; =&gt; &#039;Z&#039;, &#039;ż&#039; =&gt; &#039;z&#039;, &#039;Ž&#039; =&gt; &#039;Z&#039;, &#039;ž&#039; =&gt; &#039;z&#039;, &#039;ſ&#039; =&gt; &#039;s&#039;, &#039;€&#039; =&gt; &#039;E&#039;, &#039;£&#039; =&gt; &#039;&#039;]</span>
</code>


    
    
    

    

</article>
            </section>

        
    <section class="phpdocumentor-properties">
        <h3 class="phpdocumentor-elements__header" id="properties">
            Properties
            <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#properties" class="headerlink"><i class="fas fa-link"></i></a>

        </h3>
                    <article
        class="
            phpdocumentor-element
            -property
            -private
                                                                    "
>
    <h4 class="phpdocumentor-element__name" id="property_pluralizer">
        $pluralizer
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#property_pluralizer" class="headerlink"><i class="fas fa-link"></i></a>

        <span class="phpdocumentor-element__modifiers">
                                            </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">27</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
        <span class="phpdocumentor-signature__visibility">private</span>
            <span class="phpdocumentor-signature__type"><a href="classes/RectorPrefix202507-Doctrine-Inflector-WordInflector.html"><abbr title="\RectorPrefix202507\Doctrine\Inflector\WordInflector">WordInflector</abbr></a></span>
    <span class="phpdocumentor-signature__name">$pluralizer</span>
    </code>

    
    
    

    

    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -private
                                                                    "
>
    <h4 class="phpdocumentor-element__name" id="property_singularizer">
        $singularizer
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#property_singularizer" class="headerlink"><i class="fas fa-link"></i></a>

        <span class="phpdocumentor-element__modifiers">
                                            </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">25</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
        <span class="phpdocumentor-signature__visibility">private</span>
            <span class="phpdocumentor-signature__type"><a href="classes/RectorPrefix202507-Doctrine-Inflector-WordInflector.html"><abbr title="\RectorPrefix202507\Doctrine\Inflector\WordInflector">WordInflector</abbr></a></span>
    <span class="phpdocumentor-signature__name">$singularizer</span>
    </code>

    
    
    

    

    

</article>
            </section>

            <section class="phpdocumentor-methods">
        <h3 class="phpdocumentor-elements__header" id="methods">
            Methods
            <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#methods" class="headerlink"><i class="fas fa-link"></i></a>

        </h3>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method___construct">
        __construct()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method___construct" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">28</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">__construct</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href="classes/RectorPrefix202507-Doctrine-Inflector-WordInflector.html"><abbr title="\RectorPrefix202507\Doctrine\Inflector\WordInflector">WordInflector</abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$singularizer</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type"><a href="classes/RectorPrefix202507-Doctrine-Inflector-WordInflector.html"><abbr title="\RectorPrefix202507\Doctrine\Inflector\WordInflector">WordInflector</abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$pluralizer</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$singularizer</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href="classes/RectorPrefix202507-Doctrine-Inflector-WordInflector.html"><abbr title="\RectorPrefix202507\Doctrine\Inflector\WordInflector">WordInflector</abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$pluralizer</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href="classes/RectorPrefix202507-Doctrine-Inflector-WordInflector.html"><abbr title="\RectorPrefix202507\Doctrine\Inflector\WordInflector">WordInflector</abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_camelize">
        camelize()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_camelize" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">54</span>

    </aside>

        <p class="phpdocumentor-summary">Camelizes a word. This uses the classify() method and turns the first character to lowercase.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">camelize</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$word</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$word</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_capitalize">
        capitalize()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_capitalize" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">83</span>

    </aside>

        <p class="phpdocumentor-summary">Uppercases words with configurable delimiters between words.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">capitalize</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$delimiters</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&quot; 
	
 -&quot;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
        <section class="phpdocumentor-description"><p>Takes a string and capitalizes all of the words, like PHP's built-in
ucwords function. This extends that behavior, however, by allowing the
word delimiters to be configured, rather than only separating on
whitespace.</p>
<p>Here is an example:
<code class="prettyprint"></p>
<?php
$string = 'top-o-the-morning to all_of_you!';
echo $inflector->capitalize($string);
// Top-O-The-Morning To All_of_you!

echo $inflector->capitalize($string, '-_ ');
// Top-O-The-Morning To All_Of_You!
?>
</code>
</section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>The string to operate on.</p>
</section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$delimiters</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&quot; 
	
 -&quot;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>A list of word separators.</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
                    &mdash;
                <section class="phpdocumentor-description"><p>The string with all delimiter-separated words capitalized.</p>
</section>

            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_classify">
        classify()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_classify" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">47</span>

    </aside>

        <p class="phpdocumentor-summary">Converts a word into the format for a Doctrine class name. Converts &#039;table_name&#039; to &#039;TableName&#039;.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">classify</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$word</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$word</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_pluralize">
        pluralize()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_pluralize" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">200</span>

    </aside>

        <p class="phpdocumentor-summary">Returns a word in plural form.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">pluralize</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$word</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$word</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>The word in singular form.</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
                    &mdash;
                <section class="phpdocumentor-description"><p>The word in plural form.</p>
</section>

            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_seemsUtf8">
        seemsUtf8()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_seemsUtf8" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">92</span>

    </aside>

        <p class="phpdocumentor-summary">Checks if the given string seems like it has utf8 characters in it.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">seemsUtf8</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>The string to check for utf8 characters in.</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_singularize">
        singularize()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_singularize" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">189</span>

    </aside>

        <p class="phpdocumentor-summary">Returns a word in singular form.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">singularize</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$word</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$word</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>The word in plural form.</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
                    &mdash;
                <section class="phpdocumentor-description"><p>The word in singular form.</p>
</section>

            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_tableize">
        tableize()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_tableize" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">36</span>

    </aside>

        <p class="phpdocumentor-summary">Converts a word into the format for a Doctrine table name. Converts &#039;ModelName&#039; to &#039;model_name&#039;.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">tableize</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$word</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$word</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_unaccent">
        unaccent()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_unaccent" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">134</span>

    </aside>

        <p class="phpdocumentor-summary">Remove any illegal characters, accents, etc.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">unaccent</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>String to unaccent</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
                    &mdash;
                <section class="phpdocumentor-description"><p>Unaccented string</p>
</section>

            </section>

</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_urlize">
        urlize()
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_urlize" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php"><a href="files/vendor-rector-rector-vendor-doctrine-inflector-lib-doctrine-inflector-inflector.html"><abbr title="vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php">Inflector.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">162</span>

    </aside>

        <p class="phpdocumentor-summary">Convert any passed string to a url friendly string.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                    <span class="phpdocumentor-signature__name">urlize</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    <div class="phpdocumentor-label-line">
        </div>
    
        <section class="phpdocumentor-description"><p>Converts 'My first blog post' to 'my-first-blog-post'</p>
</section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"><p>String to urlize.</p>
</section>

            </dd>
            </dl>

    

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
                    &mdash;
                <section class="phpdocumentor-description"><p>Urlized string.</p>
</section>

            </section>

</article>
            </section>

        <div class="phpdocumentor-modal" id="source-view">
    <div class="phpdocumentor-modal-bg" data-exit-button></div>
    <div class="phpdocumentor-modal-container">
        <div class="phpdocumentor-modal-content">
            <pre style="max-height: 500px; overflow-y: scroll" data-src="files/vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php.txt" class="language-php line-numbers linkable-line-numbers"></pre>
        </div>
        <button data-exit-button class="phpdocumentor-modal__close">&times;</button>
    </div>
</div>

    <script type="text/javascript">
        (function () {
            function loadExternalCodeSnippet(el, url, line) {
                Array.prototype.slice.call(el.querySelectorAll('pre[data-src]')).forEach((pre) => {
                    const src = url || pre.getAttribute('data-src').replace(/\\/g, '/');
                    const language = 'php';

                    const code = document.createElement('code');
                    code.className = 'language-' + language;
                    pre.textContent = '';
                    pre.setAttribute('data-line', line)
                    code.textContent = 'Loading…';
                    pre.appendChild(code);

                    var xhr = new XMLHttpRequest();

                    xhr.open('GET', src, true);

                    xhr.onreadystatechange = function () {
                        if (xhr.readyState !== 4) {
                            return;
                        }

                        if (xhr.status < 400 && xhr.responseText) {
                            code.textContent = xhr.responseText;
                            Prism.highlightElement(code);
                            d=document.getElementsByClassName("line-numbers");
                            d[0].scrollTop = d[0].children[1].offsetTop;
                            return;
                        }

                        if (xhr.status === 404) {
                            code.textContent = '✖ Error: File could not be found';
                            return;
                        }

                        if (xhr.status >= 400) {
                            code.textContent = '✖ Error ' + xhr.status + ' while fetching file: ' + xhr.statusText;
                            return;
                        }

                        code.textContent = '✖ Error: An unknown error occurred';
                    };

                    xhr.send(null);
                });
            }

            const modalButtons = document.querySelectorAll("[data-modal]");
            const openedAsLocalFile = window.location.protocol === 'file:';
            if (modalButtons.length > 0 && openedAsLocalFile) {
                console.warn(
                    'Viewing the source code is unavailable because you are opening this page from the file:// scheme; ' +
                    'browsers block XHR requests when a page is opened this way'
                );
            }

            modalButtons.forEach(function (trigger) {
                if (openedAsLocalFile) {
                    trigger.setAttribute("hidden", "hidden");
                }

                trigger.addEventListener("click", function (event) {
                    event.preventDefault();
                    const modal = document.getElementById(trigger.dataset.modal);
                    if (!modal) {
                        console.error(`Modal with id "${trigger.dataset.modal}" could not be found`);
                        return;
                    }
                    modal.classList.add("phpdocumentor-modal__open");

                    loadExternalCodeSnippet(modal, trigger.dataset.src || null, trigger.dataset.line)
                    const exits = modal.querySelectorAll("[data-exit-button]");
                    exits.forEach(function (exit) {
                        exit.addEventListener("click", function (event) {
                            event.preventDefault();
                            modal.classList.remove("phpdocumentor-modal__open");
                        });
                    });
                });
            });
        })();
    </script>

    </article>
                                </section>
                <section class="phpdocumentor-on-this-page__sidebar">
                                
    <section class="phpdocumentor-on-this-page__content">
        <strong class="phpdocumentor-on-this-page__title">On this page</strong>

        <ul class="phpdocumentor-list -clean">
            <li class="phpdocumentor-on-this-page-section__title">Table Of Contents</li>
            <li>
                <ul class="phpdocumentor-list -clean">
                                        <li><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#toc-constants">Constants</a></li>
                                                            <li><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#toc-properties">Properties</a></li>
                                                            <li><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#toc-methods">Methods</a></li>
                                    </ul>
            </li>
                            <li class="phpdocumentor-on-this-page-section__title">Constants</li>
                <li>
                    <ul class="phpdocumentor-list -clean">
                                                    <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#constant_ACCENTED_CHARACTERS">ACCENTED_CHARACTERS</a></li>
                                            </ul>
                </li>
            
                            <li class="phpdocumentor-on-this-page-section__title">Properties</li>
                <li>
                    <ul class="phpdocumentor-list -clean">
                                                    <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#property_pluralizer">$pluralizer</a></li>
                                                    <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#property_singularizer">$singularizer</a></li>
                                            </ul>
                </li>
            
                        <li class="phpdocumentor-on-this-page-section__title">Methods</li>
            <li>
                <ul class="phpdocumentor-list -clean">
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method___construct">__construct()</a></li>
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_camelize">camelize()</a></li>
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_capitalize">capitalize()</a></li>
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_classify">classify()</a></li>
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_pluralize">pluralize()</a></li>
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_seemsUtf8">seemsUtf8()</a></li>
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_singularize">singularize()</a></li>
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_tableize">tableize()</a></li>
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_unaccent">unaccent()</a></li>
                                            <li class=""><a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#method_urlize">urlize()</a></li>
                                    </ul>
            </li>
                    </ul>
    </section>

                </section>
                            </div>
            <section data-search-results class="phpdocumentor-search-results phpdocumentor-search-results--hidden">
    <section class="phpdocumentor-search-results__dialog">
        <header class="phpdocumentor-search-results__header">
            <h2 class="phpdocumentor-search-results__title">Search results</h2>
            <button class="phpdocumentor-search-results__close"><i class="fas fa-times"></i></button>
        </header>
        <section class="phpdocumentor-search-results__body">
            <ul class="phpdocumentor-search-results__entries"></ul>
        </section>
    </section>
</section>
        </div>
        <a href="classes/RectorPrefix202507-Doctrine-Inflector-Inflector.html#top" class="phpdocumentor-back-to-top"><i class="fas fa-chevron-circle-up"></i></a>

    </main>

    <script>
        cssVars({});
    </script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-highlight/prism-line-highlight.min.js"></script>
</body>
</html>
