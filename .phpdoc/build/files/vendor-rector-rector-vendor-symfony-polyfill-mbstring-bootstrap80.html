<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
            <title>Documentation</title>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="../">
    <link rel="icon" href="images/favicon.ico"/>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/base.css">
            <link rel="preconnect" href="https://fonts.gstatic.com">
        <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@100;200;300;400;600;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Source+Code+Pro:wght@400;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="css/template.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/css/all.min.css" integrity="sha256-ybRkN9dBjhcS2qrW1z+hfCxq+1aBdwyQM5wlQoQVt/0=" crossorigin="anonymous" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/themes/prism-okaidia.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-numbers/prism-line-numbers.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-highlight/prism-line-highlight.css">
                <script src="https://cdn.jsdelivr.net/npm/fuse.js@3.4.6"></script>
        <script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2"></script>
        <script src="js/template.js"></script>
        <script src="js/search.js"></script>
        <script defer src="js/searchIndex.js"></script>
    </head>
<body id="top">
    <header class="phpdocumentor-header phpdocumentor-section">
    <h1 class="phpdocumentor-title"><a href="" class="phpdocumentor-title__link">Documentation</a></h1>
    <input class="phpdocumentor-header__menu-button" type="checkbox" id="menu-button" name="menu-button" />
    <label class="phpdocumentor-header__menu-icon" for="menu-button">
        <i class="fas fa-bars"></i>
    </label>
    <section data-search-form class="phpdocumentor-search">
    <label>
        <span class="visually-hidden">Search for</span>
        <svg class="phpdocumentor-search__icon" width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="7.5" cy="7.5" r="6.5" stroke="currentColor" stroke-width="2"/>
            <line x1="12.4892" y1="12.2727" x2="19.1559" y2="18.9393" stroke="currentColor" stroke-width="3"/>
        </svg>
        <input type="search" class="phpdocumentor-field phpdocumentor-search__field" placeholder="Loading .." disabled />
    </label>
</section>

    <nav class="phpdocumentor-topnav">
    <ul class="phpdocumentor-topnav__menu">
        </ul>
</nav>
</header>

    <main class="phpdocumentor">
        <div class="phpdocumentor-section">
            <input class="phpdocumentor-sidebar__menu-button" type="checkbox" id="sidebar-button" name="sidebar-button" />
<label class="phpdocumentor-sidebar__menu-icon" for="sidebar-button">
    Menu
</label>
<aside class="phpdocumentor-column -three phpdocumentor-sidebar">
                    <section class="phpdocumentor-sidebar__category -namespaces">
            <h2 class="phpdocumentor-sidebar__category-header">Namespaces</h2>
                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/app.html" class="">App</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/app-commands.html" class="">Commands</a>
                
            </li>
                    <li>
                <a href="namespaces/app-providers.html" class="">Providers</a>
                
            </li>
                    <li>
                <a href="namespaces/app-services.html" class="">Services</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/tests.html" class="">Tests</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/illuminate.html" class="">Illuminate</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/illuminate-bus.html" class="">Bus</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-console.html" class="">Console</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-redis.html" class="">Redis</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-cache.html" class="">Cache</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-support.html" class="">Support</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-config.html" class="">Config</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-container.html" class="">Container</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-contracts.html" class="">Contracts</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-database.html" class="">Database</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-events.html" class="">Events</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-filesystem.html" class="">Filesystem</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-http.html" class="">Http</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-pipeline.html" class="">Pipeline</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-process.html" class="">Process</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-session.html" class="">Session</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-testing.html" class="">Testing</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-view.html" class="">View</a>
                
            </li>
                    <li>
                <a href="namespaces/illuminate-foundation.html" class="">Foundation</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/paratest.html" class="">ParaTest</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/brick.html" class="">Brick</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/brick-math.html" class="">Math</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/composer.html" class="">Composer</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/composer-autoload.html" class="">Autoload</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/doctrine.html" class="">Doctrine</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/doctrine-deprecations.html" class="">Deprecations</a>
                
            </li>
                    <li>
                <a href="namespaces/doctrine-inflector.html" class="">Inflector</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/cron.html" class="">Cron</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rectorlaravel.html" class="">RectorLaravel</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/rectorlaravel-commands.html" class="">Commands</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-contract.html" class="">Contract</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-nodeanalyzer.html" class="">NodeAnalyzer</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-nodefactory.html" class="">NodeFactory</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-rector.html" class="">Rector</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-set.html" class="">Set</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorlaravel-valueobject.html" class="">ValueObject</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/ergebnis.html" class="">Ergebnis</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/ergebnis-composer.html" class="">Composer</a>
                
            </li>
                    <li>
                <a href="namespaces/ergebnis-json.html" class="">Json</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/fidry.html" class="">Fidry</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/fidry-cpucorecounter.html" class="">CpuCoreCounter</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/whoops.html" class="">Whoops</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/whoops-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/whoops-handler.html" class="">Handler</a>
                
            </li>
                    <li>
                <a href="namespaces/whoops-inspector.html" class="">Inspector</a>
                
            </li>
                    <li>
                <a href="namespaces/whoops-util.html" class="">Util</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/fruitcake.html" class="">Fruitcake</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/fruitcake-cors.html" class="">Cors</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/grahamcampbell.html" class="">GrahamCampbell</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/grahamcampbell-resulttype.html" class="">ResultType</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/guzzlehttp.html" class="">GuzzleHttp</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/guzzlehttp-cookie.html" class="">Cookie</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-handler.html" class="">Handler</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-promise.html" class="">Promise</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-psr7.html" class="">Psr7</a>
                
            </li>
                    <li>
                <a href="namespaces/guzzlehttp-uritemplate.html" class="">UriTemplate</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/hamcrest.html" class="">Hamcrest</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/hamcrest-arrays.html" class="">Arrays</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-collection.html" class="">Collection</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-core.html" class="">Core</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-internal.html" class="">Internal</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-number.html" class="">Number</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-text.html" class="">Text</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-type.html" class="">Type</a>
                
            </li>
                    <li>
                <a href="namespaces/hamcrest-xml.html" class="">Xml</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/iamcal.html" class="">iamcal</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/jean85.html" class="">Jean85</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/jean85-exception.html" class="">Exception</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/joli.html" class="">Joli</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/joli-jolinotif.html" class="">JoliNotif</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phar.html" class="">phar</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/qa.html" class="">qa</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/qa-cs.html" class="">cs</a>
                
            </li>
                    <li>
                <a href="namespaces/qa-phpstan.html" class="">phpstan</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/jolicode.html" class="">JoliCode</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/jolicode-phposhelper.html" class="">PhpOsHelper</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/jsonschema.html" class="">JsonSchema</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/jsonschema-constraints.html" class="">Constraints</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-entity.html" class="">Entity</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-iterator.html" class="">Iterator</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-tool.html" class="">Tool</a>
                
            </li>
                    <li>
                <a href="namespaces/jsonschema-uri.html" class="">Uri</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/larastan.html" class="">Larastan</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/larastan-larastan.html" class="">Larastan</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/laravel.html" class="">Laravel</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/laravel-prompts.html" class="">Prompts</a>
                
            </li>
                    <li>
                <a href="namespaces/laravel-serializableclosure.html" class="">SerializableClosure</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/database.html" class="">Database</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/database-seeders.html" class="">Seeders</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/laravelzero.html" class="">LaravelZero</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/laravelzero-framework.html" class="">Framework</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/league.html" class="">League</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/league-flysystem.html" class="">Flysystem</a>
                
            </li>
                    <li>
                <a href="namespaces/league-mimetypedetection.html" class="">MimeTypeDetection</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/localheinz.html" class="">Localheinz</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/localheinz-diff.html" class="">Diff</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/mabeenum.html" class="">MabeEnum</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/masterminds.html" class="">Masterminds</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/masterminds-html5.html" class="">HTML5</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/mockery.html" class="">Mockery</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/mockery-adapter.html" class="">Adapter</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-countvalidator.html" class="">CountValidator</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-generator.html" class="">Generator</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-loader.html" class="">Loader</a>
                
            </li>
                    <li>
                <a href="namespaces/mockery-matcher.html" class="">Matcher</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/deepcopy.html" class="">DeepCopy</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/deepcopy-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-filter.html" class="">Filter</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-matcher.html" class="">Matcher</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-reflection.html" class="">Reflection</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-typefilter.html" class="">TypeFilter</a>
                
            </li>
                    <li>
                <a href="namespaces/deepcopy-typematcher.html" class="">TypeMatcher</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/carbon.html" class="">Carbon</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/carbon-doctrine.html" class="">Doctrine</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-messageformatter.html" class="">MessageFormatter</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-cli.html" class="">Cli</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-exceptions.html" class="">Exceptions</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-laravel.html" class="">Laravel</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-phpstan.html" class="">PHPStan</a>
                
            </li>
                    <li>
                <a href="namespaces/carbon-traits.html" class="">Traits</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/nette.html" class="">Nette</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/nette-utils.html" class="">Utils</a>
                
            </li>
                    <li>
                <a href="namespaces/nette-localization.html" class="">Localization</a>
                
            </li>
                    <li>
                <a href="namespaces/nette-iterators.html" class="">Iterators</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpparser.html" class="">PhpParser</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpparser-builder.html" class="">Builder</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-comment.html" class="">Comment</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-errorhandler.html" class="">ErrorHandler</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-lexer.html" class="">Lexer</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-node.html" class="">Node</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-nodevisitor.html" class="">NodeVisitor</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-parser.html" class="">Parser</a>
                
            </li>
                    <li>
                <a href="namespaces/phpparser-prettyprinter.html" class="">PrettyPrinter</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/nunomaduro.html" class="">NunoMaduro</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/nunomaduro-collision.html" class="">Collision</a>
                
            </li>
                    <li>
                <a href="namespaces/nunomaduro-laravelconsolesummary.html" class="">LaravelConsoleSummary</a>
                
            </li>
                    <li>
                <a href="namespaces/nunomaduro-laravelconsoletask.html" class="">LaravelConsoleTask</a>
                
            </li>
                    <li>
                <a href="namespaces/nunomaduro-laraveldesktopnotifier.html" class="">LaravelDesktopNotifier</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/termwind.html" class="">Termwind</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/termwind-components.html" class="">Components</a>
                
            </li>
                    <li>
                <a href="namespaces/termwind-enums.html" class="">Enums</a>
                
            </li>
                    <li>
                <a href="namespaces/termwind-laravel.html" class="">Laravel</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/peck.html" class="">Peck</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/peck-plugins.html" class="">Plugins</a>
                
            </li>
                    <li>
                <a href="namespaces/peck-services.html" class="">Services</a>
                
            </li>
                    <li>
                <a href="namespaces/peck-support.html" class="">Support</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/pest.html" class="">Pest</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/pest-configuration.html" class="">Configuration</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-contracts.html" class="">Contracts</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-exceptions.html" class="">Exceptions</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-factories.html" class="">Factories</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-plugins.html" class="">Plugins</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-support.html" class="">Support</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-testcasefilters.html" class="">TestCaseFilters</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-testcasemethodfilters.html" class="">TestCaseMethodFilters</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-arch.html" class="">Arch</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-mutate.html" class="">Mutate</a>
                
            </li>
                    <li>
                <a href="namespaces/pest-stressless.html" class="">Stressless</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phario.html" class="">PharIo</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phario-manifest.html" class="">Manifest</a>
                
            </li>
                    <li>
                <a href="namespaces/phario-csfixer.html" class="">CSFixer</a>
                
            </li>
                    <li>
                <a href="namespaces/phario-version.html" class="">Version</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpdocumentor.html" class="">phpDocumentor</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpdocumentor-reflection.html" class="">Reflection</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpoption.html" class="">PhpOption</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpstan.html" class="">PHPStan</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpstan-phpdocparser.html" class="">PhpDocParser</a>
                
            </li>
                    <li>
                <a href="namespaces/phpstan-dependencyinjection.html" class="">DependencyInjection</a>
                
            </li>
                    <li>
                <a href="namespaces/phpstan-rules.html" class="">Rules</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/phpunit.html" class="">PHPUnit</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/phpunit-runner.html" class="">Runner</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-textui.html" class="">TextUI</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-event.html" class="">Event</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-framework.html" class="">Framework</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-metadata.html" class="">Metadata</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-util.html" class="">Util</a>
                
            </li>
                    <li>
                <a href="namespaces/phpunit-architecture.html" class="">Architecture</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/psr.html" class="">Psr</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/psr-clock.html" class="">Clock</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-container.html" class="">Container</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-eventdispatcher.html" class="">EventDispatcher</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-http.html" class="">Http</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-log.html" class="">Log</a>
                
            </li>
                    <li>
                <a href="namespaces/psr-simplecache.html" class="">SimpleCache</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/ramsey.html" class="">Ramsey</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/ramsey-collection.html" class="">Collection</a>
                
            </li>
                    <li>
                <a href="namespaces/ramsey-uuid.html" class="">Uuid</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rectorprefix202507.html" class="">RectorPrefix202507</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/rectorprefix202507-clue.html" class="">Clue</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-composer.html" class="">Composer</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-doctrine.html" class="">Doctrine</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-evenement.html" class="">Evenement</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-fidry.html" class="">Fidry</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-illuminate.html" class="">Illuminate</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-nette.html" class="">Nette</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-ondram.html" class="">OndraM</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-psr.html" class="">Psr</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-react.html" class="">React</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-symfony.html" class="">Symfony</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-sebastianbergmann.html" class="">SebastianBergmann</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-symplify.html" class="">Symplify</a>
                
            </li>
                    <li>
                <a href="namespaces/rectorprefix202507-webmozart.html" class="">Webmozart</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rectorprefix20220529.html" class="">RectorPrefix20220529</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/rector.html" class="">Rector</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/rector-arguments.html" class="">Arguments</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-carbon.html" class="">Carbon</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-codequality.html" class="">CodeQuality</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-codingstyle.html" class="">CodingStyle</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-deadcode.html" class="">DeadCode</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-earlyreturn.html" class="">EarlyReturn</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-instanceof.html" class="">Instanceof_</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-naming.html" class="">Naming</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-netteutils.html" class="">NetteUtils</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php52.html" class="">Php52</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php53.html" class="">Php53</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php54.html" class="">Php54</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php55.html" class="">Php55</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php56.html" class="">Php56</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php70.html" class="">Php70</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php71.html" class="">Php71</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php72.html" class="">Php72</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php73.html" class="">Php73</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php74.html" class="">Php74</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php80.html" class="">Php80</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php81.html" class="">Php81</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php82.html" class="">Php82</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php83.html" class="">Php83</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php84.html" class="">Php84</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php85.html" class="">Php85</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-privatization.html" class="">Privatization</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-removing.html" class="">Removing</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-renaming.html" class="">Renaming</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-strict.html" class="">Strict</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-transform.html" class="">Transform</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-typedeclaration.html" class="">TypeDeclaration</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-visibility.html" class="">Visibility</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-application.html" class="">Application</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-autoloading.html" class="">Autoloading</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-betterphpdocparser.html" class="">BetterPhpDocParser</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-bootstrap.html" class="">Bootstrap</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-bridge.html" class="">Bridge</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-caching.html" class="">Caching</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-changesreporting.html" class="">ChangesReporting</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-comments.html" class="">Comments</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-composer.html" class="">Composer</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-config.html" class="">Config</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-configuration.html" class="">Configuration</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-console.html" class="">Console</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-contract.html" class="">Contract</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-customrules.html" class="">CustomRules</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-dependencyinjection.html" class="">DependencyInjection</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-differ.html" class="">Differ</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-enum.html" class="">Enum</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-familytree.html" class="">FamilyTree</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-filesystem.html" class="">FileSystem</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-git.html" class="">Git</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodeanalyzer.html" class="">NodeAnalyzer</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodecollector.html" class="">NodeCollector</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodedecorator.html" class="">NodeDecorator</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodemanipulator.html" class="">NodeManipulator</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodenameresolver.html" class="">NodeNameResolver</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodenestingscope.html" class="">NodeNestingScope</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodetyperesolver.html" class="">NodeTypeResolver</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-parallel.html" class="">Parallel</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-php.html" class="">Php</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpattribute.html" class="">PhpAttribute</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpdocparser.html" class="">PhpDocParser</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpparser.html" class="">PhpParser</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpstan.html" class="">PHPStan</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpstanstatictypemapper.html" class="">PHPStanStaticTypeMapper</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-postrector.html" class="">PostRector</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-processanalyzer.html" class="">ProcessAnalyzer</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-rector.html" class="">Rector</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-reflection.html" class="">Reflection</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-reporting.html" class="">Reporting</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-set.html" class="">Set</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-skipper.html" class="">Skipper</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-staticreflection.html" class="">StaticReflection</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-statictypemapper.html" class="">StaticTypeMapper</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-testing.html" class="">Testing</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-util.html" class="">Util</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-validation.html" class="">Validation</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-valueobject.html" class="">ValueObject</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-vendorlocker.html" class="">VendorLocker</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-versionbonding.html" class="">VersionBonding</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-tests.html" class="">Tests</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-rectorinstaller.html" class="">RectorInstaller</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-doctrine.html" class="">Doctrine</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp72.html" class="">DowngradePhp72</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp73.html" class="">DowngradePhp73</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp74.html" class="">DowngradePhp74</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp80.html" class="">DowngradePhp80</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp81.html" class="">DowngradePhp81</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp82.html" class="">DowngradePhp82</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp83.html" class="">DowngradePhp83</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp84.html" class="">DowngradePhp84</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-downgradephp85.html" class="">DowngradePhp85</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-nodefactory.html" class="">NodeFactory</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpdocdecorator.html" class="">PhpDocDecorator</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-phpunit.html" class="">PHPUnit</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-symfony.html" class="">Symfony</a>
                
            </li>
                    <li>
                <a href="namespaces/rector-typeperfect.html" class="">TypePerfect</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/utils.html" class="">Utils</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/utils-rector.html" class="">Rector</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/symfony.html" class="">Symfony</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/symfony-component.html" class="">Component</a>
                
            </li>
                    <li>
                <a href="namespaces/symfony-contracts.html" class="">Contracts</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/symplify.html" class="">Symplify</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/symplify-ruledocgenerator.html" class="">RuleDocGenerator</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/sebastianbergmann.html" class="">SebastianBergmann</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/sebastianbergmann-codecoverage.html" class="">CodeCoverage</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-fileiterator.html" class="">FileIterator</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-invoker.html" class="">Invoker</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-template.html" class="">Template</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-timer.html" class="">Timer</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-cliparser.html" class="">CliParser</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-codeunit.html" class="">CodeUnit</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-codeunitreverselookup.html" class="">CodeUnitReverseLookup</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-comparator.html" class="">Comparator</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-complexity.html" class="">Complexity</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-diff.html" class="">Diff</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-environment.html" class="">Environment</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-exporter.html" class="">Exporter</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-globalstate.html" class="">GlobalState</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-linesofcode.html" class="">LinesOfCode</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-objectenumerator.html" class="">ObjectEnumerator</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-objectreflector.html" class="">ObjectReflector</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-recursioncontext.html" class="">RecursionContext</a>
                
            </li>
                    <li>
                <a href="namespaces/sebastianbergmann-type.html" class="">Type</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/staabm.html" class="">staabm</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/staabm-sideeffectsdetector.html" class="">SideEffectsDetector</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/theseer.html" class="">TheSeer</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/theseer-tokenizer.html" class="">Tokenizer</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/tomasvotruba.html" class="">TomasVotruba</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/tomasvotruba-typecoverage.html" class="">TypeCoverage</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/dotenv.html" class="">Dotenv</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/dotenv-exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-loader.html" class="">Loader</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-parser.html" class="">Parser</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-repository.html" class="">Repository</a>
                
            </li>
                    <li>
                <a href="namespaces/dotenv-store.html" class="">Store</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/voku.html" class="">voku</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/voku-helper.html" class="">helper</a>
                
            </li>
            </ul>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/webmozart.html" class="">Webmozart</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/webmozart-assert.html" class="">Assert</a>
                
            </li>
            </ul>

                        </section>
                <section class="phpdocumentor-sidebar__category -packages">
            <h2 class="phpdocumentor-sidebar__category-header">Packages</h2>
                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="packages/Application.html" class="">Application</a>
</h4>

                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="packages/JsonSchema.html" class="">JsonSchema</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="packages/JsonSchema-Entity.html" class="">Entity</a>
                
            </li>
                    <li>
                <a href="packages/JsonSchema-Exception.html" class="">Exception</a>
                
            </li>
                    <li>
                <a href="packages/JsonSchema-Iterator.html" class="">Iterator</a>
                
            </li>
            </ul>

                        </section>
            
    <section class="phpdocumentor-sidebar__category -reports">
        <h2 class="phpdocumentor-sidebar__category-header">Reports</h2>
                <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/deprecated.html">Deprecated</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/errors.html">Errors</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/markers.html">Markers</a></h3>
    </section>

    <section class="phpdocumentor-sidebar__category -indices">
        <h2 class="phpdocumentor-sidebar__category-header">Indices</h2>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="indices/files.html">Files</a></h3>
    </section>
</aside>

            <div class="phpdocumentor-column -nine phpdocumentor-content">
                                <section>
                                        <ul class="phpdocumentor-breadcrumbs">
    </ul>

    <article class="phpdocumentor-element -file">
        <h2 class="phpdocumentor-content__title">bootstrap80.php</h2>

        
        
        
        


        
<h3 id="toc">
    Table of Contents
    <a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html#toc" class="headerlink"><i class="fas fa-link"></i></a>

</h3>







<h4 id="toc-constants">
    Constants
    <a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html#toc-constants" class="headerlink"><i class="fas fa-link"></i></a>

</h4>
<dl class="phpdocumentor-table-of-contents">
            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="namespaces/default.html#constant_MB_CASE_LOWER">MB_CASE_LOWER</a>
    <span>
        &nbsp;= 1                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="namespaces/default.html#constant_MB_CASE_TITLE">MB_CASE_TITLE</a>
    <span>
        &nbsp;= 2                            </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -constant -public">
    <a class="" href="namespaces/default.html#constant_MB_CASE_UPPER">MB_CASE_UPPER</a>
    <span>
        &nbsp;= 0                            </span>
</dt>

    </dl>




<h4 id="toc-functions">
    Functions
    <a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html#toc-functions" class="headerlink"><i class="fas fa-link"></i></a>

</h4>
<dl class="phpdocumentor-table-of-contents">
            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_convert_encoding">mb_convert_encoding()</a>
    <span>
                                &nbsp;: array&lt;string|int, mixed&gt;|string|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_decode_mimeheader">mb_decode_mimeheader()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_encode_mimeheader">mb_encode_mimeheader()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_decode_numericentity">mb_decode_numericentity()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_encode_numericentity">mb_encode_numericentity()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_convert_case">mb_convert_case()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_internal_encoding">mb_internal_encoding()</a>
    <span>
                                &nbsp;: bool|string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_language">mb_language()</a>
    <span>
                                &nbsp;: bool|string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_list_encodings">mb_list_encodings()</a>
    <span>
                                &nbsp;: array&lt;string|int, mixed&gt;    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_encoding_aliases">mb_encoding_aliases()</a>
    <span>
                                &nbsp;: array&lt;string|int, mixed&gt;    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_check_encoding">mb_check_encoding()</a>
    <span>
                                &nbsp;: bool    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_detect_encoding">mb_detect_encoding()</a>
    <span>
                                &nbsp;: string|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_detect_order">mb_detect_order()</a>
    <span>
                                &nbsp;: array&lt;string|int, mixed&gt;|bool    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_parse_str">mb_parse_str()</a>
    <span>
                                &nbsp;: bool    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strlen">mb_strlen()</a>
    <span>
                                &nbsp;: int    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strpos">mb_strpos()</a>
    <span>
                                &nbsp;: int|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strtolower">mb_strtolower()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strtoupper">mb_strtoupper()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_substitute_character">mb_substitute_character()</a>
    <span>
                                &nbsp;: bool|int|string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_substr">mb_substr()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_stripos">mb_stripos()</a>
    <span>
                                &nbsp;: int|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_stristr">mb_stristr()</a>
    <span>
                                &nbsp;: string|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strrchr">mb_strrchr()</a>
    <span>
                                &nbsp;: string|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strrichr">mb_strrichr()</a>
    <span>
                                &nbsp;: string|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strripos">mb_strripos()</a>
    <span>
                                &nbsp;: int|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strrpos">mb_strrpos()</a>
    <span>
                                &nbsp;: int|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strstr">mb_strstr()</a>
    <span>
                                &nbsp;: string|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_get_info">mb_get_info()</a>
    <span>
                                &nbsp;: array&lt;string|int, mixed&gt;|int|string|false|null    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_http_output">mb_http_output()</a>
    <span>
                                &nbsp;: bool|string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_strwidth">mb_strwidth()</a>
    <span>
                                &nbsp;: int    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_substr_count">mb_substr_count()</a>
    <span>
                                &nbsp;: int    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_output_handler">mb_output_handler()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_http_input">mb_http_input()</a>
    <span>
                                &nbsp;: array&lt;string|int, mixed&gt;|string|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_convert_variables">mb_convert_variables()</a>
    <span>
                                &nbsp;: string|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_ord">mb_ord()</a>
    <span>
                                &nbsp;: int|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_chr">mb_chr()</a>
    <span>
                                &nbsp;: string|false    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_scrub">mb_scrub()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_str_split">mb_str_split()</a>
    <span>
                                &nbsp;: array&lt;string|int, mixed&gt;    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_str_pad">mb_str_pad()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_ucfirst">mb_ucfirst()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_lcfirst">mb_lcfirst()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_trim">mb_trim()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_ltrim">mb_ltrim()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

            <dt class="phpdocumentor-table-of-contents__entry -function -">
    <a class="" href="namespaces/default.html#function_mb_rtrim">mb_rtrim()</a>
    <span>
                                &nbsp;: string    </span>
</dt>

    </dl>


        
    <section class="phpdocumentor-constants">
        <h3 class="phpdocumentor-elements__header" id="constants">
            Constants
            <a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html#constants" class="headerlink"><i class="fas fa-link"></i></a>

        </h3>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_MB_CASE_LOWER">
        MB_CASE_LOWER
        <a href="namespaces/default.html#constant_MB_CASE_LOWER" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">234</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">MB_CASE_LOWER</span>
    = <span class="phpdocumentor-signature__default-value">1</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_MB_CASE_TITLE">
        MB_CASE_TITLE
        <a href="namespaces/default.html#constant_MB_CASE_TITLE" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">237</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">MB_CASE_TITLE</span>
    = <span class="phpdocumentor-signature__default-value">2</span>
</code>


    
    
    

    

</article>
                    <article class="phpdocumentor-element -constant -public ">
    <h4 class="phpdocumentor-element__name" id="constant_MB_CASE_UPPER">
        MB_CASE_UPPER
        <a href="namespaces/default.html#constant_MB_CASE_UPPER" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>

    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">231</span>

    </aside>

    
    
    <code class="phpdocumentor-signature phpdocumentor-code ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">MB_CASE_UPPER</span>
    = <span class="phpdocumentor-signature__default-value">0</span>
</code>


    
    
    

    

</article>
            </section>

            <section class="phpdocumentor-functions">
        <h3 class="phpdocumentor-elements__header" id="functions">
            Functions
            <a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html#functions" class="headerlink"><i class="fas fa-link"></i></a>

        </h3>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_convert_encoding">
        mb_convert_encoding()
        <a href="namespaces/default.html#function_mb_convert_encoding" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">20</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_convert_encoding</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$to_encoding</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$from_encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|string|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$to_encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$from_encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|string|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_decode_mimeheader">
        mb_decode_mimeheader()
        <a href="namespaces/default.html#function_mb_decode_mimeheader" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">23</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_decode_mimeheader</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_encode_mimeheader">
        mb_encode_mimeheader()
        <a href="namespaces/default.html#function_mb_encode_mimeheader" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">26</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_encode_mimeheader</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$charset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$transfer_encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$newline</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&quot;
&quot;</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$indent</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$charset</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$transfer_encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$newline</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&quot;
&quot;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$indent</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_decode_numericentity">
        mb_decode_numericentity()
        <a href="namespaces/default.html#function_mb_decode_numericentity" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">29</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_decode_numericentity</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$map</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$map</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_encode_numericentity">
        mb_encode_numericentity()
        <a href="namespaces/default.html#function_mb_encode_numericentity" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">32</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_encode_numericentity</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$map</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$hex</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$map</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$hex</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_convert_case">
        mb_convert_case()
        <a href="namespaces/default.html#function_mb_convert_case" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">35</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_convert_case</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$mode</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$mode</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_internal_encoding">
        mb_internal_encoding()
        <a href="namespaces/default.html#function_mb_internal_encoding" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">41</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_internal_encoding</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool|string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool|string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_language">
        mb_language()
        <a href="namespaces/default.html#function_mb_language" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">47</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_language</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$language</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool|string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$language</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool|string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_list_encodings">
        mb_list_encodings()
        <a href="namespaces/default.html#function_mb_list_encodings" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">50</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_list_encodings</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span></code>

    
    
    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_encoding_aliases">
        mb_encoding_aliases()
        <a href="namespaces/default.html#function_mb_encoding_aliases" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">53</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_encoding_aliases</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_check_encoding">
        mb_check_encoding()
        <a href="namespaces/default.html#function_mb_check_encoding" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">59</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_check_encoding</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$value</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$value</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_detect_encoding">
        mb_detect_encoding()
        <a href="namespaces/default.html#function_mb_detect_encoding" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">66</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_detect_encoding</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encodings</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$strict</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encodings</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$strict</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_detect_order">
        mb_detect_order()
        <a href="namespaces/default.html#function_mb_detect_order" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">73</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_detect_order</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|bool</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|bool</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_parse_str">
        mb_parse_str()
        <a href="namespaces/default.html#function_mb_parse_str" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">76</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_parse_str</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__reference-operator">&amp;</span><span class="phpdocumentor-signature__argument__name">$result</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$result</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strlen">
        mb_strlen()
        <a href="namespaces/default.html#function_mb_strlen" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">79</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strlen</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strpos">
        mb_strpos()
        <a href="namespaces/default.html#function_mb_strpos" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">85</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strpos</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$haystack</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$haystack</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strtolower">
        mb_strtolower()
        <a href="namespaces/default.html#function_mb_strtolower" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">88</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strtolower</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strtoupper">
        mb_strtoupper()
        <a href="namespaces/default.html#function_mb_strtoupper" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">91</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strtoupper</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_substitute_character">
        mb_substitute_character()
        <a href="namespaces/default.html#function_mb_substitute_character" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">98</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_substitute_character</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string|int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$substitute_character</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool|int|string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$substitute_character</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool|int|string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_substr">
        mb_substr()
        <a href="namespaces/default.html#function_mb_substr" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">101</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_substr</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$start</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$start</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_stripos">
        mb_stripos()
        <a href="namespaces/default.html#function_mb_stripos" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">107</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_stripos</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$haystack</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$haystack</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_stristr">
        mb_stristr()
        <a href="namespaces/default.html#function_mb_stristr" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">113</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_stristr</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$haystack</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$before_needle</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$haystack</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$before_needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strrchr">
        mb_strrchr()
        <a href="namespaces/default.html#function_mb_strrchr" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">119</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strrchr</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$haystack</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$before_needle</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$haystack</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$before_needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strrichr">
        mb_strrichr()
        <a href="namespaces/default.html#function_mb_strrichr" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">125</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strrichr</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$haystack</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$before_needle</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$haystack</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$before_needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strripos">
        mb_strripos()
        <a href="namespaces/default.html#function_mb_strripos" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">131</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strripos</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$haystack</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$haystack</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strrpos">
        mb_strrpos()
        <a href="namespaces/default.html#function_mb_strrpos" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">137</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strrpos</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$haystack</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$offset</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">0</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$haystack</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$offset</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">0</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strstr">
        mb_strstr()
        <a href="namespaces/default.html#function_mb_strstr" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">143</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strstr</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$haystack</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">bool|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$before_needle</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$haystack</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$before_needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">bool|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_get_info">
        mb_get_info()
        <a href="namespaces/default.html#function_mb_get_info" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">149</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_get_info</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$type</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&#039;all&#039;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|int|string|false|null</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$type</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&#039;all&#039;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|int|string|false|null</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_http_output">
        mb_http_output()
        <a href="namespaces/default.html#function_mb_http_output" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">155</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_http_output</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool|string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">bool|string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_strwidth">
        mb_strwidth()
        <a href="namespaces/default.html#function_mb_strwidth" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">158</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_strwidth</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_substr_count">
        mb_substr_count()
        <a href="namespaces/default.html#function_mb_substr_count" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">161</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_substr_count</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$haystack</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$needle</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$haystack</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$needle</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_output_handler">
        mb_output_handler()
        <a href="namespaces/default.html#function_mb_output_handler" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">164</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_output_handler</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$status</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$status</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_http_input">
        mb_http_input()
        <a href="namespaces/default.html#function_mb_http_input" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">170</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_http_input</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$type</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|string|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$type</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|string|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_convert_variables">
        mb_convert_variables()
        <a href="namespaces/default.html#function_mb_convert_variables" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">180</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_convert_variables</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$to_encoding</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$from_encoding</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__reference-operator">&amp;</span><span class="phpdocumentor-signature__argument__name">$var</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__variadic-operator">...</span><span class="phpdocumentor-signature__argument__reference-operator">&amp;</span><span class="phpdocumentor-signature__argument__name">$vars</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$to_encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$from_encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;|string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$var</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$vars</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_ord">
        mb_ord()
        <a href="namespaces/default.html#function_mb_ord" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">187</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_ord</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">int|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">int|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_chr">
        mb_chr()
        <a href="namespaces/default.html#function_mb_chr" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">193</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_chr</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$codepoint</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string|false</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$codepoint</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string|false</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_scrub">
        mb_scrub()
        <a href="namespaces/default.html#function_mb_scrub" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">196</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_scrub</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_str_split">
        mb_str_split()
        <a href="namespaces/default.html#function_mb_str_split" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">199</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_str_split</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">1</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">1</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_str_pad">
        mb_str_pad()
        <a href="namespaces/default.html#function_mb_str_pad" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">203</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_str_pad</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$length</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$pad_string</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&#039; &#039;</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$pad_type</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">STR_PAD_RIGHT</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$length</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$pad_string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&#039; &#039;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$pad_type</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                 = <span class="phpdocumentor-signature__argument__default-value">STR_PAD_RIGHT</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_ucfirst">
        mb_ucfirst()
        <a href="namespaces/default.html#function_mb_ucfirst" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">207</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_ucfirst</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_lcfirst">
        mb_lcfirst()
        <a href="namespaces/default.html#function_mb_lcfirst" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">211</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_lcfirst</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_trim">
        mb_trim()
        <a href="namespaces/default.html#function_mb_trim" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">215</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_trim</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$characters</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$characters</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_ltrim">
        mb_ltrim()
        <a href="namespaces/default.html#function_mb_ltrim" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">219</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_ltrim</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$characters</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$characters</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
                    <article class="phpdocumentor-element -function - ">
    <h4 class="phpdocumentor-element__name" id="function_mb_rtrim">
        mb_rtrim()
        <a href="namespaces/default.html#function_mb_rtrim" class="headerlink"><i class="fas fa-link"></i></a>

    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php"><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html"><abbr title="vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php">bootstrap80.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">223</span>

    </aside>

    
    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility"></span>
                    <span class="phpdocumentor-signature__name">mb_rtrim</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$string</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$characters</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string|null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$encoding</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">string</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$string</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$characters</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$encoding</span>
                : <span class="phpdocumentor-signature__argument__return-type">string|null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

            <section>
        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
        <span class="phpdocumentor-signature__response_type">string</span>
            </section>

</article>
            </section>

        <div class="phpdocumentor-modal" id="source-view">
    <div class="phpdocumentor-modal-bg" data-exit-button></div>
    <div class="phpdocumentor-modal-container">
        <div class="phpdocumentor-modal-content">
            <pre style="max-height: 500px; overflow-y: scroll" data-src="files/vendor/rector/rector/vendor/symfony/polyfill-mbstring/bootstrap80.php.txt" class="language-php line-numbers linkable-line-numbers"></pre>
        </div>
        <button data-exit-button class="phpdocumentor-modal__close">&times;</button>
    </div>
</div>

    <script type="text/javascript">
        (function () {
            function loadExternalCodeSnippet(el, url, line) {
                Array.prototype.slice.call(el.querySelectorAll('pre[data-src]')).forEach((pre) => {
                    const src = url || pre.getAttribute('data-src').replace(/\\/g, '/');
                    const language = 'php';

                    const code = document.createElement('code');
                    code.className = 'language-' + language;
                    pre.textContent = '';
                    pre.setAttribute('data-line', line)
                    code.textContent = 'Loading…';
                    pre.appendChild(code);

                    var xhr = new XMLHttpRequest();

                    xhr.open('GET', src, true);

                    xhr.onreadystatechange = function () {
                        if (xhr.readyState !== 4) {
                            return;
                        }

                        if (xhr.status < 400 && xhr.responseText) {
                            code.textContent = xhr.responseText;
                            Prism.highlightElement(code);
                            d=document.getElementsByClassName("line-numbers");
                            d[0].scrollTop = d[0].children[1].offsetTop;
                            return;
                        }

                        if (xhr.status === 404) {
                            code.textContent = '✖ Error: File could not be found';
                            return;
                        }

                        if (xhr.status >= 400) {
                            code.textContent = '✖ Error ' + xhr.status + ' while fetching file: ' + xhr.statusText;
                            return;
                        }

                        code.textContent = '✖ Error: An unknown error occurred';
                    };

                    xhr.send(null);
                });
            }

            const modalButtons = document.querySelectorAll("[data-modal]");
            const openedAsLocalFile = window.location.protocol === 'file:';
            if (modalButtons.length > 0 && openedAsLocalFile) {
                console.warn(
                    'Viewing the source code is unavailable because you are opening this page from the file:// scheme; ' +
                    'browsers block XHR requests when a page is opened this way'
                );
            }

            modalButtons.forEach(function (trigger) {
                if (openedAsLocalFile) {
                    trigger.setAttribute("hidden", "hidden");
                }

                trigger.addEventListener("click", function (event) {
                    event.preventDefault();
                    const modal = document.getElementById(trigger.dataset.modal);
                    if (!modal) {
                        console.error(`Modal with id "${trigger.dataset.modal}" could not be found`);
                        return;
                    }
                    modal.classList.add("phpdocumentor-modal__open");

                    loadExternalCodeSnippet(modal, trigger.dataset.src || null, trigger.dataset.line)
                    const exits = modal.querySelectorAll("[data-exit-button]");
                    exits.forEach(function (exit) {
                        exit.addEventListener("click", function (event) {
                            event.preventDefault();
                            modal.classList.remove("phpdocumentor-modal__open");
                        });
                    });
                });
            });
        })();
    </script>

    </article>
                                </section>
                <section class="phpdocumentor-on-this-page__sidebar">
                            
    <section class="phpdocumentor-on-this-page__content">
        <strong class="phpdocumentor-on-this-page__title">On this page</strong>

        <ul class="phpdocumentor-list -clean">
            <li class="phpdocumentor-on-this-page-section__title">Table Of Contents</li>
            <li>
                <ul class="phpdocumentor-list -clean">
                                                                                                                                                                    <li><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html#toc-constants">Constants</a></li>
                                                                <li><a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html#toc-functions">Functions</a></li>
                                    </ul>
            </li>

                            <li class="phpdocumentor-on-this-page-section__title">Constants</li>
                <li>
                    <ul class="phpdocumentor-list -clean">
                                                    <li class=""><a href="namespaces/default.html#constant_MB_CASE_LOWER">MB_CASE_LOWER</a></li>
                                                    <li class=""><a href="namespaces/default.html#constant_MB_CASE_TITLE">MB_CASE_TITLE</a></li>
                                                    <li class=""><a href="namespaces/default.html#constant_MB_CASE_UPPER">MB_CASE_UPPER</a></li>
                                            </ul>
                </li>
            
                            <li class="phpdocumentor-on-this-page-section__title">Functions</li>
                <li>
                    <ul class="phpdocumentor-list -clean">
                                                    <li class=""><a href="namespaces/default.html#function_mb_check_encoding">mb_check_encoding()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_chr">mb_chr()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_convert_case">mb_convert_case()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_convert_encoding">mb_convert_encoding()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_convert_variables">mb_convert_variables()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_decode_mimeheader">mb_decode_mimeheader()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_decode_numericentity">mb_decode_numericentity()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_detect_encoding">mb_detect_encoding()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_detect_order">mb_detect_order()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_encode_mimeheader">mb_encode_mimeheader()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_encode_numericentity">mb_encode_numericentity()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_encoding_aliases">mb_encoding_aliases()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_get_info">mb_get_info()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_http_input">mb_http_input()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_http_output">mb_http_output()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_internal_encoding">mb_internal_encoding()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_language">mb_language()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_lcfirst">mb_lcfirst()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_list_encodings">mb_list_encodings()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_ltrim">mb_ltrim()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_ord">mb_ord()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_output_handler">mb_output_handler()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_parse_str">mb_parse_str()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_rtrim">mb_rtrim()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_scrub">mb_scrub()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_str_pad">mb_str_pad()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_str_split">mb_str_split()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_stripos">mb_stripos()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_stristr">mb_stristr()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strlen">mb_strlen()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strpos">mb_strpos()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strrchr">mb_strrchr()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strrichr">mb_strrichr()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strripos">mb_strripos()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strrpos">mb_strrpos()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strstr">mb_strstr()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strtolower">mb_strtolower()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strtoupper">mb_strtoupper()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_strwidth">mb_strwidth()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_substitute_character">mb_substitute_character()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_substr">mb_substr()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_substr_count">mb_substr_count()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_trim">mb_trim()</a></li>
                                                    <li class=""><a href="namespaces/default.html#function_mb_ucfirst">mb_ucfirst()</a></li>
                                            </ul>
                </li>
                    </ul>
    </section>

                </section>
                            </div>
            <section data-search-results class="phpdocumentor-search-results phpdocumentor-search-results--hidden">
    <section class="phpdocumentor-search-results__dialog">
        <header class="phpdocumentor-search-results__header">
            <h2 class="phpdocumentor-search-results__title">Search results</h2>
            <button class="phpdocumentor-search-results__close"><i class="fas fa-times"></i></button>
        </header>
        <section class="phpdocumentor-search-results__body">
            <ul class="phpdocumentor-search-results__entries"></ul>
        </section>
    </section>
</section>
        </div>
        <a href="files/vendor-rector-rector-vendor-symfony-polyfill-mbstring-bootstrap80.html#top" class="phpdocumentor-back-to-top"><i class="fas fa-chevron-circle-up"></i></a>

    </main>

    <script>
        cssVars({});
    </script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-highlight/prism-line-highlight.min.js"></script>
</body>
</html>
