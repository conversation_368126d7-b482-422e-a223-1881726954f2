1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-faffc2f102fe098c5a29eeda9f3673e0
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameShouldDispatchAfterCommit.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 6e1b02d951185ab50ae8ffc05f86703b * pathUvendor/rector/rector/vendor/illuminate/contracts/Events/ShouldDispatchAfterCommit.php	 * sourceq<?php

namespace RectorPrefix202507\Illuminate\Contracts\Events;

interface ShouldDispatchAfterCommit
{
    //
}
 * namespaceAliases/\RectorPrefix202507\Illuminate\Contracts\EventsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEvents * includes * constants * functions
 * classes
 * interfacesI\RectorPrefix202507\Illuminate\Contracts\Events\ShouldDispatchAfterCommit,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ShouldDispatchAfterCommit."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums