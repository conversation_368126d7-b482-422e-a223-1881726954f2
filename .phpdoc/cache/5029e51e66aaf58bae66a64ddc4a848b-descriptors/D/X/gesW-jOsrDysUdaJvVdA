1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-6d147b86607b34f3d5abc46aac791f46
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name SuspiciousOperationException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 769030b465ba8d48477304e271863cc2 * pathIvendor/symfony/http-foundation/Exception/SuspiciousOperationException.php	 * source<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpFoundation\Exception;

/**
 * Raised when a user has performed an operation that should be considered
 * suspicious from a security perspective.
 */
class SuspiciousOperationException extends UnexpectedValueException implements RequestExceptionInterface
{
}
 * namespaceAliases+\Symfony\Component\HttpFoundation\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classesH\Symfony\Component\HttpFoundation\Exception\SuspiciousOperationException(phpDocumentor\Descriptor\ClassDescriptor#$+%SuspiciousOperationException-"oRaised when a user has performed an operation that should be considered
suspicious from a security perspective.	


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$D\Symfony\Component\HttpFoundation\Exception\UnexpectedValueException%UnexpectedValueException
 * implementsE\Symfony\Component\HttpFoundation\Exception\RequestExceptionInterface#$;%RequestExceptionInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums