**********
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-a993340625765a2fe9b2dd4583751ef3
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameSymfony5SetProvider.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ce7692b67c9a5b95af85a32aedbecb98 * path]vendor/rector/rector/vendor/rector/rector-symfony/src/Set/SetProvider/Symfony5SetProvider.php	 * sourcek<?php

declare (strict_types=1);
namespace Rector\Symfony\Set\SetProvider;

use Rector\Set\Contract\SetInterface;
use Rector\Set\Contract\SetProviderInterface;
use Rector\Set\Enum\SetGroup;
use Rector\Set\ValueObject\ComposerTriggeredSet;
final class Symfony5SetProvider implements SetProviderInterface
{
    /**
     * @return SetInterface[]
     */
    public function provide() : array
    {
        return [
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '5.0', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony50.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/console', '5.0', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony50/symfony50-console.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/debug', '5.0', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony50/symfony50-debug.php'),
            // @todo handle types per package?
            // __DIR__ . '/../../../config/sets/symfony/symfony5/symfony50/symfony50-types.php'
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/config', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-config.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/console', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-console.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/dependency-injection', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-dependency-injection.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/event-dispatcher', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-event-dispatcher.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/form', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-form.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/framework-bundle', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-framework-bundle.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-foundation', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-http-foundation.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/inflector', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-inflector.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/notifier', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-notifier.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-http', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-security-http.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-core', '5.1', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony51/symfony51-security-core.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/dependency-injection', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-dependency-injection.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/form', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-form.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-foundation', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-http-foundation.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/mime', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-mime.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/notifier', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-notifier.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/property-access', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-property-access.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/property-info', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-property-info.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-core', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-security-core.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-http', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-security-http.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/validator', '5.2', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony52/symfony52-validator.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '5.3', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony53.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-foundation', '5.3', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony53/symfony53-http-foundation.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/console', '5.3', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony53/symfony53-console.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-kernel', '5.3', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony53/symfony53-http-kernel.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-core', '5.3', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony53/symfony53-security-core.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-mailer', '5.3', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony53/symfony53-mailer.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/form', '5.3', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony53/symfony53-form.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/framework-bundle', '5.3', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony53/symfony53-framework-bundle.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '5.4', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony54.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/validator', '5.4', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony54/symfony54-validator.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-bundle', '5.4', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony54/symfony54-security-bundle.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-core', '5.4', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony54/symfony54-security-core.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-http', '5.4', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony54/symfony54-security-http.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/cache', '5.4', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony54/symfony54-cache.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-kernel', '5.4', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony54/symfony54-http-kernel.php'),
            new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/notifier', '5.4', __DIR__ . '/../../../config/sets/symfony/symfony5/symfony54/symfony54-notifier.php'),
        ];
    }
}
 * namespaceAliases\Rector\Symfony\Set\SetProviderphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSetProvider * includes * constants * functions
 * classes3\Rector\Symfony\Set\SetProvider\Symfony5SetProvider(phpDocumentor\Descriptor\ClassDescriptor#$+%Symfony5SetProvider-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ./@0   * readOnly * final * abstract
 * methodsprovide)phpDocumentor\Descriptor\MethodDescriptor#$>\Rector\Symfony\Set\SetProvider\Symfony5SetProvider::provide()%55" 
	

return -phpDocumentor\Descriptor\Tag\ReturnDescriptor8	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$!\Rector\Set\Contract\SetInterface%SetInterface
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|  ./0c./?0g 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType;<%phpDocumentor\Reflection\Types\Mixed_ A BCD E F GH? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(I 
 * implements)\Rector\Set\Contract\SetProviderInterface#$W%SetProviderInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums