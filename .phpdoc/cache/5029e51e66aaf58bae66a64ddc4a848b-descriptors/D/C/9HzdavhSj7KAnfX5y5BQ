1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b6fbc5ecf6a0e372a0a2621bc605c0f3
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name CanBeEscapedWhenCastToString.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 095bc89f3abe03d13c8ace063e097467 * pathYvendor/rector/rector/vendor/illuminate/contracts/Support/CanBeEscapedWhenCastToString.php	 * source]<?php

namespace RectorPrefix202507\Illuminate\Contracts\Support;

interface CanBeEscapedWhenCastToString
{
    /**
     * Indicate that the object's string representation should be escaped when __toString is invoked.
     *
     * @param  bool  $escape
     * @return $this
     */
    public function escapeWhenCastingToString($escape = \true);
}
 * namespaceAliases0\RectorPrefix202507\Illuminate\Contracts\SupportphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSupport * includes * constants * functions
 * classes
 * interfacesM\RectorPrefix202507\Illuminate\Contracts\Support\CanBeEscapedWhenCastToString,phpDocumentor\Descriptor\InterfaceDescriptor#$,%CanBeEscapedWhenCastToString."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methodsescapeWhenCastingToString)phpDocumentor\Descriptor\MethodDescriptor#$j\RectorPrefix202507\Illuminate\Contracts\Support\CanBeEscapedWhenCastToString::escapeWhenCastingToString()%44" ^Indicate that the object's string representation should be escaped when __toString is invoked.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type&phpDocumentor\Reflection\Types\Boolean  * variableNameescapereturn -phpDocumentor\Descriptor\Tag\ReturnDescriptor>	
 :#phpDocumentor\Reflection\Types\This   /0
1/0
1Y 	 * parent" * arguments=+phpDocumentor\Descriptor\ArgumentDescriptor =
 
	"'
  "0"1 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * default	rue * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicHI	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums