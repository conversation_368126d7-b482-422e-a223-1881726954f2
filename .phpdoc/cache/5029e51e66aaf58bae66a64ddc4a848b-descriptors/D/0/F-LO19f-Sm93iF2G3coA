1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-551d33c773bf83a4bfce54dd75799e6a
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameObjectMethodsDescription.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash c882d5631550e9e1d2bd90539f92c88f * path[vendor/ta-tikoma/phpunit-architecture-test/src/Asserts/Methods/ObjectMethodsDescription.php	 * source<?php

declare(strict_types=1);

namespace PHPUnit\Architecture\Asserts\Methods;

use PhpParser\Node;
use PHPUnit\Architecture\Asserts\Inheritance\ObjectInheritanceDescription;
use PHPUnit\Architecture\Asserts\Methods\Elements\MethodDescription;
use PHPUnit\Architecture\Asserts\Methods\Elements\ObjectMethods;
use PHPUnit\Architecture\Elements\ObjectDescription;
use PHPUnit\Architecture\Services\ServiceContainer;

/**
 * Describe object methods
 */
abstract class ObjectMethodsDescription extends ObjectInheritanceDescription
{
    public ObjectMethods $methods;

    public static function make(string $path): ?self
    {
        /** @var ObjectDescription|null $description */
        $description = parent::make($path);
        if ($description === null) {
            return null;
        }

        /** @var Node\Stmt\ClassMethod[] $methods */
        $methods = ServiceContainer::$nodeFinder->findInstanceOf($description->stmts, Node\Stmt\ClassMethod::class);

        $description->methods = new ObjectMethods(
            array_map(static function (Node\Stmt\ClassMethod $classMethod) use ($description): MethodDescription {
                return MethodDescription::make($description, $classMethod);
            }, $methods)
        );

        return $description;
    }
}
 * namespaceAliases%\PHPUnit\Architecture\Asserts\MethodsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameMethods * includes * constants * functions
 * classes>\PHPUnit\Architecture\Asserts\Methods\ObjectMethodsDescription(phpDocumentor\Descriptor\ClassDescriptor#$+%ObjectMethodsDescription-"Describe object methods	


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /0(1   * readOnly * final * abstract
 * methodsmake)phpDocumentor\Descriptor\MethodDescriptor#$F\PHPUnit\Architecture\Asserts\Methods\ObjectMethodsDescription::make()%66" 
	 
param  /01;/0'1 	 * parent" * argumentspath+phpDocumentor\Descriptor\ArgumentDescriptor <
 
	 
  """# 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\String_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicCD	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realType$phpDocumentor\Reflection\Types\Self_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference34
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * propertiesmethods+phpDocumentor\Descriptor\PropertyDescriptor#$H\PHPUnit\Architecture\Asserts\Methods\ObjectMethodsDescription::$methods%SS+ 
	 
var  /01 /01  :"F5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualLMN"0Q ?&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$<\PHPUnit\Architecture\Asserts\Methods\Elements\ObjectMethods%
ObjectMethodsA  (:#$F\PHPUnit\Architecture\Asserts\Inheritance\ObjectInheritanceDescription%ObjectInheritanceDescription
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums