1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b16356f779119f81a7209deb99bf25a1
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRefreshDatabaseState.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4f1c5cd5b455fa9bf5148e5d9f5ad613 * pathYvendor/laravel-zero/foundation/src/Illuminate/Foundation/Testing/RefreshDatabaseState.php	 * source<?php

namespace Illuminate\Foundation\Testing;

class RefreshDatabaseState
{
    /**
     * The current SQLite in-memory database connections.
     *
     * @var array<string, \PDO>
     */
    public static $inMemoryConnections = [];

    /**
     * Indicates if the test database has been migrated.
     *
     * @var bool
     */
    public static $migrated = false;

    /**
     * Indicates if a lazy refresh hook has been invoked.
     *
     * @var bool
     */
    public static $lazilyRefreshed = false;
}
 * namespaceAliases\Illuminate\Foundation\TestingphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameTesting * includes * constants * functions
 * classes3\Illuminate\Foundation\Testing\RefreshDatabaseState(phpDocumentor\Descriptor\ClassDescriptor#$+%RefreshDatabaseState-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * propertiesinMemoryConnections+phpDocumentor\Descriptor\PropertyDescriptor#$I\Illuminate\Foundation\Testing\RefreshDatabaseState::$inMemoryConnections%66+ 2The current SQLite in-memory database connections.	

var *phpDocumentor\Descriptor\Tag\VarDescriptor:	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\PDO%PDO
 * keyType&phpDocumentor\Reflection\Types\String_  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types D &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableName
  ./0 ./0  	 * parent"	 * static5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtual
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write <"'
 * default[] migrated7#$>\Illuminate\Foundation\Testing\RefreshDatabaseState::$migrated%ZZ+ 1Indicates if the test database has been migrated.	

: ;:	
 <&phpDocumentor\Reflection\Types\Boolean K
  ./0 ./0  L"MNOPQRST"4W <"@Xfalse lazilyRefreshed7#$E\Illuminate\Foundation\Testing\RefreshDatabaseState::$lazilyRefreshed%__+ 2Indicates if a lazy refresh hook has been invoked.	

: ;:	
 <] K
  ./0 ./0  L"MNOPQRST"4W <"QX^ (L 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums