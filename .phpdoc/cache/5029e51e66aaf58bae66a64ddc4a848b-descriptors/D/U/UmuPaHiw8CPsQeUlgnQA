1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-beb73e880e6a448fc86ae071893f9c04
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameAsController.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 38394322259d300f9d48c475ebb9c9dc * path5vendor/symfony/http-kernel/Attribute/AsController.php	 * sourceo<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\Attribute;

/**
 * Autoconfigures controllers as services by applying
 * the `controller.service_arguments` tag to them.
 *
 * This enables injecting services as method arguments in addition
 * to other conventional dependency injection strategies.
 */
#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_FUNCTION)]
class AsController
{
}
 * namespaceAliases'\Symfony\Component\HttpKernel\AttributephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Attribute * includes * constants * functions
 * classes4\Symfony\Component\HttpKernel\Attribute\AsController(phpDocumentor\Descriptor\ClassDescriptor#$+%AsController-"bAutoconfigures controllers as services by applying
the `controller.service_arguments` tag to them.	vThis enables injecting services as method arguments in addition
to other conventional dependency injection strategies.

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 012   * readOnly * final * abstract
 * methods
 * properties(	 * parent 
 * implements
 * usedTraits
 * attributes ,phpDocumentor\Descriptor\AttributeDescriptor7 phpDocumentor\Descriptor\AttributeDescriptor arguments 2phpDocumentor\Descriptor\ValueObjects\CallArgument9 phpDocumentor\Descriptor\ValueObjects\CallArgument value6\Attribute::TARGET_CLASS | \Attribute::TARGET_FUNCTION8 phpDocumentor\Descriptor\ValueObjects\CallArgument name < phpDocumentor\Descriptor\AttributeDescriptor attributeClass &	 #$
\Attribute%&
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums