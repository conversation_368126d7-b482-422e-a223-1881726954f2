1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-3a2747ff9eba1c8ed5e16d176a62086d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePolyfillPackage.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 9c2c69f7dcc25e422b9bfe874fb5499f * path8vendor/rector/rector/src/ValueObject/PolyfillPackage.php	 * source<?php

declare (strict_types=1);
namespace Rector\ValueObject;

/**
 * @api
 */
final class PolyfillPackage
{
    /**
     * @var string
     */
    public const PHP_80 = 'symfony/polyfill-php80';
    /**
     * @var string
     */
    public const PHP_73 = 'symfony/polyfill-php73';
}
 * namespaceAliases\Rector\ValueObjectphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameValueObject * includes * constants * functions
 * classes#\Rector\ValueObject\PolyfillPackage(phpDocumentor\Descriptor\ClassDescriptor#$+%PolyfillPackage-"
	

api .	
 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber	/ phpDocumentor\Reflection\Location columnNumber /01   * readOnly * final * abstract
 * methods
 * properties(PHP_80+phpDocumentor\Descriptor\ConstantDescriptor#$+\Rector\ValueObject\PolyfillPackage::PHP_80%77+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor:	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  /01 /01   * value'symfony/polyfill-php80'3
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent"<  PHP_738#$+\Rector\ValueObject\PolyfillPackage::PHP_73%HH+ 
	

: ;:	
 <= >
  /01 /01  ?'symfony/polyfill-php73'3ABC"4F G"<  G 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums