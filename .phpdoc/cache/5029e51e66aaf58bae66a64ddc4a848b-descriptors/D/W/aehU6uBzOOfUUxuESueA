1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-4a4f35fc4c7dbdcab2294111751ea8bf
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameWithoutErrorHandler.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 7ce72f3174e4bef750aadb3cc9e77846 * pathGvendor/phpunit/phpunit/src/Framework/Attributes/WithoutErrorHandler.php	 * source<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Framework\Attributes;

use Attribute;

/**
 * @immutable
 *
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
#[Attribute(Attribute::TARGET_METHOD)]
final readonly class WithoutErrorHandler
{
}
 * namespaceAliases\PHPUnit\Framework\AttributesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Attributes * includes * constants * functions
 * classes1\PHPUnit\Framework\Attributes\WithoutErrorHandler(phpDocumentor\Descriptor\ClassDescriptor#$+%WithoutErrorHandler-"
	

	immutable .	
 no-named-arguments /	QParameter names are not covered by the backward compatibility promise for PHPUnit 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 123   * readOnly * final * abstract
 * methods
 * properties(	 * parent 
 * implements
 * usedTraits
 * attributes ,phpDocumentor\Descriptor\AttributeDescriptor7 phpDocumentor\Descriptor\AttributeDescriptor arguments 2phpDocumentor\Descriptor\ValueObjects\CallArgument9 phpDocumentor\Descriptor\ValueObjects\CallArgument value\Attribute::TARGET_METHOD8 phpDocumentor\Descriptor\ValueObjects\CallArgument name < phpDocumentor\Descriptor\AttributeDescriptor attributeClass 	Attribute	 #$
\Attribute%D
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums