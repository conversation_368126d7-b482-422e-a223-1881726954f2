1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-174f290238b09f0439fc8226e1241f3a
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameTrinaryLogic.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash f367ced0f18cf07a3cc08e11e9dfd08d * pathCvendor/rector/rector/vendor/ondram/ci-detector/src/TrinaryLogic.php	 * source(<?php

declare (strict_types=1);
namespace RectorPrefix202507\OndraM\CiDetector;

/**
 * @see https://en.wikipedia.org/wiki/Three-valued_logic
 * @see https://github.com/phpstan/phpstan-src/blob/cedc99f51f7b8d815036e983166d7cb51ab46734/src/TrinaryLogic.php
 *
 * @internal
 */
final class TrinaryLogic
{
    private const YES = 1;
    private const MAYBE = 0;
    private const NO = -1;
    /** @var self[] */
    private static array $registry = [];
    private int $value;
    private function __construct(int $value)
    {
        $this->value = $value;
    }
    public static function createMaybe() : self
    {
        return self::create(self::MAYBE);
    }
    public static function createFromBoolean(bool $value) : self
    {
        return self::create($value ? self::YES : self::NO);
    }
    /**
     * Return true if it's known for sure that the value is true
     */
    public function yes() : bool
    {
        return $this->value === self::YES;
    }
    /**
     * Return true if it's not known for sure whether the value is true or false
     */
    public function maybe() : bool
    {
        return $this->value === self::MAYBE;
    }
    /**
     * Return true if it's known for sure that the value is false
     */
    public function no() : bool
    {
        return $this->value === self::NO;
    }
    /**
     * Return string representation of the value.
     * "Yes" when the value is true, "No" when its false, "Maybe" when it's not known for sure whether its
     *  true or false.
     */
    public function describe() : string
    {
        static $labels = [self::NO => 'No', self::MAYBE => 'Maybe', self::YES => 'Yes'];
        return $labels[$this->value];
    }
    private static function create(int $value) : self
    {
        return self::$registry[$value] ??= new self($value);
    }
}
 * namespaceAliases%\RectorPrefix202507\OndraM\CiDetectorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
CiDetector * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums