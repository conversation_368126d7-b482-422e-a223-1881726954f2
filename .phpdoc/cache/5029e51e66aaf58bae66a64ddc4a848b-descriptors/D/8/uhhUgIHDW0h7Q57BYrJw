1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-5ad194514e9bfb6380c8ec7fc7b6822f
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameSecurityConfigKey.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 036671e8f726c1d4336af8a8a30d2c08 * pathZvendor/rector/rector/vendor/rector/rector-symfony/rules/Configs/Enum/SecurityConfigKey.php	 * source*<?php

declare (strict_types=1);
namespace Rector\Symfony\Configs\Enum;

final class SecurityConfigKey
{
    /**
     * @var string
     */
    public const ACCESS_CONTROL = 'access_control';
    /**
     * @var string
     */
    public const ACCESS_DECISION_MANAGER = 'access_decision_manager';
    /**
     * @var string
     */
    public const ENTITY = 'entity';
    /**
     * @var string
     */
    public const ROLE = 'role';
    /**
     * @var string
     */
    public const ROLES = 'roles';
    /**
     * @var string
     */
    public const FIREWALLS = 'firewalls';
    /**
     * @var string
     */
    public const FIREWALL = 'firewall';
    /**
     * @var string
     */
    public const PROVIDERS = 'providers';
    /**
     * @var string
     */
    public const PROVIDER = 'provider';
}
 * namespaceAliases\Rector\Symfony\Configs\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes.\Rector\Symfony\Configs\Enum\SecurityConfigKey(phpDocumentor\Descriptor\ClassDescriptor#$+%SecurityConfigKey-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./,0   * readOnly * final * abstract
 * methods
 * properties(	ACCESS_CONTROL+phpDocumentor\Descriptor\ConstantDescriptor#$>\Rector\Symfony\Configs\Enum\SecurityConfigKey::ACCESS_CONTROL%66+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor9	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  ./0 ./0   * value'access_control'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent";  ACCESS_DECISION_MANAGER7#$G\Rector\Symfony\Configs\Enum\SecurityConfigKey::ACCESS_DECISION_MANAGER%GG+ 
	

9 :9	
 ;< =
  ./0 ./0  >'access_decision_manager'2@AB"-E F";  ENTITY7#$6\Rector\Symfony\Configs\Enum\SecurityConfigKey::ENTITY%JJ+ 
	

9 :9	
 ;< =
  ./0 ./0  >'entity'2@AB"-E F";  ROLE7#$4\Rector\Symfony\Configs\Enum\SecurityConfigKey::ROLE%MM+ 
	

9 :9	
 ;< =
  ./0 ./0  >'role'2@AB"-E F";  ROLES7#$5\Rector\Symfony\Configs\Enum\SecurityConfigKey::ROLES%PP+ 
	

9 :9	
 ;< =
  ./0 ./0  >'roles'2@AB"-E F";  	FIREWALLS7#$9\Rector\Symfony\Configs\Enum\SecurityConfigKey::FIREWALLS%SS+ 
	

9 :9	
 ;< =
  ./0 ./0  >'firewalls'2@AB"-E F";  FIREWALL7#$8\Rector\Symfony\Configs\Enum\SecurityConfigKey::FIREWALL%VV+ 
	

9 :9	
 ;< =
  ./#0 ./#0  >
'firewall'2@AB"-E F";  	PROVIDERS7#$9\Rector\Symfony\Configs\Enum\SecurityConfigKey::PROVIDERS%YY+ 
	

9 :9	
 ;< =
  ./'0 ./'0  >'providers'2@AB"-E F";  PROVIDER7#$8\Rector\Symfony\Configs\Enum\SecurityConfigKey::PROVIDER%\\+ 
	

9 :9	
 ;< =
  ./+0 ./+0  >
'provider'2@AB"-E F";  F 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums