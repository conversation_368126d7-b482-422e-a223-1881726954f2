1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-8eabd31930eb4eab9f21f4ff57e1007a
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name NoSupportedNotifierException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 2b3dcd6043547e10586d09c4bf0d1a7a * pathHvendor/jolicode/jolinotif/src/Exception/NoSupportedNotifierException.php	 * source
<?php

/*
 * This file is part of the JoliNotif project.
 *
 * (c) Loïck Piera <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Joli\JoliNotif\Exception;

trigger_deprecation('jolicode/jolinotif', '2.7', 'The "%s" class is deprecated and will be removed in 3.0.', NoSupportedNotifierException::class);

/**
 * @deprecated since 2.7, will be removed in 3.0
 */
class NoSupportedNotifierException extends \RuntimeException implements Exception
{
    public function __construct(
        string $message = 'No supported notifier',
        int $code = 0,
        ?\Throwable $previous = null,
    ) {
        parent::__construct($message, $code, $previous);
    }
}
 * namespaceAliases\Joli\JoliNotif\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classes6\Joli\JoliNotif\Exception\NoSupportedNotifierException(phpDocumentor\Descriptor\ClassDescriptor#$+%NoSupportedNotifierException-"
	


deprecated 1phpDocumentor\Descriptor\Tag\DeprecatedDescriptor.	!since 2.7, will be removed in 3.0 : phpDocumentor\Descriptor\Tag\DeprecatedDescriptor version

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 234   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$E\Joli\JoliNotif\Exception\NoSupportedNotifierException::__construct()%99" 
	 
param  2344234 	 * parent" * argumentsmessage+phpDocumentor\Descriptor\ArgumentDescriptor ?
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type&phpDocumentor\Reflection\Types\String_ 
 * default'No supported notifier' * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicGHcode@ J
 
	 
  "'"( A""B&phpDocumentor\Reflection\Types\Integer D0FGHIGHprevious@ M
 
	 
  "'"( A""B'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$
\Throwable%	ThrowableDnullFGHIGH	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference67
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(=#$\RuntimeException%RuntimeException
 * implements#\Joli\JoliNotif\Exception\Exception#$c%&
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums