1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-806af3592e3e1bd9002ca14dbff353b3
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameSignalsHandler.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 18482a7cc6baa3724fcccfb864e1414e * pathCvendor/rector/rector/vendor/react/event-loop/src/SignalsHandler.php	 * source+<?php

namespace RectorPrefix202507\React\EventLoop;

/**
 * @internal
 */
final class SignalsHandler
{
    private $signals = array();
    public function add($signal, $listener)
    {
        if (!isset($this->signals[$signal])) {
            $this->signals[$signal] = array();
        }
        if (\in_array($listener, $this->signals[$signal])) {
            return;
        }
        $this->signals[$signal][] = $listener;
    }
    public function remove($signal, $listener)
    {
        if (!isset($this->signals[$signal])) {
            return;
        }
        $index = \array_search($listener, $this->signals[$signal], \true);
        unset($this->signals[$signal][$index]);
        if (isset($this->signals[$signal]) && \count($this->signals[$signal]) === 0) {
            unset($this->signals[$signal]);
        }
    }
    public function call($signal)
    {
        if (!isset($this->signals[$signal])) {
            return;
        }
        foreach ($this->signals[$signal] as $listener) {
            \call_user_func($listener, $signal);
        }
    }
    public function count($signal)
    {
        if (!isset($this->signals[$signal])) {
            return 0;
        }
        return \count($this->signals[$signal]);
    }
    public function isEmpty()
    {
        return !$this->signals;
    }
}
 * namespaceAliases#\RectorPrefix202507\React\EventLoopphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	EventLoop * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums