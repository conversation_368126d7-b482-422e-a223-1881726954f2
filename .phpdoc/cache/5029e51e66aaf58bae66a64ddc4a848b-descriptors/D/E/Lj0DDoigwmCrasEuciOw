1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-d61deed6929a5d7799b627b7a1d63aa1
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameObjectReference.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 8ddb0e5d6fa8b65c0b657ac4376ad3c1 * path1vendor/rector/rector/src/Enum/ObjectReference.php	 * source*<?php

declare (strict_types=1);
namespace Rector\Enum;

final class ObjectReference
{
    /**
     * @var string
     */
    public const SELF = 'self';
    /**
     * @var string
     */
    public const PARENT = 'parent';
    /**
     * @var string
     */
    public const STATIC = 'static';
}
 * namespaceAliases\Rector\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes\Rector\Enum\ObjectReference(phpDocumentor\Descriptor\ClassDescriptor#$+%ObjectReference-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(SELF+phpDocumentor\Descriptor\ConstantDescriptor#$"\Rector\Enum\ObjectReference::SELF%66+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor9	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  ./0 ./0   * value'self'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent";  PARENT7#$$\Rector\Enum\ObjectReference::PARENT%GG+ 
	

9 :9	
 ;< =
  ./0 ./0  >'parent'2@AB"-E F";  STATIC7#$$\Rector\Enum\ObjectReference::STATIC%JJ+ 
	

9 :9	
 ;< =
  ./0 ./0  >'static'2@AB"-E F";  F 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums