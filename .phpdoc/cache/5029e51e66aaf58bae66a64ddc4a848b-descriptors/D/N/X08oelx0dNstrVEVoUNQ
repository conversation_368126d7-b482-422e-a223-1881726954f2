1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-f75d39c34ee60401672d83d4c88571d4
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePhpVersionedFilter.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 74c2f32d633ce78cd29aaf25dbe5ee6c * path>vendor/rector/rector/src/VersionBonding/PhpVersionedFilter.php	 * source<?php

declare (strict_types=1);
namespace Rector\VersionBonding;

use Rector\Contract\Rector\RectorInterface;
use Rector\Php\PhpVersionProvider;
use Rector\Php\PolyfillPackagesProvider;
use Rector\VersionBonding\Contract\MinPhpVersionInterface;
use Rector\VersionBonding\Contract\RelatedPolyfillInterface;
final class PhpVersionedFilter
{
    /**
     * @readonly
     */
    private PhpVersionProvider $phpVersionProvider;
    /**
     * @readonly
     */
    private PolyfillPackagesProvider $polyfillPackagesProvider;
    public function __construct(PhpVersionProvider $phpVersionProvider, PolyfillPackagesProvider $polyfillPackagesProvider)
    {
        $this->phpVersionProvider = $phpVersionProvider;
        $this->polyfillPackagesProvider = $polyfillPackagesProvider;
    }
    /**
     * @param list<RectorInterface> $rectors
     * @return list<RectorInterface>
     */
    public function filter(array $rectors) : array
    {
        $minProjectPhpVersion = $this->phpVersionProvider->provide();
        $activeRectors = [];
        foreach ($rectors as $rector) {
            if ($rector instanceof RelatedPolyfillInterface) {
                $polyfillPackageNames = $this->polyfillPackagesProvider->provide();
                if (\in_array($rector->providePolyfillPackage(), $polyfillPackageNames, \true)) {
                    $activeRectors[] = $rector;
                    continue;
                }
            }
            if (!$rector instanceof MinPhpVersionInterface) {
                $activeRectors[] = $rector;
                continue;
            }
            // does satisfy version? → include
            if ($rector->provideMinPhpVersion() <= $minProjectPhpVersion) {
                $activeRectors[] = $rector;
            }
        }
        return $activeRectors;
    }
}
 * namespaceAliases\Rector\VersionBondingphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameVersionBonding * includes * constants * functions
 * classes)\Rector\VersionBonding\PhpVersionedFilter(phpDocumentor\Descriptor\ClassDescriptor#$+%PhpVersionedFilter-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./50   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$8\Rector\VersionBonding\PhpVersionedFilter::__construct()%55" 
	 
param  ./0./0 	 * parent" * argumentsphpVersionProvider+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\Rector\Php\PhpVersionProvider%PhpVersionProvider
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEFpolyfillPackagesProvider< H
 
	 
  " "! =">?@#$$\Rector\Php\PolyfillPackagesProvider%PolyfillPackagesProviderC DEFGEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  filter6#$3\Rector\VersionBonding\PhpVersionedFilter::filter()%UU" 
	

8 ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
 >*phpDocumentor\Reflection\PseudoTypes\List_ * valueType?@#$'\Rector\Contract\Rector\RectorInterface%RectorInterface
 * keyType&phpDocumentor\Reflection\Types\Integer  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ ] 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNamerectorsreturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorf	
 >XY?@#$Z%[\] ^_` a ] bc  ./0v./40 9":e< e
 
	"?
  "V"W ="6>"AC DEFGEFKL%phpDocumentor\Reflection\Types\Array_YM \ ^_` a ] bcN23OPQ"5T  
 * properties;+phpDocumentor\Descriptor\PropertyDescriptor#$>\Rector\VersionBonding\PhpVersionedFilter::$phpVersionProvider%;;+ 
	

readonly l	
 var  ./0 ./0  9"K5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualOPQR'PRIVATET >?@#$A%BC  Hj#$D\Rector\VersionBonding\PhpVersionedFilter::$polyfillPackagesProvider%HH+ 
	

l l	
 m  ./0 ./0  9"KnopqOPQ"zT >?@#$I%JC  (9 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums