1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-cc5ed774932393e2f73f6a1711faef2e
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name&DateMalformedPeriodStringException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 3ba5aabdfdc157222e4b67d37c1b7581 * pathTvendor/symfony/polyfill-php83/Resources/stubs/DateMalformedPeriodStringException.php	 * source^<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

if (\PHP_VERSION_ID < 80300) {
    class DateMalformedPeriodStringException extends DateException
    {
    }
}
 * namespaceAliases * includes * constants * functions
 * classes#\DateMalformedPeriodStringException(phpDocumentor\Descriptor\ClassDescriptorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen&$ phpDocumentor\Reflection\Fqsen name"DateMalformedPeriodStringException+

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ,-.   * readOnly * final * abstract
 * methods
 * properties#	 * parent()\DateException*
DateException
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums