1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-45def4cf1e8dbf2cbc3b5ec3321e184f
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameInvalidOptionException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 7d51f4bfd5467718bb4ed58211b46027 * pathPvendor/rector/rector/vendor/symfony/console/Exception/InvalidOptionException.php	 * source<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace RectorPrefix202507\Symfony\Component\Console\Exception;

/**
 * Represents an incorrect option name or value typed in the console.
 *
 * <AUTHOR> Tamarelle <<EMAIL>>
 */
class InvalidOptionException extends \InvalidArgumentException implements ExceptionInterface
{
}
 * namespaceAliases7\RectorPrefix202507\Symfony\Component\Console\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classesN\RectorPrefix202507\Symfony\Component\Console\Exception\InvalidOptionException(phpDocumentor\Descriptor\ClassDescriptor#$+%InvalidOptionException-"BRepresents an incorrect option name or value typed in the console.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	)Jérôme Tamarelle <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 234   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$\InvalidArgumentException%InvalidArgumentException
 * implementsJ\RectorPrefix202507\Symfony\Component\Console\Exception\ExceptionInterface#$>%ExceptionInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums