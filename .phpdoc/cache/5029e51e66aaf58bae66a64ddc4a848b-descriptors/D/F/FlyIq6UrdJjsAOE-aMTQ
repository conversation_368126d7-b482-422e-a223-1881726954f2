1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-89c41d56af108542458d1c3a6f09e309
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameSocketCaster.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 138802ae7600373de92d14001cf135d6 * path1vendor/symfony/var-dumper/Caster/SocketCaster.php	 * source1<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\VarDumper\Caster;

use Symfony\Component\VarDumper\Cloner\Stub;

/**
 * <AUTHOR> Grekas <<EMAIL>>
 * <AUTHOR> Daubois <<EMAIL>>
 *
 * @internal
 */
final class SocketCaster
{
    public static function castSocket(\Socket $socket, array $a, Stub $stub, bool $isNested): array
    {
        socket_getsockname($socket, $addr, $port);
        $info = stream_get_meta_data(socket_export_stream($socket));

        if (\PHP_VERSION_ID >= 80300) {
            $uri = ($info['uri'] ?? '//');
            if (str_starts_with($uri, 'unix://')) {
                $uri .= $addr;
            } else {
                $uri .= \sprintf(str_contains($addr, ':') ? '[%s]:%s' : '%s:%s', $addr, $port);
            }

            $a[Caster::PREFIX_VIRTUAL.'uri'] = $uri;

            if (@socket_atmark($socket)) {
                $a[Caster::PREFIX_VIRTUAL.'atmark'] = true;
            }
        }

        $a += [
            Caster::PREFIX_VIRTUAL.'timed_out' => $info['timed_out'],
            Caster::PREFIX_VIRTUAL.'blocked' => $info['blocked'],
        ];

        if (!$lastError = socket_last_error($socket)) {
            return $a;
        }

        static $errors;

        if (!$errors) {
            $errors = get_defined_constants(true)['sockets'] ?? [];
            $errors = array_flip(array_filter($errors, static fn ($k) => str_starts_with($k, 'SOCKET_E'), \ARRAY_FILTER_USE_KEY));
        }

        $a[Caster::PREFIX_VIRTUAL.'last_error'] = new ConstStub($errors[$lastError], socket_strerror($lastError));

        return $a;
    }
}
 * namespaceAliases#\Symfony\Component\VarDumper\CasterphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameCaster * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums