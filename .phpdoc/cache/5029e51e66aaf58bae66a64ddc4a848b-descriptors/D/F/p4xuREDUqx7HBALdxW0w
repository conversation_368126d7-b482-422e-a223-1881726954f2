1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b195d481ce3bcea2254cb77357447ca1
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameConfigurableRectorInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash aea347d1a2ebd93063067553e1741b4f * pathHvendor/rector/rector/src/Contract/Rector/ConfigurableRectorInterface.php	 * sourcel<?php

declare (strict_types=1);
namespace Rector\Contract\Rector;

use Symplify\RuleDocGenerator\Contract\ConfigurableRuleInterface;
interface ConfigurableRectorInterface extends \Rector\Contract\Rector\RectorInterface, ConfigurableRuleInterface
{
    /**
     * @param mixed[] $configuration
     */
    public function configure(array $configuration) : void;
}
 * namespaceAliases\Rector\Contract\RectorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRector * includes * constants * functions
 * classes
 * interfaces3\Rector\Contract\Rector\ConfigurableRectorInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ConfigurableRectorInterface."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /0
1  
 * parents'\Rector\Contract\Rector\RectorInterface#$3%RectorInterface=\Symplify\RuleDocGenerator\Contract\ConfigurableRuleInterface#$5%ConfigurableRuleInterface(
 * methods	configure)phpDocumentor\Descriptor\MethodDescriptor#$@\Rector\Contract\Rector\ConfigurableRectorInterface::configure()%88" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor;	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableName
configuration  /012/01h 	 * parent" * argumentsJ+phpDocumentor\Descriptor\ArgumentDescriptor J
 
	"*
  "2"3 3 phpDocumentor\Descriptor\ArgumentDescriptor method"!=",
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicQR	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums