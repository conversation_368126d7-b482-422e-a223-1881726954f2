1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b05f93de66cdd8b4564170fcd48a699b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameDatePeriodFilter.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 2513b469d8aa5fe7a953b10c45294842 * pathJvendor/myclabs/deep-copy/src/DeepCopy/TypeFilter/Date/DatePeriodFilter.php	 * sourceJ<?php

namespace DeepCopy\TypeFilter\Date;

use DatePeriod;
use DeepCopy\TypeFilter\TypeFilter;

/**
 * @final
 */
class DatePeriodFilter implements TypeFilter
{
    /**
     * {@inheritdoc}
     *
     * @param DatePeriod $element
     *
     * @see http://news.php.net/php.bugs/205076
     */
    public function apply($element)
    {
        $options = 0;
        if (PHP_VERSION_ID >= 80200 && $element->include_end_date) {
            $options |= DatePeriod::INCLUDE_END_DATE;
        }
        if (!$element->include_start_date) {
            $options |= DatePeriod::EXCLUDE_START_DATE;
        }

        if ($element->getEndDate()) {
            return new DatePeriod($element->getStartDate(), $element->getDateInterval(), $element->getEndDate(), $options);
        }

        if (PHP_VERSION_ID >= 70217) {
            $recurrences = $element->getRecurrences();
        } else {
            $recurrences = $element->recurrences - $element->include_start_date;
        }

        return new DatePeriod($element->getStartDate(), $element->getDateInterval(), $recurrences, $options);
    }
}
 * namespaceAliases\DeepCopy\TypeFilter\DatephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDate * includes * constants * functions
 * classes*\DeepCopy\TypeFilter\Date\DatePeriodFilter(phpDocumentor\Descriptor\ClassDescriptor#$+%DatePeriodFilter-"
	

final .	
 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /0*1   * readOnly * final * abstract
 * methodsapply)phpDocumentor\Descriptor\MethodDescriptor#$3\DeepCopy\TypeFilter\Date\DatePeriodFilter::apply()%66" 
{@inheritdoc}	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor:	
  * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\DatePeriod%
DatePeriod * variableNameelementsee *phpDocumentor\Descriptor\Tag\SeeDescriptorC	
 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference4phpDocumentor\Reflection\DocBlock\Tags\Reference\Url9 phpDocumentor\Reflection\DocBlock\Tags\Reference\Url uri#http://news.php.net/php.bugs/205076  /01+/0)1F 	 * parent" * argumentsB+phpDocumentor\Descriptor\ArgumentDescriptor B
 
	"+
  "5"6 3 phpDocumentor\Descriptor\ArgumentDescriptor method""<"-
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicOP	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference34
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(I 
 * implements\DeepCopy\TypeFilter\TypeFilter#$^%
TypeFilter
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums