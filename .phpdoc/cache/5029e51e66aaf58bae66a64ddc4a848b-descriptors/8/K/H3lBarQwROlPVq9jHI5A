1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-d74fe068e85b9645c6fe9642e2bc73af
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameArgumentMover.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 019e113a094b386a054565a15ffd3193 * pathTvendor/rector/rector/vendor/rector/rector-phpunit/src/NodeAnalyzer/ArgumentMover.php	 * source<?php

declare (strict_types=1);
namespace Rector\PHPUnit\NodeAnalyzer;

use PhpParser\Node\Expr\MethodCall;
use PhpParser\Node\Expr\StaticCall;
final class ArgumentMover
{
    /**
     * @param \PhpParser\Node\Expr\MethodCall|\PhpParser\Node\Expr\StaticCall $node
     */
    public function removeFirstArg($node) : void
    {
        if ($node->isFirstClassCallable()) {
            return;
        }
        $methodArguments = $node->getArgs();
        \array_shift($methodArguments);
        $node->args = $methodArguments;
    }
}
 * namespaceAliases\Rector\PHPUnit\NodeAnalyzerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameNodeAnalyzer * includes * constants * functions
 * classes*\Rector\PHPUnit\NodeAnalyzer\ArgumentMover(phpDocumentor\Descriptor\ClassDescriptor#$+%
ArgumentMover-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methodsremoveFirstArg)phpDocumentor\Descriptor\MethodDescriptor#$<\Rector\PHPUnit\NodeAnalyzer\ArgumentMover::removeFirstArg()%55" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\PhpParser\Node\Expr\MethodCall%
MethodCall=>#$\PhpParser\Node\Expr\StaticCall%
StaticCall4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNamenode  ./
0./0 	 * parent" * argumentsF+phpDocumentor\Descriptor\ArgumentDescriptor F
 
	"$
  ","- 3 phpDocumentor\Descriptor\ArgumentDescriptor method":"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicMN	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(G 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums