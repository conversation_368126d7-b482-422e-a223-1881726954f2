1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-39732b3dcd3a543faf592cad80d5f823
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameAssertLocker.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash bdb666332044b96f8ff8d9cc1621625c * path<vendor/pestphp/pest-plugin-arch/src/Support/AssertLocker.php	 * source<?php

declare(strict_types=1);

namespace Pest\Arch\Support;

use PHPUnit\Framework\Assert;
use ReflectionClass;
use ReflectionProperty;

/**
 * @internal
 */
final class AssertLocker
{
    /**
     * The current assert count.
     */
    private static int $count = 0;

    /**
     * Locks the assert count.
     */
    public static function incrementAndLock(): void
    {
        // @phpstan-ignore-next-line
        Assert::assertTrue(true);

        self::$count = Assert::getCount();
    }

    /**
     * Unlocks the assert count.
     */
    public static function unlock(): void
    {
        $reflection = self::reflection();

        $reflection->setValue(null, self::$count);
    }

    /**
     * Gets the current assert count reflection.
     */
    private static function reflection(): ReflectionProperty
    {
        $reflectionClass = new ReflectionClass(Assert::class);

        $property = $reflectionClass->getProperty('count');
        $property->setAccessible(true);

        return $property;
    }
}
 * namespaceAliases\Pest\Arch\SupportphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSupport * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums