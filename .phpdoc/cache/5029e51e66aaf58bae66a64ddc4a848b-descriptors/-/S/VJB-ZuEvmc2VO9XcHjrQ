1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-a7fb66a2b4972739e557aec7b05110db
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameExprUsedInNodeAnalyzer.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 9c5432c272234eeaabb52af4e4d4503f * pathKvendor/rector/rector/rules/DeadCode/NodeAnalyzer/ExprUsedInNodeAnalyzer.php	 * sourcee<?php

declare (strict_types=1);
namespace Rector\DeadCode\NodeAnalyzer;

use PhpParser\Node;
use PhpParser\Node\Expr;
use PhpParser\Node\Expr\FuncCall;
use PhpParser\Node\Expr\Include_;
use PhpParser\Node\Expr\Variable;
use Rector\NodeAnalyzer\CompactFuncCallAnalyzer;
final class ExprUsedInNodeAnalyzer
{
    /**
     * @readonly
     */
    private \Rector\DeadCode\NodeAnalyzer\UsedVariableNameAnalyzer $usedVariableNameAnalyzer;
    /**
     * @readonly
     */
    private CompactFuncCallAnalyzer $compactFuncCallAnalyzer;
    public function __construct(\Rector\DeadCode\NodeAnalyzer\UsedVariableNameAnalyzer $usedVariableNameAnalyzer, CompactFuncCallAnalyzer $compactFuncCallAnalyzer)
    {
        $this->usedVariableNameAnalyzer = $usedVariableNameAnalyzer;
        $this->compactFuncCallAnalyzer = $compactFuncCallAnalyzer;
    }
    public function isUsed(Node $node, Variable $variable) : bool
    {
        if ($node instanceof Include_) {
            return \true;
        }
        // variable as variable variable need mark as used
        if ($node instanceof Variable && $node->name instanceof Expr) {
            return \true;
        }
        if ($node instanceof FuncCall) {
            return $this->compactFuncCallAnalyzer->isInCompact($node, $variable);
        }
        return $this->usedVariableNameAnalyzer->isVariableNamed($node, $variable);
    }
}
 * namespaceAliases\Rector\DeadCode\NodeAnalyzerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameNodeAnalyzer * includes * constants * functions
 * classes4\Rector\DeadCode\NodeAnalyzer\ExprUsedInNodeAnalyzer(phpDocumentor\Descriptor\ClassDescriptor#$+%ExprUsedInNodeAnalyzer-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./)0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$C\Rector\DeadCode\NodeAnalyzer\ExprUsedInNodeAnalyzer::__construct()%55" 
	 
param  ./0./0G 	 * parent" * argumentsusedVariableNameAnalyzer+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$6\Rector\DeadCode\NodeAnalyzer\UsedVariableNameAnalyzer%UsedVariableNameAnalyzer
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEFcompactFuncCallAnalyzer< H
 
	 
  " "! =">?@#$,\Rector\NodeAnalyzer\CompactFuncCallAnalyzer%CompactFuncCallAnalyzerC DEFGEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  isUsed6#$>\Rector\DeadCode\NodeAnalyzer\ExprUsedInNodeAnalyzer::isUsed()%UU" 
	 
8  ./0M./(0a 9":node< W
 
	 
  ";"< ="6>?@#$\PhpParser\Node%NodeC DEFGEFvariable< Z
 
	 
  ";"< ="6>?@#$\PhpParser\Node\Expr\Variable%VariableC DEFGEFKL&phpDocumentor\Reflection\Types\Boolean N23OPQ"5T  
 * properties;+phpDocumentor\Descriptor\PropertyDescriptor#$O\Rector\DeadCode\NodeAnalyzer\ExprUsedInNodeAnalyzer::$usedVariableNameAnalyzer%;;+ 
	

readonly a	
 var  ./0 ./0  9"K5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualOPQR'PRIVATET >?@#$A%BC  H_#$N\Rector\DeadCode\NodeAnalyzer\ExprUsedInNodeAnalyzer::$compactFuncCallAnalyzer%HH+ 
	

a a	
 b  ./0 ./0  9"KcdefOPQ"cT >?@#$I%JC  (9 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums