1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-4f0c5d8aff90bac42bd8db473f6582c7
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameCollectorDataNormalizer.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 1999e1a60faefc292e81b59c5ac919e1 * pathAvendor/tomasvotruba/type-coverage/src/CollectorDataNormalizer.php	 * source<?php

declare(strict_types=1);

namespace TomasVotruba\TypeCoverage;

use TomasVotruba\TypeCoverage\ValueObject\TypeCountAndMissingTypes;

final class CollectorDataNormalizer
{
    /**
     * @param array<string, array<array{0: int, 1: array<string, int>}>> $collectorDataByPath
     */
    public function normalize(array $collectorDataByPath): TypeCountAndMissingTypes
    {
        $totalCount = 0;
        $missingCount = 0;

        $missingTypeLinesByFilePath = [];

        foreach ($collectorDataByPath as $filePath => $typeCoverageData) {
            foreach ($typeCoverageData as $nestedData) {
                $totalCount += $nestedData[0];

                $missingCount += count($nestedData[1]);

                $missingTypeLinesByFilePath[$filePath] = array_merge(
                    $missingTypeLinesByFilePath[$filePath] ?? [],
                    $nestedData[1]
                );
            }
        }

        return new TypeCountAndMissingTypes($totalCount, $missingCount, $missingTypeLinesByFilePath);
    }
}
 * namespaceAliases\TomasVotruba\TypeCoveragephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameTypeCoverage * includes * constants * functions
 * classes2\TomasVotruba\TypeCoverage\CollectorDataNormalizer(phpDocumentor\Descriptor\ClassDescriptor#$+%CollectorDataNormalizer-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber	/ phpDocumentor\Reflection\Location columnNumber ./$0   * readOnly * final * abstract
 * methods	normalize)phpDocumentor\Descriptor\MethodDescriptor#$?\TomasVotruba\TypeCoverage\CollectorDataNormalizer::normalize()%55" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType;</phpDocumentor\Reflection\PseudoTypes\ArrayShape6 phpDocumentor\Reflection\PseudoTypes\ArrayShape items 3phpDocumentor\Reflection\PseudoTypes\ArrayShapeItem3 phpDocumentor\Reflection\PseudoTypes\ShapeItem key05 phpDocumentor\Reflection\PseudoTypes\ShapeItem value&phpDocumentor\Reflection\Types\Integer 8 phpDocumentor\Reflection\PseudoTypes\ShapeItem optional?@1B;<C 
 * keyType&phpDocumentor\Reflection\Types\String_  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types G C 4 phpDocumentor\Reflection\Types\AggregatedType token|DF HIJ G C KLFG HIJ G C KL * variableNamecollectorDataByPath  ./0$./#0 	 * parent" * argumentsN+phpDocumentor\Descriptor\ArgumentDescriptor N
 
	"$
  "="> 3 phpDocumentor\Descriptor\ArgumentDescriptor method":"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicUV	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$?\TomasVotruba\TypeCoverage\ValueObject\TypeCountAndMissingTypes%TypeCountAndMissingTypes? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(O 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums