1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c09feca6e062c8b2a872363e35a2c8b5
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameCancellationQueue.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 73c15073b5c3924eb16d2241407c1217 * pathLvendor/rector/rector/vendor/react/promise/src/Internal/CancellationQueue.php	 * source'<?php

namespace RectorPrefix202507\React\Promise\Internal;

/**
 * @internal
 */
final class CancellationQueue
{
    /** @var bool */
    private $started = \false;
    /** @var object[] */
    private $queue = [];
    public function __invoke() : void
    {
        if ($this->started) {
            return;
        }
        $this->started = \true;
        $this->drain();
    }
    /**
     * @param mixed $cancellable
     */
    public function enqueue($cancellable) : void
    {
        if (!\is_object($cancellable) || !\method_exists($cancellable, 'then') || !\method_exists($cancellable, 'cancel')) {
            return;
        }
        $length = \array_push($this->queue, $cancellable);
        if ($this->started && 1 === $length) {
            $this->drain();
        }
    }
    private function drain() : void
    {
        for ($i = \key($this->queue); isset($this->queue[$i]); $i++) {
            $cancellable = $this->queue[$i];
            \assert(\method_exists($cancellable, 'cancel'));
            $exception = null;
            try {
                $cancellable->cancel();
            } catch (\Throwable $exception) {
            }
            unset($this->queue[$i]);
            if ($exception) {
                throw $exception;
            }
        }
        $this->queue = [];
    }
}
 * namespaceAliases*\RectorPrefix202507\React\Promise\InternalphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameInternal * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums