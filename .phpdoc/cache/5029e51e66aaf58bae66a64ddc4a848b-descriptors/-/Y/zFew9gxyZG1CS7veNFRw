1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-ea5ac51df4d27a49a54ac265915b0890
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameConsecutiveMethodName.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 56a37455f3d56bf67ba4d93f59cabed4 * pathTvendor/rector/rector/vendor/rector/rector-phpunit/src/Enum/ConsecutiveMethodName.php	 * sourceg<?php

declare (strict_types=1);
namespace Rector\PHPUnit\Enum;

final class ConsecutiveMethodName
{
    /**
     * @var string
     */
    public const WILL_RETURN_ON_CONSECUTIVE_CALLS = 'willReturnOnConsecutiveCalls';
    /**
     * @var string
     */
    public const WILL_RETURN_ARGUMENT = 'willReturnArgument';
    /**
     * @var string
     */
    public const WILL_RETURN_SELF = 'willReturnSelf';
    /**
     * @var string
     */
    public const WILL_THROW_EXCEPTION = 'willThrowException';
    /**
     * @var string
     */
    public const WILL_RETURN_REFERENCE = 'willReturnReference';
    /**
     * @var string
     */
    public const WILL_RETURN = 'willReturn';
    /**
     * @var string
     */
    public const WILL_RETURN_CALLBACK = 'willReturnCallback';
    /**
     * @var string
     */
    public const WITH_CONSECUTIVE = 'withConsecutive';
}
 * namespaceAliases\Rector\PHPUnit\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes*\Rector\PHPUnit\Enum\ConsecutiveMethodName(phpDocumentor\Descriptor\ClassDescriptor#$+%ConsecutiveMethodName-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./(0   * readOnly * final * abstract
 * methods
 * properties( WILL_RETURN_ON_CONSECUTIVE_CALLS+phpDocumentor\Descriptor\ConstantDescriptor#$L\Rector\PHPUnit\Enum\ConsecutiveMethodName::WILL_RETURN_ON_CONSECUTIVE_CALLS%66+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor9	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  ./0 ./0   * value'willReturnOnConsecutiveCalls'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent";  WILL_RETURN_ARGUMENT7#$@\Rector\PHPUnit\Enum\ConsecutiveMethodName::WILL_RETURN_ARGUMENT%GG+ 
	

9 :9	
 ;< =
  ./0 ./0  >'willReturnArgument'2@AB"-E F";  WILL_RETURN_SELF7#$<\Rector\PHPUnit\Enum\ConsecutiveMethodName::WILL_RETURN_SELF%JJ+ 
	

9 :9	
 ;< =
  ./0 ./0  >'willReturnSelf'2@AB"-E F";  WILL_THROW_EXCEPTION7#$@\Rector\PHPUnit\Enum\ConsecutiveMethodName::WILL_THROW_EXCEPTION%MM+ 
	

9 :9	
 ;< =
  ./0 ./0  >'willThrowException'2@AB"-E F";  WILL_RETURN_REFERENCE7#$A\Rector\PHPUnit\Enum\ConsecutiveMethodName::WILL_RETURN_REFERENCE%PP+ 
	

9 :9	
 ;< =
  ./0 ./0  >'willReturnReference'2@AB"-E F";  WILL_RETURN7#$7\Rector\PHPUnit\Enum\ConsecutiveMethodName::WILL_RETURN%SS+ 
	

9 :9	
 ;< =
  ./0 ./0  >'willReturn'2@AB"-E F";  WILL_RETURN_CALLBACK7#$@\Rector\PHPUnit\Enum\ConsecutiveMethodName::WILL_RETURN_CALLBACK%VV+ 
	

9 :9	
 ;< =
  ./#0 ./#0  >'willReturnCallback'2@AB"-E F";  WITH_CONSECUTIVE7#$<\Rector\PHPUnit\Enum\ConsecutiveMethodName::WITH_CONSECUTIVE%YY+ 
	

9 :9	
 ;< =
  ./'0 ./'0  >'withConsecutive'2@AB"-E F";  F 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums