1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-75fd3df0bb7777829f7568f8ad4dc1e3
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameComposer.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 70f36a0b7866de5f5d74e597ab0fe0d0 * path8vendor/pestphp/pest-plugin-arch/src/Support/Composer.php	 * sourceT<?php

declare(strict_types=1);

namespace Pest\Arch\Support;

use Composer\Autoload\ClassLoader;
use Pest\TestSuite;

/**
 * @internal
 */
final class Composer
{
    /**
     * Gets the list of namespaces defined in the "composer.json" file.
     *
     * @return array<int, string>
     */
    public static function userNamespaces(): array
    {
        $namespaces = [];

        $rootPath = TestSuite::getInstance()->rootPath.DIRECTORY_SEPARATOR;

        foreach (self::loader()->getPrefixesPsr4() as $namespace => $directories) {
            foreach ($directories as $directory) {
                $directory = realpath($directory);

                if ($directory === false) {
                    continue;
                }

                if (str_starts_with($directory, $rootPath.'vendor')) {
                    continue;
                }

                if (str_starts_with($directory, $rootPath.'tests') && ! str_ends_with($directory, 'pest-plugin-arch'.DIRECTORY_SEPARATOR.'tests')) {
                    continue;
                }

                $namespaces[] = rtrim($namespace, '\\');
            }
        }

        return $namespaces;
    }

    /**
     * Gets composer's autoloader class.
     */
    public static function loader(): ClassLoader
    {
        $autoload = TestSuite::getInstance()->rootPath.DIRECTORY_SEPARATOR.'vendor'.DIRECTORY_SEPARATOR.'autoload.php';
        $autoloadLines = explode("\n", (string) file_get_contents($autoload));

        /** @var ClassLoader $loader */
        $loader = eval($autoloadLines[count($autoloadLines) - 2]);

        return $loader;
    }
}
 * namespaceAliases\Pest\Arch\SupportphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSupport * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums