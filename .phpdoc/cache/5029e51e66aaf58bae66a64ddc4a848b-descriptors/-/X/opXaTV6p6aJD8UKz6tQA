1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-eda6bd42824b4f88292c34421f7cf915
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameIsInfinite.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash c5f3a7bc47cee6e1b43bb73811b091b0 * pathCvendor/phpunit/phpunit/src/Framework/Constraint/Math/IsInfinite.php	 * sourced<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Framework\Constraint;

use function is_infinite;

/**
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
final class IsInfinite extends Constraint
{
    /**
     * Returns a string representation of the constraint.
     */
    public function toString(): string
    {
        return 'is infinite';
    }

    /**
     * Evaluates the constraint for parameter $other. Returns true if the
     * constraint is met, false otherwise.
     */
    protected function matches(mixed $other): bool
    {
        return is_infinite($other);
    }
}
 * namespaceAliases\PHPUnit\Framework\ConstraintphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Constraint * includes * constants * functions
 * classes(\PHPUnit\Framework\Constraint\IsInfinite(phpDocumentor\Descriptor\ClassDescriptor#$+%
IsInfinite-"
	

no-named-arguments .	QParameter names are not covered by the backward compatibility promise for PHPUnit 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 01#2   * readOnly * final * abstract
 * methodstoString)phpDocumentor\Descriptor\MethodDescriptor#$4\PHPUnit\Framework\Constraint\IsInfinite::toString()%77" 2Returns a string representation of the constraint.	

  012,012w 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\String_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference45
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  matches8#$3\PHPUnit\Framework\Constraint\IsInfinite::matches()%GG" fEvaluates the constraint for parameter $other. Returns true if the
constraint is met, false otherwise.	

param  01201"2` ;"<other+phpDocumentor\Descriptor\ArgumentDescriptor K
 
	 
  "5"6 3 phpDocumentor\Descriptor\ArgumentDescriptor method". * type%phpDocumentor\Reflection\Types\Mixed_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicRS=>&phpDocumentor\Reflection\Types\Boolean @45ABCD'	PROTECTEDF  
 * properties(;#$(\PHPUnit\Framework\Constraint\Constraint%&
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums