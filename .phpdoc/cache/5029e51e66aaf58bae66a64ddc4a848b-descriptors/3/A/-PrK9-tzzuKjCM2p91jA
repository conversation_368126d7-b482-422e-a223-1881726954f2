1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-dc5d510f4863328b0b261ba041dc882c
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameHub.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash a699932b55aeb2db8575aa0b1682e69d * pathAvendor/rector/rector/vendor/illuminate/contracts/Pipeline/Hub.php	 * source9<?php

namespace RectorPrefix202507\Illuminate\Contracts\Pipeline;

interface Hub
{
    /**
     * Send an object through one of the available pipelines.
     *
     * @param  mixed  $object
     * @param  string|null  $pipeline
     * @return mixed
     */
    public function pipe($object, $pipeline = null);
}
 * namespaceAliases1\RectorPrefix202507\Illuminate\Contracts\PipelinephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen namePipeline * includes * constants * functions
 * classes
 * interfaces5\RectorPrefix202507\Illuminate\Contracts\Pipeline\Hub,phpDocumentor\Descriptor\InterfaceDescriptor#$,%Hub."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methodspipe)phpDocumentor\Descriptor\MethodDescriptor#$=\RectorPrefix202507\Illuminate\Contracts\Pipeline\Hub::pipe()%44" 6Send an object through one of the available pipelines.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type%phpDocumentor\Reflection\Types\Mixed_  * variableNameobject98	
 :'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ $phpDocumentor\Reflection\Types\Null_ 4 phpDocumentor\Reflection\Types\AggregatedType token|<pipelinereturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorE	
 :;   /01/015 	 * parent" * arguments=+phpDocumentor\Descriptor\ArgumentDescriptor =
 
	"'
  "7"8 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicMNDI D
 
	"+
  "7"8 J":"-KnullLMNOMN	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType; ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums