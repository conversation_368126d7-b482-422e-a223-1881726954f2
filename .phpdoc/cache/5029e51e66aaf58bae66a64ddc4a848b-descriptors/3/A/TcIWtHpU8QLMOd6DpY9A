1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-507adef27266f6f6b6ffe4acb2b88d5b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameNameException.php * namespace

 * packageApplication
 * summary,This file is part of the ramsey/uuid library * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplatexFor the full copyright and license information, please view the LICENSE
file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * items	copyright &phpDocumentor\Descriptor\TagDescriptor

,Copyright (c) <PERSON> <<EMAIL>> license 

&http://opensource.org/licenses/MIT MIT package 

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 24c24df923f920498e2571d17b234a06 * path2vendor/ramsey/uuid/src/Exception/NameException.php	 * sourceV<?php

/**
 * This file is part of the ramsey/uuid library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) Ben Ramsey <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 */

declare(strict_types=1);

namespace Ramsey\Uuid\Exception;

use RuntimeException as PhpRuntimeException;

/**
 * Thrown to indicate that an error occurred while attempting to hash a namespace and name
 */
class NameException extends PhpRuntimeException implements UuidExceptionInterface
{
}
 * namespaceAliases\Ramsey\Uuid\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen($ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classes$\Ramsey\Uuid\Exception\NameException(phpDocumentor\Descriptor\ClassDescriptor)*1+
NameException3(WThrown to indicate that an error occurred while attempting to hash a namespace and name


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 567    * readOnly * final * abstract
 * methods
 * properties.	 * parent)*\RuntimeException+RuntimeException
 * implements-\Ramsey\Uuid\Exception\UuidExceptionInterface)*A+UuidExceptionInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums