1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-fae4629a898a825608f95a71f917a3ab
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameexceptions.php * namespace

 * packageApplication
 * summarytThis file is part of the Nette Framework (https://nette.org)
Copyright (c) 2004 David <PERSON>l (https://davidgrudl.com) * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate
3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash e6ddfbae46d661797fa4995bb7a6aa6a * path@vendor/rector/rector/vendor/nette/utils/src/Utils/exceptions.php	 * source<?php

/**
 * This file is part of the Nette Framework (https://nette.org)
 * Copyright (c) 2004 David Grudl (https://davidgrudl.com)
 */
declare (strict_types=1);
namespace RectorPrefix202507\Nette\Utils;

/**
 * An error occurred while working with the image.
 */
class ImageException extends \Exception
{
}
/**
 * The image file is invalid or in an unsupported format.
 */
class UnknownImageFileException extends ImageException
{
}
/**
 * JSON encoding or decoding failed.
 */
class JsonException extends \JsonException
{
}
/**
 * Regular expression pattern or execution failed.
 */
class RegexpException extends \Exception
{
}
/**
 * Type validation failed. The value doesn't match the expected type constraints.
 */
class AssertionException extends \Exception
{
}
 * namespaceAliases\RectorPrefix202507\Nette\UtilsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen#$ phpDocumentor\Reflection\Fqsen nameUtils * includes * constants * functions
 * classes.\RectorPrefix202507\Nette\Utils\ImageException(phpDocumentor\Descriptor\ClassDescriptor$%,&ImageException.#/An error occurred while working with the image.


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber 012   * readOnly * final * abstract
 * methods
 * properties)	 * parent$%
\Exception&	Exception
 * implements
 * usedTraits 9\RectorPrefix202507\Nette\Utils\UnknownImageFileException-$%=&UnknownImageFileException>#6The image file is invalid or in an unsupported format.


""  012 012  34567)8$%,&.;< -\RectorPrefix202507\Nette\Utils\JsonException-$%@&
JsonExceptionA#!JSON encoding or decoding failed.


""  012 012  34567)8$%\JsonException&A;< /\RectorPrefix202507\Nette\Utils\RegexpException-$%D&RegexpExceptionE#/Regular expression pattern or execution failed.


""  012 01!2  34567)8$%9&:;< 2\RectorPrefix202507\Nette\Utils\AssertionException-$%G&AssertionExceptionH#NType validation failed. The value doesn't match the expected type constraints.


""  01%2 01'2  34567)8$%9&:;< 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums