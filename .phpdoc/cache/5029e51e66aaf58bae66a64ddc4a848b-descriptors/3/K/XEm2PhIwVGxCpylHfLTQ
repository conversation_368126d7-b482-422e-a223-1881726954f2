1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0f4415dbe7d649c253bc723f385b6cea
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameJMSSetList.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash cb8e7f33c4ddf9ab2f30cc5f60855f86 * pathHvendor/rector/rector/vendor/rector/rector-symfony/src/Set/JMSSetList.php	 * sourceT<?php

declare (strict_types=1);
namespace Rector\Symfony\Set;

/**
 * @deprecated Use ->withAttributesSets(symfony: true) in rector.php config instead
 *
 * @api
 */
final class JMSSetList
{
    /**
     * @var string
     */
    public const ANNOTATIONS_TO_ATTRIBUTES = __DIR__ . '/../../config/sets/jms/annotations-to-attributes.php';
}
 * namespaceAliases\Rector\Symfony\SetphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSet * includes * constants * functions
 * classes\Rector\Symfony\Set\JMSSetList(phpDocumentor\Descriptor\ClassDescriptor#$+%
JMSSetList-"
	


deprecated 1phpDocumentor\Descriptor\Tag\DeprecatedDescriptor.	DUse ->withAttributesSets(symfony: true) in rector.php config instead : phpDocumentor\Descriptor\Tag\DeprecatedDescriptor version
api 2	
 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 345   * readOnly * final * abstract
 * methods
 * properties(ANNOTATIONS_TO_ATTRIBUTES+phpDocumentor\Descriptor\ConstantDescriptor#$9\Rector\Symfony\Set\JMSSetList::ANNOTATIONS_TO_ATTRIBUTES%;;+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor>	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  345 345   * value@__DIR__ . '/../../config/sets/jms/annotations-to-attributes.php'7
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent"@  K 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums