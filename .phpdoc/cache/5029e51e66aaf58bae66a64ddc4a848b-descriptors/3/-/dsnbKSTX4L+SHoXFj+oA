1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-ca8856ba1b1cbb0ac8d595ab0a51f303
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Mixed_.php * namespace

 * packageApplication
 * summary#This file is part of phpDocumentor. * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplatexFor the full copyright and license information, please view the LICENSE
file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * itemslink +phpDocumentor\Descriptor\Tag\LinkDescriptor


  * linkhttp://phpdoc.orgpackage &phpDocumentor\Descriptor\TagDescriptor

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash b731eed66fb543da879a15428502d8dc * path7vendor/phpdocumentor/type-resolver/src/Types/Mixed_.php	 * sourceq<?php

declare(strict_types=1);

/**
 * This file is part of phpDocumentor.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @link      http://phpdoc.org
 */

namespace phpDocumentor\Reflection\Types;

use phpDocumentor\Reflection\Type;

/**
 * Value Object representing an unknown, or mixed, type.
 *
 * @psalm-immutable
 */
final class Mixed_ implements Type
{
    /**
     * Returns a rendered output of the Type as it would be used in a DocBlock.
     */
    public function __toString(): string
    {
        return 'mixed';
    }
}
 * namespaceAliases\phpDocumentor\Reflection\TypesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen($ phpDocumentor\Reflection\Fqsen nameTypes * includes * constants * functions
 * classes&\phpDocumentor\Reflection\Types\Mixed_(phpDocumentor\Descriptor\ClassDescriptor)*1+Mixed_3(5Value Object representing an unknown, or mixed, type.


psalm-immutable 5


 ""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 67 8    * readOnly * final * abstract
 * methods
__toString)phpDocumentor\Descriptor\MethodDescriptor)*4\phpDocumentor\Reflection\Types\Mixed_::__toString()+==( HReturns a rendered output of the Type as it would be used in a DocBlock.


  678&678m  	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\String_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference:;
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties.A 
 * implements\phpDocumentor\Reflection\Type)*O+Type
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums