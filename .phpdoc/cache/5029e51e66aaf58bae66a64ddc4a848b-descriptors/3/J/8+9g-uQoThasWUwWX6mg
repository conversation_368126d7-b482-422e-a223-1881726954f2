1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-771c8d349f2c33dc73fb089d3e13665c
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Parser.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash b2e1d3e021c7835544807057385c499b * path9vendor/rector/rector/vendor/sebastian/diff/src/Parser.php	 * source-<?php

declare (strict_types=1);
/*
 * This file is part of sebastian/diff.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace RectorPrefix202507\SebastianBergmann\Diff;

use function array_pop;
use function assert;
use function count;
use function max;
use function preg_match;
use function preg_split;
/**
 * Unified diff parser.
 */
final class Parser
{
    /**
     * @return Diff[]
     */
    public function parse(string $string) : array
    {
        $lines = preg_split('(\\r\\n|\\r|\\n)', $string);
        if (!empty($lines) && $lines[count($lines) - 1] === '') {
            array_pop($lines);
        }
        $lineCount = count($lines);
        $diffs = [];
        $diff = null;
        $collected = [];
        for ($i = 0; $i < $lineCount; $i++) {
            if (preg_match('#^---\\h+"?(?P<file>[^\\v\\t"]+)#', $lines[$i], $fromMatch) && preg_match('#^\\+\\+\\+\\h+"?(?P<file>[^\\v\\t"]+)#', $lines[$i + 1], $toMatch)) {
                if ($diff !== null) {
                    $this->parseFileDiff($diff, $collected);
                    $diffs[] = $diff;
                    $collected = [];
                }
                assert(!empty($fromMatch['file']));
                assert(!empty($toMatch['file']));
                $diff = new Diff($fromMatch['file'], $toMatch['file']);
                $i++;
            } else {
                if (preg_match('/^(?:diff --git |index [\\da-f.]+|[+-]{3} [ab])/', $lines[$i])) {
                    continue;
                }
                $collected[] = $lines[$i];
            }
        }
        if ($diff !== null && count($collected)) {
            $this->parseFileDiff($diff, $collected);
            $diffs[] = $diff;
        }
        return $diffs;
    }
    private function parseFileDiff(Diff $diff, array $lines) : void
    {
        $chunks = [];
        $chunk = null;
        $diffLines = [];
        foreach ($lines as $line) {
            if (preg_match('/^@@\\s+-(?P<start>\\d+)(?:,\\s*(?P<startrange>\\d+))?\\s+\\+(?P<end>\\d+)(?:,\\s*(?P<endrange>\\d+))?\\s+@@/', $line, $match, \PREG_UNMATCHED_AS_NULL)) {
                $chunk = new Chunk((int) $match['start'], isset($match['startrange']) ? max(0, (int) $match['startrange']) : 1, (int) $match['end'], isset($match['endrange']) ? max(0, (int) $match['endrange']) : 1);
                $chunks[] = $chunk;
                $diffLines = [];
                continue;
            }
            if (preg_match('/^(?P<type>[+ -])?(?P<line>.*)/', $line, $match)) {
                $type = Line::UNCHANGED;
                if ($match['type'] === '+') {
                    $type = Line::ADDED;
                } elseif ($match['type'] === '-') {
                    $type = Line::REMOVED;
                }
                $diffLines[] = new Line($type, $match['line']);
                ($nullsafeVariable1 = $chunk) ? $nullsafeVariable1->setLines($diffLines) : null;
            }
        }
        $diff->setChunks($chunks);
    }
}
 * namespaceAliases*\RectorPrefix202507\SebastianBergmann\DiffphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDiff * includes * constants * functions
 * classes1\RectorPrefix202507\SebastianBergmann\Diff\Parser(phpDocumentor\Descriptor\ClassDescriptor#$+%Parser-"Unified diff parser.	


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /0W1   * readOnly * final * abstract
 * methodsparse)phpDocumentor\Descriptor\MethodDescriptor#$:\RectorPrefix202507\SebastianBergmann\Diff\Parser::parse()%66" 
	

return -phpDocumentor\Descriptor\Tag\ReturnDescriptor9	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$/\RectorPrefix202507\SebastianBergmann\Diff\Diff%&
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|param  /01 /0=1V 	 * parent" * argumentsstring+phpDocumentor\Descriptor\ArgumentDescriptor L
 
	 
  "0"1 3 phpDocumentor\Descriptor\ArgumentDescriptor method";E 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicQR	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType<=%phpDocumentor\Reflection\Types\Mixed_ A BCD E F GH? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference34
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
parseFileDiff7#$B\RectorPrefix202507\SebastianBergmann\Diff\Parser::parseFileDiff()%^^" 
	 
I  /0>1\/0V1) J"KdiffM `
 
	 
  "H"I N"C;>?#$@%&O PQRSQRlinesM a
 
	 
  "H"I N"C;<=V A BCD E F GHO PQRSQRTU$phpDocumentor\Reflection\Types\Void_ W34XYZ['PRIVATE]  
 * properties(J 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums