1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-cb0db56c9ead93392ee976b49d5e1daf
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name"ArrayUnionResponseTypeAnalyzer.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 3884e748d56ad702294ad83a2942e470 * pathevendor/rector/rector/vendor/rector/rector-symfony/src/TypeAnalyzer/ArrayUnionResponseTypeAnalyzer.php	 * source]<?php

declare (strict_types=1);
namespace Rector\Symfony\TypeAnalyzer;

use PHPStan\Type\ArrayType;
use PHPStan\Type\ObjectType;
use PHPStan\Type\Type;
use PHPStan\Type\TypeWithClassName;
use PHPStan\Type\UnionType;
final class ArrayUnionResponseTypeAnalyzer
{
    /**
     * @param class-string $className
     */
    public function isArrayUnionResponseType(Type $type, string $className) : bool
    {
        if (!$type instanceof UnionType) {
            return \false;
        }
        $hasArrayType = \false;
        $hasResponseType = \false;
        foreach ($type->getTypes() as $unionedType) {
            if ($unionedType instanceof ArrayType) {
                $hasArrayType = \true;
                continue;
            }
            if ($this->isTypeOfClassName($unionedType, $className)) {
                $hasResponseType = \true;
                continue;
            }
            return \false;
        }
        if (!$hasArrayType) {
            return \false;
        }
        return $hasResponseType;
    }
    /**
     * @param class-string $className
     */
    private function isTypeOfClassName(Type $type, string $className) : bool
    {
        if (!$type instanceof TypeWithClassName) {
            return \false;
        }
        $objectType = new ObjectType($className);
        return $objectType->isSuperTypeOf($type)->yes();
    }
}
 * namespaceAliases\Rector\Symfony\TypeAnalyzerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameTypeAnalyzer * includes * constants * functions
 * classes;\Rector\Symfony\TypeAnalyzer\ArrayUnionResponseTypeAnalyzer(phpDocumentor\Descriptor\ClassDescriptor#$+%ArrayUnionResponseTypeAnalyzer-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./20   * readOnly * final * abstract
 * methodsisArrayUnionResponseType)phpDocumentor\Descriptor\MethodDescriptor#$W\Rector\Symfony\TypeAnalyzer\ArrayUnionResponseTypeAnalyzer::isArrayUnionResponseType()%55" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type*phpDocumentor\Reflection\Types\ClassString1 phpDocumentor\Reflection\Types\ClassString fqsen  * variableName	className  ./0@./&0 	 * parent" * argumentstype+phpDocumentor\Descriptor\ArgumentDescriptor A
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method":&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\PHPStan\Type\Type%Type
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicJK>B >
 
	"$
  "'"( C":"&H IJKLJK	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\Boolean ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  isTypeOfClassName6#$P\Rector\Symfony\TypeAnalyzer\ArrayUnionResponseTypeAnalyzer::isTypeOfClassName()%WW" 
	

8 98	
 :;< =>  ./*0C./10Y ?"@AB A
 
	 
  "G"H C";:DE#$F%GH IJKLJK>B >
 
	"D
  "G"H C";:"FH IJKLJKMNO P23QRST'PRIVATEV  
 * properties(? 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums