1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-93bfc2434477f3fdc700da6cdf704814
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameLivewireLevelSetList.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4f19a5226ee334a5028f7edf365bdbc4 * pathSvendor/driftingly/rector-laravel/src/Set/Packages/Livewire/LivewireLevelSetList.php	 * source<?php

declare(strict_types=1);

namespace RectorLaravel\Set\Packages\Livewire;

final class LivewireLevelSetList
{
    /**
     * @var string
     */
    public const UP_TO_LIVEWIRE = __DIR__ . '/../../../../config/sets/packages/livewire/level/up-to-livewire-30.php';
}
 * namespaceAliases$\RectorLaravel\Set\Packages\LivewirephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameLivewire * includes * constants * functions
 * classes9\RectorLaravel\Set\Packages\Livewire\LivewireLevelSetList(phpDocumentor\Descriptor\ClassDescriptor#$+%LivewireLevelSetList-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./
0   * readOnly * final * abstract
 * methods
 * properties(UP_TO_LIVEWIRE+phpDocumentor\Descriptor\ConstantDescriptor#$I\RectorLaravel\Set\Packages\Livewire\LivewireLevelSetList::UP_TO_LIVEWIRE%66+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor9	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  ./0 ./0   * valueR__DIR__ . '/../../../../config/sets/packages/livewire/level/up-to-livewire-30.php'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent";  F 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums