1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-7a343ea8c7d4e77955ac36e392e0d6fd
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameTranslationDumperPass.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash b0575488dff840c5bcf5ce4d864395f5 * pathHvendor/symfony/translation/DependencyInjection/TranslationDumperPass.php	 * source=<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Translation\DependencyInjection;

use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

/**
 * Adds tagged translation.formatter services to translation writer.
 */
class TranslationDumperPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container): void
    {
        if (!$container->hasDefinition('translation.writer')) {
            return;
        }

        $definition = $container->getDefinition('translation.writer');

        foreach ($container->findTaggedServiceIds('translation.dumper', true) as $id => $attributes) {
            $definition->addMethodCall('addDumper', [$attributes[0]['alias'], new Reference($id)]);
        }
    }
}
 * namespaceAliases2\Symfony\Component\Translation\DependencyInjectionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDependencyInjection * includes * constants * functions
 * classesH\Symfony\Component\Translation\DependencyInjection\TranslationDumperPass(phpDocumentor\Descriptor\ClassDescriptor#$+%TranslationDumperPass-"AAdds tagged translation.formatter services to translation writer.	


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /0#1   * readOnly * final * abstract
 * methodsprocess)phpDocumentor\Descriptor\MethodDescriptor#$S\Symfony\Component\Translation\DependencyInjection\TranslationDumperPass::process()%66" 
	 
param  /01x/0"19 	 * parent" * arguments	container+phpDocumentor\Descriptor\ArgumentDescriptor <
 
	 
  """# 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$7\Symfony\Component\DependencyInjection\ContainerBuilder%ContainerBuilder
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicFG	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference34
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(: 
 * implementsE\Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface#$U%CompilerPassInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums