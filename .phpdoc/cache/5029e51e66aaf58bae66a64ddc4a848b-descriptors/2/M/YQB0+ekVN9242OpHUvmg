1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-8de2d185a1dc3c4e2d3de107ecb2861f
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameDeprecatedScopeHelper.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 95d2b4e482ea644b78a3cd5eff4d62ed * pathYvendor/phpstan/phpstan-deprecation-rules/src/Rules/Deprecations/DeprecatedScopeHelper.php	 * source<?php declare(strict_types = 1);

namespace PHPStan\Rules\Deprecations;

use PHPStan\Analyser\Scope;

class DeprecatedScopeHelper
{

	/** @var DeprecatedScopeResolver[]  */
	private array $resolvers;

	/**
	 * @param DeprecatedScopeResolver[] $checkers
	 */
	public function __construct(array $checkers)
	{
		$this->resolvers = $checkers;
	}

	public function isScopeDeprecated(Scope $scope): bool
	{
		foreach ($this->resolvers as $checker) {
			if ($checker->isScopeDeprecated($scope)) {
				return true;
			}
		}

		return false;
	}

}
 * namespaceAliases\PHPStan\Rules\DeprecationsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDeprecations * includes * constants * functions
 * classes1\PHPStan\Rules\Deprecations\DeprecatedScopeHelper(phpDocumentor\Descriptor\ClassDescriptor#$+%DeprecatedScopeHelper-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./ 0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$@\PHPStan\Rules\Deprecations\DeprecatedScopeHelper::__construct()%55" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$3\PHPStan\Rules\Deprecations\DeprecatedScopeResolver%DeprecatedScopeResolver
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNamecheckers  ./0./0T 	 * parent" * argumentsJ+phpDocumentor\Descriptor\ArgumentDescriptor J
 
	"$
  "-". 3 phpDocumentor\Descriptor\ArgumentDescriptor method":"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicQR	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  isScopeDeprecated6#$F\PHPStan\Rules\Deprecations\DeprecatedScopeHelper::isScopeDeprecated()%^^" 
	 
8  ./0X./0 K"LscopeM `
 
	 
  "?"@ N"::=>#$\PHPStan\Analyser\Scope%ScopeO PQRSQRTU&phpDocumentor\Reflection\Types\Boolean W23XYZ"9]  
 * properties	resolvers+phpDocumentor\Descriptor\PropertyDescriptor#$=\PHPStan\Rules\Deprecations\DeprecatedScopeHelper::$resolvers%ee+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptorh	
 :;<=>#$?%@A BCD E F GHI
  ./0 ./0  K"T5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualXYZ['PRIVATE] :"ZO  (K 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums