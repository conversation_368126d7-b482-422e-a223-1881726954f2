1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-bd4bf666871ea1025b1eb33be956ceec
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameNodeValueNormalizer.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 01bf35727db9e7bfbefbc207163de231 * pathcvendor/rector/rector/vendor/rector/rector-doctrine/rules/CodeQuality/Helper/NodeValueNormalizer.php	 * source+<?php

declare (strict_types=1);
namespace Rector\Doctrine\CodeQuality\Helper;

use PhpParser\Node\Arg;
use PhpParser\Node\Expr\ClassConstFetch;
use PhpParser\Node\Identifier;
use PhpParser\Node\Name\FullyQualified;
use PhpParser\Node\Scalar\String_;
final class NodeValueNormalizer
{
    /**
     * @param Arg[] $args
     */
    public static function ensureKeyIsClassConstFetch(array $args, string $argumentName) : void
    {
        foreach ($args as $arg) {
            if (!$arg->name instanceof Identifier) {
                continue;
            }
            if ($arg->name->toString() !== $argumentName) {
                continue;
            }
            // already done
            if ($arg->value instanceof ClassConstFetch) {
                continue;
            }
            $value = $arg->value;
            // we need string reference
            if (!$value instanceof String_) {
                continue;
            }
            $arg->value = new ClassConstFetch(new FullyQualified($value->value), new Identifier('class'));
        }
    }
}
 * namespaceAliases#\Rector\Doctrine\CodeQuality\HelperphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameHelper * includes * constants * functions
 * classes7\Rector\Doctrine\CodeQuality\Helper\NodeValueNormalizer(phpDocumentor\Descriptor\ClassDescriptor#$+%NodeValueNormalizer-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./%0   * readOnly * final * abstract
 * methodsensureKeyIsClassConstFetch)phpDocumentor\Descriptor\MethodDescriptor#$U\Rector\Doctrine\CodeQuality\Helper\NodeValueNormalizer::ensureKeyIsClassConstFetch()%55" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\PhpParser\Node\Arg%Arg
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNameargs  ./0K./$0' 	 * parent" * argumentsJ+phpDocumentor\Descriptor\ArgumentDescriptor J
 
	"$
  "-". 3 phpDocumentor\Descriptor\ArgumentDescriptor method":"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicQRargumentNameM T
 
	 
  "-". N":E O PQRSQR	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(K 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums