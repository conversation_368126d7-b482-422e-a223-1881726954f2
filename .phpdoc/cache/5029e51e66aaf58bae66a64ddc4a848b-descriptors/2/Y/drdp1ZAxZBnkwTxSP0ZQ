1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-f898c1c5e12dbcf34bde6d1309635502
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameControllerResolverInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash f67cd6afc1f40712da01d6ca6c635707 * pathEvendor/symfony/http-kernel/Controller/ControllerResolverInterface.php	 * source\<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\Controller;

use Symfony\Component\HttpFoundation\Request;

/**
 * A ControllerResolverInterface implementation knows how to determine the
 * controller to execute based on a Request object.
 *
 * A Controller can be any valid PHP callable.
 *
 * <AUTHOR> Potencier <<EMAIL>>
 */
interface ControllerResolverInterface
{
    /**
     * Returns the Controller instance associated with a Request.
     *
     * As several resolvers can exist for a single application, a resolver must
     * return false when it is not able to determine the controller.
     *
     * The resolver must only throw an exception when it should be able to load a
     * controller but cannot because of some errors made by the developer.
     *
     * @return callable|false A PHP callable representing the Controller,
     *                        or false if this resolver is not able to determine the controller
     *
     * @throws \LogicException If a controller was found based on the request but it is not callable
     */
    public function getController(Request $request): callable|false;
}
 * namespaceAliases(\Symfony\Component\HttpKernel\ControllerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Controller * includes * constants * functions
 * classes
 * interfacesD\Symfony\Component\HttpKernel\Controller\ControllerResolverInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ControllerResolverInterface."
xA ControllerResolverInterface implementation knows how to determine the
controller to execute based on a Request object.	+A Controller can be any valid PHP callable.
author -phpDocumentor\Descriptor\Tag\AuthorDescriptor1	%Fabien Potencier <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 45)6  
 * parents(
 * methods
getController)phpDocumentor\Descriptor\MethodDescriptor#$U\Symfony\Component\HttpKernel\Controller\ControllerResolverInterface::getController()%99" :Returns the Controller instance associated with a Request.	As several resolvers can exist for a single application, a resolver must
return false when it is not able to determine the controller.

The resolver must only throw an exception when it should be able to load a
controller but cannot because of some errors made by the developer.
return -phpDocumentor\Descriptor\Tag\ReturnDescriptor>	mA PHP callable representing the Controller,
or false if this resolver is not able to determine the controller  * type'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types (phpDocumentor\Reflection\Types\Callable_4 phpDocumentor\Reflection\Types\Callable_ returnType 4 phpDocumentor\Reflection\Types\Callable_ parameters+phpDocumentor\Reflection\PseudoTypes\False_ 4 phpDocumentor\Reflection\Types\AggregatedType token|throws -phpDocumentor\Descriptor\Tag\ThrowsDescriptorJ	EIf a controller was found based on the request but it is not callable A&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\LogicException%LogicExceptionparam  45(645(6X 	 * parent" * argumentsrequest+phpDocumentor\Descriptor\ArgumentDescriptor T
 
	 
  "<"= 3 phpDocumentor\Descriptor\ArgumentDescriptor method"%AMN#$)\Symfony\Component\HttpFoundation\Request%Request
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadic[\	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnTypeBC DE FG HI? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums