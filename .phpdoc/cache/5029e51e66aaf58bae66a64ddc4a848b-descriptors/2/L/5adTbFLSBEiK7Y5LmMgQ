1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c6af95d996198758465e3964799715c6
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameEventEmitter.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 03d7a20d448ae3a23236f9b67e6d33d5 * pathDvendor/rector/rector/vendor/evenement/evenement/src/EventEmitter.php	 * sourcer<?php

declare (strict_types=1);
/*
 * This file is part of Evenement.
 *
 * (c) Igor Wiedler <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace RectorPrefix202507\Evenement;

class EventEmitter implements EventEmitterInterface
{
    use EventEmitterTrait;
}
 * namespaceAliases\RectorPrefix202507\EvenementphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Evenement * includes * constants * functions
 * classes*\RectorPrefix202507\Evenement\EventEmitter(phpDocumentor\Descriptor\ClassDescriptor#$+%EventEmitter-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(	 * parent 
 * implements3\RectorPrefix202507\Evenement\EventEmitterInterface#$8%EventEmitterInterface
 * usedTraits/\RectorPrefix202507\Evenement\EventEmitterTrait#$;%EventEmitterTrait 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums