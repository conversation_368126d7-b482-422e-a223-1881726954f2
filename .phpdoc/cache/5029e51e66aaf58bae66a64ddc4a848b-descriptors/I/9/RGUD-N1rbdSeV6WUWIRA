1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-9f941f3298e88035db429fa23c7e2ccf
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameWritesToConsole.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash dad10a730f1284c9747fd1e06f44f3af * path<vendor/pestphp/pest/src/Concerns/Logging/WritesToConsole.php	 * source
<?php

declare(strict_types=1);

namespace Pest\Concerns\Logging;

/**
 * @internal
 */
trait WritesToConsole
{
    /**
     * Writes the given success message to the console.
     */
    private function writeSuccess(string $message): void
    {
        $this->writePestTestOutput($message, 'fg-green, bold', '✓');
    }

    /**
     * Writes the given error message to the console.
     */
    private function writeError(string $message): void
    {
        $this->writePestTestOutput($message, 'fg-red, bold', '⨯');
    }

    /**
     * Writes the given warning message to the console.
     */
    private function writeWarning(string $message): void
    {
        $this->writePestTestOutput($message, 'fg-yellow, bold', '-');
    }

    /**
     * Writes the give message to the console.
     */
    private function writePestTestOutput(string $message, string $color, string $symbol): void
    {
        $this->writeWithColor($color, "$symbol ", false);
        $this->write($message);
        $this->writeNewLine();
    }
}
 * namespaceAliases\Pest\Concerns\LoggingphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameLogging * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums