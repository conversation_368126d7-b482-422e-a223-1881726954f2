1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-65584b1e91032ad255f2590adafaec24
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name	Crypt.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash d0c464bb7dd01f1f1baef83b0e598fd6 * path+vendor/illuminate/support/Facades/Crypt.php	 * sourcej<?php

namespace Illuminate\Support\Facades;

/**
 * @method static bool supported(string $key, string $cipher)
 * @method static string generateKey(string $cipher)
 * @method static string encrypt(mixed $value, bool $serialize = true)
 * @method static string encryptString(string $value)
 * @method static mixed decrypt(string $payload, bool $unserialize = true)
 * @method static string decryptString(string $payload)
 * @method static string getKey()
 * @method static array getAllKeys()
 * @method static array getPreviousKeys()
 * @method static \Illuminate\Encryption\Encrypter previousKeys(array $keys)
 *
 * @see \Illuminate\Encryption\Encrypter
 */
class Crypt extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'encrypter';
    }
}
 * namespaceAliases\Illuminate\Support\FacadesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameFacades * includes * constants * functions
 * classes!\Illuminate\Support\Facades\Crypt(phpDocumentor\Descriptor\ClassDescriptor#$+%Crypt-"
	

method
 -phpDocumentor\Descriptor\Tag\MethodDescriptor.	
 9 phpDocumentor\Descriptor\Tag\MethodDescriptor methodName	supported8 phpDocumentor\Descriptor\Tag\MethodDescriptor argumentskey+phpDocumentor\Descriptor\ArgumentDescriptor 3
 
	 
       * type&phpDocumentor\Reflection\Types\String_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadic9:cipher4 <
 
	 
      56 7 89:;9:7 phpDocumentor\Descriptor\Tag\MethodDescriptor response-phpDocumentor\Descriptor\Tag\ReturnDescriptorreturn	  5&phpDocumentor\Reflection\Types\Boolean 5 phpDocumentor\Descriptor\Tag\MethodDescriptor staticC phpDocumentor\Descriptor\Tag\MethodDescriptor hasReturnByReference/.	
 0generateKey2<4 <
 
	 
      56 7 89:;9:=>?	  56 AB/.	
 0encrypt2value4 E
 
	 
      5%phpDocumentor\Reflection\Types\Mixed_ 7 89:;9:	serialize4 G
 
	 
      5@ 7	 = 'true'89:;9:=>?	  56 AB/.	
 0
encryptString2E4 E
 
	 
      56 7 89:;9:=>?	  56 AB/.	
 0decrypt2payload4 K
 
	 
      56 7 89:;9:unserialize4 L
 
	 
      5@ 7H89:;9:=>?	  5F AB/.	
 0
decryptString2K4 K
 
	 
      56 7 89:;9:=>?	  56 AB/.	
 0getKey2=>?	  56 AB/.	
 0
getAllKeys2=>?	  5%phpDocumentor\Reflection\Types\Array_ * valueTypeF 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types 6 &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|AB/.	
 0getPreviousKeys2=>?	  5PQF R STU 6 V WXAB	/.	
 0previousKeys2keys4 [
 
	 
      5PQF R STU 6 V WX7 89:;9:=>?	  5&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$ \Illuminate\Encryption\Encrypter%	EncrypterABsee *phpDocumentor\Descriptor\Tag\SeeDescriptor`	
 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference6phpDocumentor\Reflection\DocBlock\Tags\Reference\Fqsen= phpDocumentor\Reflection\DocBlock\Tags\Reference\Fqsen fqsen#$^%_
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber efg   * readOnly * final * abstract
 * methodsgetFacadeAccessor)phpDocumentor\Descriptor\MethodDescriptor#$6\Illuminate\Support\Facades\Crypt::getFacadeAccessor()%ll" )Get the registered name of the component.	

? >?	
 56   efgefgf 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnTypeF ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReferenceij
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'	PROTECTED7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(p#$"\Illuminate\Support\Facades\Facade%Facade
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums