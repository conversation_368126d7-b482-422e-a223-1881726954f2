1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c4db6441f949f3c1ef2922438a478882
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name8TestStubForIntersectionOfInterfacesCreatedSubscriber.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 32693321d63c012893d738c5fb8a1648 * pathpvendor/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestStubForIntersectionOfInterfacesCreatedSubscriber.php	 * sourceW<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Event\Test;

use PHPUnit\Event\Subscriber;

/**
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
interface TestStubForIntersectionOfInterfacesCreatedSubscriber extends Subscriber
{
    public function notify(TestStubForIntersectionOfInterfacesCreated $event): void;
}
 * namespaceAliases\PHPUnit\Event\TestphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameTest * includes * constants * functions
 * classes
 * interfacesH\PHPUnit\Event\Test\TestStubForIntersectionOfInterfacesCreatedSubscriber,phpDocumentor\Descriptor\InterfaceDescriptor#$,%4TestStubForIntersectionOfInterfacesCreatedSubscriber."

	

no-named-arguments /	QParameter names are not covered by the backward compatibility promise for PHPUnit 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 123  
 * parents\PHPUnit\Event\Subscriber#$5%
Subscriber(
 * methodsnotify)phpDocumentor\Descriptor\MethodDescriptor#$R\PHPUnit\Event\Test\TestStubForIntersectionOfInterfacesCreatedSubscriber::notify()%88" 
	 
param  123123S 	 * parent" * argumentsevent+phpDocumentor\Descriptor\ArgumentDescriptor >
 
	 
  ","- 3 phpDocumentor\Descriptor\ArgumentDescriptor method"' * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$>\PHPUnit\Event\Test\TestStubForIntersectionOfInterfacesCreated%*TestStubForIntersectionOfInterfacesCreated
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicHI	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums