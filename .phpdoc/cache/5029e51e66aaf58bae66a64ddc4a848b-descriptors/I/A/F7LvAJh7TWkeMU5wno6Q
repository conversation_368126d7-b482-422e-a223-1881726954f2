1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-a6ad70ec38cf19c58997e20a5425dcca
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name"UnionTypeNodePhpDocNodeVisitor.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4087a03c60b7d34765b836f44ed7b6f4 * path`vendor/rector/rector/src/BetterPhpDocParser/PhpDocNodeVisitor/UnionTypeNodePhpDocNodeVisitor.php	 * source
u<?php

declare (strict_types=1);
namespace Rector\BetterPhpDocParser\PhpDocNodeVisitor;

use PHPStan\PhpDocParser\Ast\Node;
use PHPStan\PhpDocParser\Ast\Type\UnionTypeNode;
use PHPStan\PhpDocParser\Lexer\Lexer;
use Rector\BetterPhpDocParser\Attributes\AttributeMirrorer;
use Rector\BetterPhpDocParser\Contract\BasePhpDocNodeVisitorInterface;
use Rector\BetterPhpDocParser\DataProvider\CurrentTokenIteratorProvider;
use Rector\BetterPhpDocParser\ValueObject\Parser\BetterTokenIterator;
use Rector\BetterPhpDocParser\ValueObject\PhpDocAttributeKey;
use Rector\BetterPhpDocParser\ValueObject\StartAndEnd;
use Rector\BetterPhpDocParser\ValueObject\Type\BracketsAwareUnionTypeNode;
use Rector\PhpDocParser\PhpDocParser\PhpDocNodeVisitor\AbstractPhpDocNodeVisitor;
final class UnionTypeNodePhpDocNodeVisitor extends AbstractPhpDocNodeVisitor implements BasePhpDocNodeVisitorInterface
{
    /**
     * @readonly
     */
    private CurrentTokenIteratorProvider $currentTokenIteratorProvider;
    /**
     * @readonly
     */
    private AttributeMirrorer $attributeMirrorer;
    public function __construct(CurrentTokenIteratorProvider $currentTokenIteratorProvider, AttributeMirrorer $attributeMirrorer)
    {
        $this->currentTokenIteratorProvider = $currentTokenIteratorProvider;
        $this->attributeMirrorer = $attributeMirrorer;
    }
    public function enterNode(Node $node) : ?Node
    {
        if (!$node instanceof UnionTypeNode) {
            return null;
        }
        if ($node instanceof BracketsAwareUnionTypeNode) {
            return null;
        }
        $startAndEnd = $this->resolveStartAndEnd($node);
        if (!$startAndEnd instanceof StartAndEnd) {
            $firstKey = \array_key_first($node->types);
            $lastKey = \array_key_last($node->types);
            $startAndEnd = new StartAndEnd($node->types[$firstKey]->getAttribute('startIndex'), $node->types[$lastKey]->getAttribute('endIndex'));
        }
        $betterTokenProvider = $this->currentTokenIteratorProvider->provide();
        $isWrappedInCurlyBrackets = $this->isWrappedInCurlyBrackets($betterTokenProvider, $startAndEnd);
        $bracketsAwareUnionTypeNode = new BracketsAwareUnionTypeNode($node->types, $isWrappedInCurlyBrackets);
        $this->attributeMirrorer->mirror($node, $bracketsAwareUnionTypeNode);
        return $bracketsAwareUnionTypeNode;
    }
    private function isWrappedInCurlyBrackets(BetterTokenIterator $betterTokenProvider, StartAndEnd $startAndEnd) : bool
    {
        $previousPosition = $startAndEnd->getStart() - 1;
        if ($betterTokenProvider->isTokenTypeOnPosition(Lexer::TOKEN_OPEN_PARENTHESES, $previousPosition)) {
            return \true;
        }
        // there is no + 1, as end is right at the next token
        return $betterTokenProvider->isTokenTypeOnPosition(Lexer::TOKEN_CLOSE_PARENTHESES, $startAndEnd->getEnd());
    }
    private function resolveStartAndEnd(UnionTypeNode $unionTypeNode) : ?StartAndEnd
    {
        $starAndEnd = $unionTypeNode->getAttribute(PhpDocAttributeKey::START_AND_END);
        if ($starAndEnd instanceof StartAndEnd) {
            return $starAndEnd;
        }
        // unwrap with parent array type...
        $parentNode = $unionTypeNode->getAttribute(PhpDocAttributeKey::PARENT);
        if (!$parentNode instanceof Node) {
            return null;
        }
        return $parentNode->getAttribute(PhpDocAttributeKey::START_AND_END);
    }
}
 * namespaceAliases,\Rector\BetterPhpDocParser\PhpDocNodeVisitorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen namePhpDocNodeVisitor * includes * constants * functions
 * classesK\Rector\BetterPhpDocParser\PhpDocNodeVisitor\UnionTypeNodePhpDocNodeVisitor(phpDocumentor\Descriptor\ClassDescriptor#$+%UnionTypeNodePhpDocNodeVisitor-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./J0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$Z\Rector\BetterPhpDocParser\PhpDocNodeVisitor\UnionTypeNodePhpDocNodeVisitor::__construct()%55" 
	 
param  ./00./0< 	 * parent" * argumentscurrentTokenIteratorProvider+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$D\Rector\BetterPhpDocParser\DataProvider\CurrentTokenIteratorProvider%CurrentTokenIteratorProvider
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEFattributeMirrorer< H
 
	 
  " "! =">?@#$7\Rector\BetterPhpDocParser\Attributes\AttributeMirrorer%AttributeMirrorerC DEFGEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  	enterNode6#$X\Rector\BetterPhpDocParser\PhpDocNodeVisitor\UnionTypeNodePhpDocNodeVisitor::enterNode()%UU" 
	 
8  ./ 0B./30	C 9":node< W
 
	 
  ";"< ="6>?@#$\PHPStan\PhpDocParser\Ast\Node%NodeC DEFGEFKL'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realType?@#$X%YN23OPQ"5T  isWrappedInCurlyBrackets6#$g\Rector\BetterPhpDocParser\PhpDocNodeVisitor\UnionTypeNodePhpDocNodeVisitor::isWrappedInCurlyBrackets()%\\" 
	 
8  ./40	I./<0E 9":betterTokenProvider< ^
 
	 
  "P"Q ="K>?@#$A\Rector\BetterPhpDocParser\ValueObject\Parser\BetterTokenIterator%BetterTokenIteratorC DEFGEFstartAndEnd< a
 
	 
  "P"Q ="K>?@#$2\Rector\BetterPhpDocParser\ValueObject\StartAndEnd%StartAndEndC DEFGEFKL&phpDocumentor\Reflection\Types\Boolean N23OPQR'PRIVATET  resolveStartAndEnd6#$a\Rector\BetterPhpDocParser\PhpDocNodeVisitor\UnionTypeNodePhpDocNodeVisitor::resolveStartAndEnd()%ff" 
	 
8  ./=0K./I0
q 9":
unionTypeNode< h
 
	 
  "k"l ="f>?@#$,\PHPStan\PhpDocParser\Ast\Type\UnionTypeNode%
UnionTypeNodeC DEFGEFKLZ[?@#$b%cN23OPQ"eT  
 * properties;+phpDocumentor\Descriptor\PropertyDescriptor#$j\Rector\BetterPhpDocParser\PhpDocNodeVisitor\UnionTypeNodePhpDocNodeVisitor::$currentTokenIteratorProvider%;;+ 
	

readonly n	
 var  ./0 ./0  9"K5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualOPQ"eT >?@#$A%BC  Hl#$_\Rector\BetterPhpDocParser\PhpDocNodeVisitor\UnionTypeNodePhpDocNodeVisitor::$attributeMirrorer%HH+ 
	

n n	
 o  ./0 ./0  9"KpqrsOPQ"eT >?@#$I%JC  (9#$M\Rector\PhpDocParser\PhpDocParser\PhpDocNodeVisitor\AbstractPhpDocNodeVisitor%AbstractPhpDocNodeVisitor
 * implementsB\Rector\BetterPhpDocParser\Contract\BasePhpDocNodeVisitorInterface#$x%BasePhpDocNodeVisitorInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums