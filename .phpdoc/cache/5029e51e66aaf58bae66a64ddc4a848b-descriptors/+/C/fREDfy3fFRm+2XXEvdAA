1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-eab03cce89f99ec620dc5002cf5d56a2
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePropertyGetHook.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 9b4ed7d713854aac8c6c0e5368a5e48a * pathXvendor/phpunit/phpunit/src/Framework/MockObject/Runtime/PropertyHook/PropertyGetHook.php	 * source<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Framework\MockObject\Runtime;

use function sprintf;

/**
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
final readonly class PropertyGetHook extends PropertyHook
{
    /**
     * @return non-empty-string
     *
     * @internal This method is not covered by the backward compatibility promise for PHPUnit
     */
    public function asString(): string
    {
        return sprintf(
            '$%s::get',
            $this->propertyName(),
        );
    }
}
 * namespaceAliases%\PHPUnit\Framework\MockObject\RuntimephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRuntime * includes * constants * functions
 * classes5\PHPUnit\Framework\MockObject\Runtime\PropertyGetHook(phpDocumentor\Descriptor\ClassDescriptor#$+%PropertyGetHook-"
	

no-named-arguments .	QParameter names are not covered by the backward compatibility promise for PHPUnit 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 012   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$2\PHPUnit\Framework\MockObject\Runtime\PropertyHook%PropertyHook
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums