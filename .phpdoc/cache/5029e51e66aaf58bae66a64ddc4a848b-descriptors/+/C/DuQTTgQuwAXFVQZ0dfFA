1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b7c913d406c4ed951547d869da0ef916
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameJsonLogger.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 8bd617eddb0e87467682d312234b4642 * pathCvendor/pestphp/pest-plugin-type-coverage/src/Logging/JsonLogger.php	 * source
<?php

declare(strict_types=1);

namespace Pest\TypeCoverage\Logging;

use Pest\TypeCoverage\Contracts\Logger;

/**
 * @internal
 */
final class JsonLogger implements Logger
{
    /**
     * Creates a new Logger instance.
     *
     * @param  array<int, array<string, mixed>>  $logs
     */
    public function __construct(
        private readonly string $outputPath,
        private readonly float $coverageMin,
        private array $logs = [],
    ) {
        //
    }

    /**
     * {@inheritDoc}
     */
    public function append(string $path, array $uncoveredLines, array $uncoveredLinesIgnored, float $percentage): void
    {
        $this->logs[] = [
            'file' => $path,
            'uncoveredLines' => $uncoveredLines,
            'uncoveredLinesIgnored' => $uncoveredLinesIgnored,
            'percentage' => $percentage,
        ];
    }

    /**
     * {@inheritDoc}
     */
    public function output(): void
    {
        $json = json_encode([
            'format' => 'pest',
            'coverage-min' => $this->coverageMin,
            'result' => $this->logs,
            'total' => round(array_sum(array_column($this->logs, 'percentage')) / count($this->logs), 2),
        ], JSON_THROW_ON_ERROR);
        file_put_contents($this->outputPath, $json);
    }
}
 * namespaceAliases\Pest\TypeCoverage\LoggingphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameLogging * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums