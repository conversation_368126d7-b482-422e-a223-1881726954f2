1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-44a6dcc85e96f1d9a1dd0744a75ae264
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameSubscriber.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 473884fd54de16cf15840e2faba02988 * pathBvendor/pestphp/pest/src/Logging/TeamCity/Subscriber/Subscriber.php	 * source<?php

declare(strict_types=1);

namespace Pest\Logging\TeamCity\Subscriber;

use Pest\Logging\TeamCity\TeamCityLogger;

/**
 * @internal
 */
abstract class Subscriber // @pest-arch-ignore-line
{
    /**
     * Creates a new Subscriber instance.
     */
    public function __construct(private readonly TeamCityLogger $logger) {}

    /**
     * Creates a new TeamCityLogger instance.
     */
    final protected function logger(): TeamCityLogger // @pest-arch-ignore-line
    {
        return $this->logger;
    }
}
 * namespaceAliases!\Pest\Logging\TeamCity\SubscriberphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Subscriber * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums