1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-06506fe8db34cbee77fb3104cba54476
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
installed.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 98914d2c139d52b214347cfe392d16ad * path2vendor/rector/rector/vendor/composer/installed.php	 * source/S<?php

namespace RectorPrefix202507;

return array('root' => array('name' => 'rector/rector-src', 'pretty_version' => 'dev-main', 'version' => 'dev-main', 'reference' => null, 'type' => 'library', 'install_path' => __DIR__ . '/../../', 'aliases' => array(), 'dev' => \false), 'versions' => array('clue/ndjson-react' => array('pretty_version' => 'v1.3.0', 'version' => '*******', 'reference' => '392dc165fce93b5bb5c637b67e59619223c931b0', 'type' => 'library', 'install_path' => __DIR__ . '/../clue/ndjson-react', 'aliases' => array(), 'dev_requirement' => \false), 'composer/pcre' => array('pretty_version' => '3.3.2', 'version' => '*******', 'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e', 'type' => 'library', 'install_path' => __DIR__ . '/./pcre', 'aliases' => array(), 'dev_requirement' => \false), 'composer/semver' => array('pretty_version' => '3.4.3', 'version' => '3.4.3.0', 'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12', 'type' => 'library', 'install_path' => __DIR__ . '/./semver', 'aliases' => array(), 'dev_requirement' => \false), 'composer/xdebug-handler' => array('pretty_version' => '3.0.5', 'version' => '3.0.5.0', 'reference' => '6c1925561632e83d60a44492e0b344cf48ab85ef', 'type' => 'library', 'install_path' => __DIR__ . '/./xdebug-handler', 'aliases' => array(), 'dev_requirement' => \false), 'doctrine/inflector' => array('pretty_version' => '2.0.10', 'version' => '2.0.10.0', 'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc', 'type' => 'library', 'install_path' => __DIR__ . '/../doctrine/inflector', 'aliases' => array(), 'dev_requirement' => \false), 'evenement/evenement' => array('pretty_version' => 'v3.0.2', 'version' => '3.0.2.0', 'reference' => '0a16b0d71ab13284339abb99d9d2bd813640efbc', 'type' => 'library', 'install_path' => __DIR__ . '/../evenement/evenement', 'aliases' => array(), 'dev_requirement' => \false), 'fidry/cpu-core-counter' => array('pretty_version' => '1.2.0', 'version' => '1.2.0.0', 'reference' => '8520451a140d3f46ac33042715115e290cf5785f', 'type' => 'library', 'install_path' => __DIR__ . '/../fidry/cpu-core-counter', 'aliases' => array(), 'dev_requirement' => \false), 'illuminate/container' => array('pretty_version' => 'v11.45.1', 'version' => '11.45.1.0', 'reference' => '79bf9149ad7ddd7e14326ebcdd41197d2c4ee36a', 'type' => 'library', 'install_path' => __DIR__ . '/../illuminate/container', 'aliases' => array(), 'dev_requirement' => \false), 'illuminate/contracts' => array('pretty_version' => 'v11.45.1', 'version' => '11.45.1.0', 'reference' => '4b2a67d1663f50085bc91e6371492697a5d2d4e8', 'type' => 'library', 'install_path' => __DIR__ . '/../illuminate/contracts', 'aliases' => array(), 'dev_requirement' => \false), 'nette/utils' => array('pretty_version' => 'v4.0.7', 'version' => '4.0.7.0', 'reference' => 'e67c4061eb40b9c113b218214e42cb5a0dda28f2', 'type' => 'library', 'install_path' => __DIR__ . '/../nette/utils', 'aliases' => array(), 'dev_requirement' => \false), 'nikic/php-parser' => array('pretty_version' => 'v5.5.0', 'version' => '5.5.0.0', 'reference' => 'ae59794362fe85e051a58ad36b289443f57be7a9', 'type' => 'library', 'install_path' => __DIR__ . '/../nikic/php-parser', 'aliases' => array(), 'dev_requirement' => \false), 'ondram/ci-detector' => array('pretty_version' => '4.2.0', 'version' => '4.2.0.0', 'reference' => '8b0223b5ed235fd377c75fdd1bfcad05c0f168b8', 'type' => 'library', 'install_path' => __DIR__ . '/../ondram/ci-detector', 'aliases' => array(), 'dev_requirement' => \false), 'phpstan/phpdoc-parser' => array('pretty_version' => '2.2.0', 'version' => '2.2.0.0', 'reference' => 'b9e61a61e39e02dd90944e9115241c7f7e76bfd8', 'type' => 'library', 'install_path' => __DIR__ . '/../phpstan/phpdoc-parser', 'aliases' => array(), 'dev_requirement' => \false), 'phpstan/phpstan' => array('dev_requirement' => \false, 'replaced' => array(0 => '2.1.18')), 'psr/container' => array('pretty_version' => '2.0.2', 'version' => '2.0.2.0', 'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963', 'type' => 'library', 'install_path' => __DIR__ . '/../psr/container', 'aliases' => array(), 'dev_requirement' => \false), 'psr/container-implementation' => array('dev_requirement' => \false, 'provided' => array(0 => '1.1|2.0')), 'psr/log' => array('pretty_version' => '3.0.2', 'version' => '3.0.2.0', 'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3', 'type' => 'library', 'install_path' => __DIR__ . '/../psr/log', 'aliases' => array(), 'dev_requirement' => \false), 'psr/log-implementation' => array('dev_requirement' => \false, 'provided' => array(0 => '1.0|2.0|3.0')), 'psr/simple-cache' => array('pretty_version' => '3.0.0', 'version' => '3.0.0.0', 'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865', 'type' => 'library', 'install_path' => __DIR__ . '/../psr/simple-cache', 'aliases' => array(), 'dev_requirement' => \false), 'react/cache' => array('pretty_version' => 'v1.2.0', 'version' => '1.2.0.0', 'reference' => 'd47c472b64aa5608225f47965a484b75c7817d5b', 'type' => 'library', 'install_path' => __DIR__ . '/../react/cache', 'aliases' => array(), 'dev_requirement' => \false), 'react/child-process' => array('pretty_version' => 'v0.6.6', 'version' => '0.6.6.0', 'reference' => '1721e2b93d89b745664353b9cfc8f155ba8a6159', 'type' => 'library', 'install_path' => __DIR__ . '/../react/child-process', 'aliases' => array(), 'dev_requirement' => \false), 'react/dns' => array('pretty_version' => 'v1.13.0', 'version' => '1.13.0.0', 'reference' => 'eb8ae001b5a455665c89c1df97f6fb682f8fb0f5', 'type' => 'library', 'install_path' => __DIR__ . '/../react/dns', 'aliases' => array(), 'dev_requirement' => \false), 'react/event-loop' => array('pretty_version' => 'v1.5.0', 'version' => '1.5.0.0', 'reference' => 'bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354', 'type' => 'library', 'install_path' => __DIR__ . '/../react/event-loop', 'aliases' => array(), 'dev_requirement' => \false), 'react/promise' => array('pretty_version' => 'v3.2.0', 'version' => '3.2.0.0', 'reference' => '8a164643313c71354582dc850b42b33fa12a4b63', 'type' => 'library', 'install_path' => __DIR__ . '/../react/promise', 'aliases' => array(), 'dev_requirement' => \false), 'react/socket' => array('pretty_version' => 'v1.16.0', 'version' => '1.16.0.0', 'reference' => '23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1', 'type' => 'library', 'install_path' => __DIR__ . '/../react/socket', 'aliases' => array(), 'dev_requirement' => \false), 'react/stream' => array('pretty_version' => 'v1.4.0', 'version' => '1.4.0.0', 'reference' => '1e5b0acb8fe55143b5b426817155190eb6f5b18d', 'type' => 'library', 'install_path' => __DIR__ . '/../react/stream', 'aliases' => array(), 'dev_requirement' => \false), 'rector/extension-installer' => array('pretty_version' => '0.11.2', 'version' => '0.11.2.0', 'reference' => '05544e9b195863b8571ae2a3b903cbec7fa062e0', 'type' => 'composer-plugin', 'install_path' => __DIR__ . '/../rector/extension-installer', 'aliases' => array(), 'dev_requirement' => \false), 'rector/rector' => array('dev_requirement' => \false, 'replaced' => array(0 => 'dev-main')), 'rector/rector-doctrine' => array('pretty_version' => 'dev-main', 'version' => 'dev-main', 'reference' => 'ac946860ee9148f75d0dcacd8ed3082fbc26f3d1', 'type' => 'rector-extension', 'install_path' => __DIR__ . '/../rector/rector-doctrine', 'aliases' => array(0 => '9999999-dev'), 'dev_requirement' => \false), 'rector/rector-downgrade-php' => array('pretty_version' => 'dev-main', 'version' => 'dev-main', 'reference' => '7c5025bd699496ed841f149ffd981450ba7d33bc', 'type' => 'rector-extension', 'install_path' => __DIR__ . '/../rector/rector-downgrade-php', 'aliases' => array(0 => '9999999-dev'), 'dev_requirement' => \false), 'rector/rector-phpunit' => array('pretty_version' => 'dev-main', 'version' => 'dev-main', 'reference' => 'ddb73ae7df691ec068b61bbe7961af22d98b82eb', 'type' => 'rector-extension', 'install_path' => __DIR__ . '/../rector/rector-phpunit', 'aliases' => array(0 => '9999999-dev'), 'dev_requirement' => \false), 'rector/rector-src' => array('pretty_version' => 'dev-main', 'version' => 'dev-main', 'reference' => null, 'type' => 'library', 'install_path' => __DIR__ . '/../../', 'aliases' => array(), 'dev_requirement' => \false), 'rector/rector-symfony' => array('pretty_version' => 'dev-main', 'version' => 'dev-main', 'reference' => 'c1dcdae150a5f2f687f32544097117c420a56819', 'type' => 'rector-extension', 'install_path' => __DIR__ . '/../rector/rector-symfony', 'aliases' => array(0 => '9999999-dev'), 'dev_requirement' => \false), 'sebastian/diff' => array('pretty_version' => '6.0.2', 'version' => '6.0.2.0', 'reference' => 'b4ccd857127db5d41a5b676f24b51371d76d8544', 'type' => 'library', 'install_path' => __DIR__ . '/../sebastian/diff', 'aliases' => array(), 'dev_requirement' => \false), 'symfony/console' => array('pretty_version' => 'v6.4.23', 'version' => '6.4.23.0', 'reference' => '9056771b8eca08d026cd3280deeec3cfd99c4d93', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/console', 'aliases' => array(), 'dev_requirement' => \false), 'symfony/deprecation-contracts' => array('pretty_version' => 'v3.6.0', 'version' => '3.6.0.0', 'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/deprecation-contracts', 'aliases' => array(), 'dev_requirement' => \false), 'symfony/filesystem' => array('pretty_version' => 'v6.4.13', 'version' => '6.4.13.0', 'reference' => '4856c9cf585d5a0313d8d35afd681a526f038dd3', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/filesystem', 'aliases' => array(), 'dev_requirement' => \false), 'symfony/finder' => array('pretty_version' => 'v6.4.17', 'version' => '6.4.17.0', 'reference' => '1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/finder', 'aliases' => array(), 'dev_requirement' => \false), 'symfony/polyfill-ctype' => array('dev_requirement' => \false, 'replaced' => array(0 => '*')), 'symfony/polyfill-intl-grapheme' => array('dev_requirement' => \false, 'replaced' => array(0 => '*')), 'symfony/polyfill-mbstring' => array('pretty_version' => 'v1.32.0', 'version' => '1.32.0.0', 'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/polyfill-mbstring', 'aliases' => array(), 'dev_requirement' => \false), 'symfony/process' => array('pretty_version' => 'v6.4.20', 'version' => '6.4.20.0', 'reference' => 'e2a61c16af36c9a07e5c9906498b73e091949a20', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/process', 'aliases' => array(), 'dev_requirement' => \false), 'symfony/service-contracts' => array('pretty_version' => 'v3.6.0', 'version' => '3.6.0.0', 'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/service-contracts', 'aliases' => array(), 'dev_requirement' => \false), 'symfony/string' => array('dev_requirement' => \false, 'replaced' => array(0 => '*')), 'symfony/yaml' => array('pretty_version' => 'v7.3.1', 'version' => '7.3.1.0', 'reference' => '0c3555045a46ab3cd4cc5a69d161225195230edb', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/yaml', 'aliases' => array(), 'dev_requirement' => \false), 'symplify/easy-parallel' => array('pretty_version' => '11.2.2', 'version' => '11.2.2.0', 'reference' => '8586c18bb8efb31cd192a4e5cc94ae7813f72ed9', 'type' => 'library', 'install_path' => __DIR__ . '/../symplify/easy-parallel', 'aliases' => array(), 'dev_requirement' => \false), 'symplify/rule-doc-generator-contracts' => array('pretty_version' => '11.2.0', 'version' => '11.2.0.0', 'reference' => '479cfcfd46047f80624aba931d9789e50475b5c6', 'type' => 'library', 'install_path' => __DIR__ . '/../symplify/rule-doc-generator-contracts', 'aliases' => array(), 'dev_requirement' => \false), 'webmozart/assert' => array('pretty_version' => '1.11.0', 'version' => '1.11.0.0', 'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991', 'type' => 'library', 'install_path' => __DIR__ . '/../webmozart/assert', 'aliases' => array(), 'dev_requirement' => \false)));
 * namespaceAliases\RectorPrefix202507phpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRectorPrefix202507 * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums