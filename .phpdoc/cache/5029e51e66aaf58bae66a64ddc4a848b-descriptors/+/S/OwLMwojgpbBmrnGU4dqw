1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-39e43ab0268d2898d6690b3fe6913aaa
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameContent.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 7130f3c0ff8a57258645a6f8c31fb26f * pathGvendor/rector/rector/vendor/symplify/easy-parallel/src/Enum/Content.php	 * source<?php

declare (strict_types=1);
namespace RectorPrefix202507\Symplify\EasyParallel\Enum;

/**
 * @api
 */
final class Content
{
    /**
     * @var string
     */
    public const RESULT = 'result';
    /**
     * @var string
     */
    public const FILES = 'files';
}
 * namespaceAliases.\RectorPrefix202507\Symplify\EasyParallel\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes6\RectorPrefix202507\Symplify\EasyParallel\Enum\Content(phpDocumentor\Descriptor\ClassDescriptor#$+%Content-"
	

api .	
 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber	/ phpDocumentor\Reflection\Location columnNumber /01   * readOnly * final * abstract
 * methods
 * properties(RESULT+phpDocumentor\Descriptor\ConstantDescriptor#$>\RectorPrefix202507\Symplify\EasyParallel\Enum\Content::RESULT%77+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor:	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  /01 /01   * value'result'3
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent"<  FILES8#$=\RectorPrefix202507\Symplify\EasyParallel\Enum\Content::FILES%HH+ 
	

: ;:	
 <= >
  /01 /01  ?'files'3ABC"4F G"<  G 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums