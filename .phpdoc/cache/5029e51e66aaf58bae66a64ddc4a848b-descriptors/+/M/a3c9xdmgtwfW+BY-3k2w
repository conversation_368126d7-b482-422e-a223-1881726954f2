1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-69edcb3726705ecae6a8bfa494f4704c
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRelativeReferenceValidator.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 464dca2bf230e1a0035f3bd7334b2109 * path]vendor/justinrainbow/json-schema/src/JsonSchema/Tool/Validator/RelativeReferenceValidator.php	 * source<?php

declare(strict_types=1);

namespace JsonSchema\Tool\Validator;

class RelativeReferenceValidator
{
    public static function isValid(string $ref): bool
    {
        // Relative reference pattern as per RFC 3986, Section 4.1
        $pattern = '/^(([^\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/';

        if (preg_match($pattern, $ref) !== 1) {
            return false;
        }

        // Additional checks for invalid cases
        if (preg_match('/^(http|https):\/\//', $ref)) {
            return false; // Absolute URI
        }

        if (preg_match('/^:\/\//', $ref)) {
            return false; // Missing scheme in authority
        }

        if (preg_match('/^:\//', $ref)) {
            return false; // Invalid scheme separator
        }

        if (preg_match('/^\/\/$/', $ref)) {
            return false; // Empty authority
        }

        if (preg_match('/^\/\/\/[^\/]/', $ref)) {
            return false; // Invalid authority with three slashes
        }

        if (preg_match('/\s/', $ref)) {
            return false; // Spaces are not allowed in URIs
        }

        if (preg_match('/^\?#|^#$/', $ref)) {
            return false; // Missing path but having query and fragment
        }

        if ($ref === '#' || $ref === '?') {
            return false; // Missing path and having only fragment or query
        }

        return true;
    }
}
 * namespaceAliases\JsonSchema\Tool\ValidatorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Validator * includes * constants * functions
 * classes5\JsonSchema\Tool\Validator\RelativeReferenceValidator(phpDocumentor\Descriptor\ClassDescriptor#$+%RelativeReferenceValidator-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./50   * readOnly * final * abstract
 * methodsisValid)phpDocumentor\Descriptor\MethodDescriptor#$@\JsonSchema\Tool\Validator\RelativeReferenceValidator::isValid()%55" 
	 
param  ./	0n./40{ 	 * parent" * argumentsref+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\String_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicBC	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\Boolean ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(9 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums