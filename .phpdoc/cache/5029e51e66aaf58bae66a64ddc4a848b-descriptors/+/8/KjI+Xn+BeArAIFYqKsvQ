1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-38d9abe5ba787791c14a31529e0357cd
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameannotations-to-attributes.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 94dafc785859dbef34dbc5fef21b52c4 * pathcvendor/rector/rector/vendor/rector/rector-symfony/config/sets/fosrest/annotations-to-attributes.php	 * source	j<?php

declare (strict_types=1);
namespace RectorPrefix202507;

use Rector\Config\RectorConfig;
use Rector\Php80\Rector\Class_\AnnotationToAttributeRector;
use Rector\Php80\ValueObject\AnnotationToAttribute;
return static function (RectorConfig $rectorConfig) : void {
    $rectorConfig->ruleWithConfiguration(AnnotationToAttributeRector::class, [
        // @see https://github.com/FriendsOfSymfony/FOSRestBundle/pull/2325
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Copy'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Delete'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Get'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Head'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Link'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Lock'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Mkcol'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Move'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Options'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Patch'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Post'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\PropFind'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\PropPatch'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Put'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Route'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Unlink'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\Unlock'),
        // @see https://github.com/FriendsOfSymfony/FOSRestBundle/pull/2326
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\View'),
        // @see https://github.com/FriendsOfSymfony/FOSRestBundle/pull/2327
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\FileParam'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\QueryParam'),
        new AnnotationToAttribute('FOS\\RestBundle\\Controller\\Annotations\\RequestParam'),
    ]);
};
 * namespaceAliases\RectorPrefix202507phpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRectorPrefix202507 * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums