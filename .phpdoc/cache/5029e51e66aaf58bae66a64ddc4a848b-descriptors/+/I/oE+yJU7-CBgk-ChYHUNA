1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-bb75b66e5cf98a5b1679aee10a79635b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameExpression.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 7be221f5a1ea5f21650a189ed1f35075 * path9vendor/illuminate/contracts/Database/Query/Expression.php	 * source7<?php

namespace Illuminate\Contracts\Database\Query;

use Illuminate\Database\Grammar;

interface Expression
{
    /**
     * Get the value of the expression.
     *
     * @param  \Illuminate\Database\Grammar  $grammar
     * @return string|int|float
     */
    public function getValue(Grammar $grammar);
}
 * namespaceAliases$\Illuminate\Contracts\Database\QueryphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameQuery * includes * constants * functions
 * classes
 * interfaces/\Illuminate\Contracts\Database\Query\Expression,phpDocumentor\Descriptor\InterfaceDescriptor#$,%
Expression."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methodsgetValue)phpDocumentor\Descriptor\MethodDescriptor#$;\Illuminate\Contracts\Database\Query\Expression::getValue()%44"  Get the value of the expression.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\Illuminate\Database\Grammar%Grammar * variableNamegrammarreturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorA	
 :'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer %phpDocumentor\Reflection\Types\Float_ 4 phpDocumentor\Reflection\Types\AggregatedType token|  /01	/013 	 * parent" * arguments@+phpDocumentor\Descriptor\ArgumentDescriptor @
 
	"'
  "5"6 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicPQ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums