**********
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-aafc1261dba9d7bae5a451ab80fd319d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameSymfony6SetProvider.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 758030ad2d0724c2ce444159294c6747 * path]vendor/rector/rector/vendor/rector/rector-symfony/src/Set/SetProvider/Symfony6SetProvider.php	 * source!"<?php

declare (strict_types=1);
namespace Rector\Symfony\Set\SetProvider;

use Rector\Set\Contract\SetInterface;
use Rector\Set\Contract\SetProviderInterface;
use Rector\Set\Enum\SetGroup;
use Rector\Set\ValueObject\ComposerTriggeredSet;
final class Symfony6SetProvider implements SetProviderInterface
{
    /**
     * @return SetInterface[]
     */
    public function provide() : array
    {
        return [new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/dependency-injection', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-dependency-injection.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/contracts', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-contracts.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/config', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-config.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/framework-bundle', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-framework-bundle.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/doctrine-bridge', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-doctrine-bridge.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-core', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-security-core.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/serializer', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-serializer.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-http', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-security-http.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/console', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-console.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/browser-kit', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-browser-kit.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-kernel', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-http-kernel.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/validator', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-validator.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/form', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-form.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/translation', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-translation.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/property-access', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-property-access.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/property-info', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-property-info.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/routing', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-routing.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/templating', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-templating.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/event-dispatcher', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-event-dispatcher.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/expression-language', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-expression-language.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/options-resolver', '6.0', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony60/symfony60-options-resolver.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '6.1', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony61.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/serializer', '6.1', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony61/symfony61-serializer.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/validator', '6.1', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony61/symfony61-validator.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/console', '6.1', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony61/symfony61-console.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/twig-bridge', '6.1', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony61/symfony61-twig-bridge.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-core', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-security-core.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/security-http', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-security-http.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/mime', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-mime.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-kernel', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-http-kernel.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/framework-bundle', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-framework-bundle.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-foundation', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-http-foundation.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/twig-bridge', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-twig-bridge.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/translation', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-translation.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/doctrine-bridge', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-doctrine-bridge.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/messenger', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-messenger.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/mail-pace-mailer', '6.2', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony62/symfony62-mail-pace-mailer.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '6.3', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony63.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/dependency-injection', '6.3', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony63/symfony63-dependency-injection.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-client', '6.3', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony63/symfony63-http-client.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/messenger', '6.3', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony63/symfony63-messenger.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/console', '6.3', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony63/symfony63-console.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/web-link', '6.3', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony63/symfony63-web-link.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/symfony', '6.4', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony64.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/routing', '6.4', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony64/symfony64-routing.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/form', '6.4', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony64/symfony64-form.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/http-foundation', '6.4', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony64/symfony64-http-foundation.php'), new ComposerTriggeredSet(SetGroup::SYMFONY, 'symfony/error-handler', '6.4', __DIR__ . '/../../../config/sets/symfony/symfony6/symfony64/symfony64-error-handler.php')];
    }
}
 * namespaceAliases\Rector\Symfony\Set\SetProviderphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSetProvider * includes * constants * functions
 * classes3\Rector\Symfony\Set\SetProvider\Symfony6SetProvider(phpDocumentor\Descriptor\ClassDescriptor#$+%Symfony6SetProvider-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methodsprovide)phpDocumentor\Descriptor\MethodDescriptor#$>\Rector\Symfony\Set\SetProvider\Symfony6SetProvider::provide()%55" 
	

return -phpDocumentor\Descriptor\Tag\ReturnDescriptor8	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$!\Rector\Set\Contract\SetInterface%SetInterface
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|  ./0c./0! 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType;<%phpDocumentor\Reflection\Types\Mixed_ A BCD E F GH? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(I 
 * implements)\Rector\Set\Contract\SetProviderInterface#$W%SetProviderInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums