1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-1d9537a7340fff1f40736ec672e5da55
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameAssertionError.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 541cb27db2e80ca345ceed60ccbbe964 * pathAvendor/hamcrest/hamcrest-php/hamcrest/Hamcrest/AssertionError.php	 * sourcew<?php
namespace Hamcrest;

/*
 Copyright (c) 2009 hamcrest.org
 */

class AssertionError extends \RuntimeException
{
}
 * namespaceAliases	\HamcrestphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameHamcrest * includes * constants * functions
 * classes\Hamcrest\AssertionError(phpDocumentor\Descriptor\ClassDescriptor#$+%AssertionError-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./
0   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$\RuntimeException%RuntimeException
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums