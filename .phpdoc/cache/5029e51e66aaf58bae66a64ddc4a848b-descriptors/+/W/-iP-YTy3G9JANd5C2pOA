1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-491c65ed567fc77a6c495df97df65c07
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nametrace.html.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash c1f43d04df2bffcdf6ac0522095c4112 * path;vendor/symfony/error-handler/Resources/views/trace.html.php	 * source
<div class="trace-line-header break-long-words <?= $trace['file'] ? 'sf-toggle' : ''; ?>" data-toggle-selector="#trace-html-<?= $prefix; ?>-<?= $i; ?>" data-toggle-initial="<?= 'expanded' === $style ? 'display' : ''; ?>">
    <?php if ($trace['file']) { ?>
        <span class="icon icon-close"><?= $this->include('assets/images/icon-minus-square.svg'); ?></span>
        <span class="icon icon-open"><?= $this->include('assets/images/icon-plus-square.svg'); ?></span>
    <?php } ?>

    <?php if ('compact' !== $style && $trace['function']) { ?>
        <span class="trace-class"><?= $this->abbrClass($trace['class']); ?></span><?php if ($trace['type']) { ?><span class="trace-type"><?= $trace['type']; ?></span><?php } ?><span class="trace-method"><?= $trace['function']; ?></span><?php if (isset($trace['args'])) { ?><span class="trace-arguments">(<?= $this->formatArgs($trace['args']); ?>)</span><?php } ?>
    <?php } ?>

    <?php if ($trace['file']) { ?>
        <?php
        $lineNumber = $trace['line'] ?: 1;
        $fileLink = $this->fileLinkFormat->format($trace['file'], $lineNumber);
        $filePath = strtr(strip_tags($this->formatFile($trace['file'], $lineNumber)), [' at line '.$lineNumber => '']);
        $filePathParts = explode(\DIRECTORY_SEPARATOR, $filePath);
        ?>
        <span class="block trace-file-path">
            in
            <a href="<?= $fileLink; ?>">
                <?= implode(\DIRECTORY_SEPARATOR, array_slice($filePathParts, 0, -1)).\DIRECTORY_SEPARATOR; ?><strong><?= end($filePathParts); ?></strong>
            </a>
            <?php if ('compact' === $style && $trace['function']) { ?>
                <span class="trace-type"><?= $trace['type']; ?></span>
                <span class="trace-method"><?= $trace['function']; ?></span>
            <?php } ?>
            (line <?= $lineNumber; ?>)
            <span class="icon icon-copy hidden" data-clipboard-text="<?php echo implode(\DIRECTORY_SEPARATOR, $filePathParts).':'.$lineNumber; ?>">
                <?php echo $this->include('assets/images/icon-copy.svg'); ?>
            </span>
        </span>
    <?php } ?>
</div>
<?php if ($trace['file']) { ?>
    <div id="trace-html-<?= $prefix.'-'.$i; ?>" class="trace-code sf-toggle-content">
        <?= strtr($this->fileExcerpt($trace['file'], $trace['line'], 5), [
            '#DD0000' => 'var(--highlight-string)',
            '#007700' => 'var(--highlight-keyword)',
            '#0000BB' => 'var(--highlight-default)',
            '#FF8000' => 'var(--highlight-comment)',
        ]); ?>
    </div>
<?php } ?>
 * namespaceAliases * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums