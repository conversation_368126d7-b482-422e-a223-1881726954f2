1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-f6aadbc1222f644dc942bcbeff8c3125
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRemovalSet.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 735718a918a9d131f87f2a88d383dfe9 * pathBvendor/pestphp/pest-plugin-mutate/src/Mutators/Sets/RemovalSet.php	 * source<?php

declare(strict_types=1);

namespace Pest\Mutate\Mutators\Sets;

use Pest\Mutate\Contracts\MutatorSet;
use Pest\Mutate\Mutators\Concerns\HasName;
use Pest\Mutate\Mutators\Removal\RemoveArrayItem;
use Pest\Mutate\Mutators\Removal\RemoveEarlyReturn;
use Pest\Mutate\Mutators\Removal\RemoveFunctionCall;
use Pest\Mutate\Mutators\Removal\RemoveMethodCall;
use Pest\Mutate\Mutators\Removal\RemoveNullSafeOperator;

class RemovalSet implements MutatorSet
{
    use HasName;

    /**
     * {@inheritDoc}
     */
    public static function mutators(): array
    {
        return [
            RemoveArrayItem::class,
            RemoveEarlyReturn::class,
            RemoveFunctionCall::class,
            RemoveMethodCall::class,
            RemoveNullSafeOperator::class,
        ];
    }
}
 * namespaceAliases\Pest\Mutate\Mutators\SetsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSets * includes * constants * functions
 * classes%\Pest\Mutate\Mutators\Sets\RemovalSet(phpDocumentor\Descriptor\ClassDescriptor#$+%
RemovalSet-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./ 0   * readOnly * final * abstract
 * methodsmutators)phpDocumentor\Descriptor\MethodDescriptor#$1\Pest\Mutate\Mutators\Sets\RemovalSet::mutators()%55" 
{@inheritDoc}	

  ./0./0 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(9 
 * implements!\Pest\Mutate\Contracts\MutatorSet#$Q%
MutatorSet
 * usedTraits&\Pest\Mutate\Mutators\Concerns\HasName#$T%HasName 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums