1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-cc70ece1ec29f6403215c6206da1ca7d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Panicable.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 80721b437a2ccf9e4ed71ab2281b7249 * path/vendor/pestphp/pest/src/Contracts/Panicable.php	 * sourcep<?php

declare(strict_types=1);

namespace Pest\Contracts;

use Symfony\Component\Console\Output\OutputInterface;

/**
 * @internal
 */
interface Panicable
{
    /**
     * Renders the panic on the given output.
     */
    public function render(OutputInterface $output): void;

    /**
     * The exit code to be used.
     */
    public function exitCode(): int;
}
 * namespaceAliases\Pest\ContractsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Contracts * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums