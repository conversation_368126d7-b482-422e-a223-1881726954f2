1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-25eb8765234e64f06bcca7a4cb6b2f7a
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
make-rule.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash d9be53c698e83d0f5f02dd78fe75d6c6 * path7vendor/driftingly/rector-laravel/commands/make-rule.php	 * source~#!/usr/bin/env php
<?php

require __DIR__ . '/../vendor/autoload.php';

// Parse command line arguments
$configurable = false;
$ruleName = '';

// Parse options
foreach ($argv as $i => $arg) {
    if ($i === 0) {
        continue;
    } // Skip script name

    if ($arg === '--configurable' || $arg === '-c') {
        $configurable = true;
    } elseif (empty($ruleName) && ! str_starts_with($arg, '-')) {
        // If it's not an option, and we don't have a rule name yet, it's the rule name
        $ruleName = $arg;
    }
}

$command = new \RectorLaravel\Commands\MakeRuleCommand;
exit($command->execute($ruleName, $configurable));
 * namespaceAliases * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums