1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-32ee31b7019fcb7f07e1868683096227
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameTargetEntityResolver.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ef34e2b7f2d3914e9595ca87e53b0442 * path\vendor/rector/rector/vendor/rector/rector-doctrine/src/NodeAnalyzer/TargetEntityResolver.php	 * sourceW<?php

declare (strict_types=1);
namespace Rector\Doctrine\NodeAnalyzer;

use PhpParser\Node\Attribute;
use PhpParser\Node\Expr;
use PhpParser\Node\Expr\ClassConstFetch;
use PhpParser\Node\Identifier;
use PhpParser\Node\Scalar\String_;
use PHPStan\Reflection\ReflectionProvider;
use Rector\Doctrine\CodeQuality\Enum\EntityMappingKey;
use Rector\Exception\NotImplementedYetException;
use Rector\NodeNameResolver\NodeNameResolver;
final class TargetEntityResolver
{
    /**
     * @readonly
     */
    private NodeNameResolver $nodeNameResolver;
    /**
     * @readonly
     */
    private ReflectionProvider $reflectionProvider;
    public function __construct(NodeNameResolver $nodeNameResolver, ReflectionProvider $reflectionProvider)
    {
        $this->nodeNameResolver = $nodeNameResolver;
        $this->reflectionProvider = $reflectionProvider;
    }
    public function resolveFromAttribute(Attribute $attribute) : ?string
    {
        foreach ($attribute->args as $arg) {
            if (!$arg->name instanceof Identifier) {
                continue;
            }
            if ($arg->name->toString() !== EntityMappingKey::TARGET_ENTITY) {
                continue;
            }
            return $this->resolveFromExpr($arg->value);
        }
        return null;
    }
    public function resolveFromExpr(Expr $targetEntityExpr) : ?string
    {
        if ($targetEntityExpr instanceof ClassConstFetch) {
            $targetEntity = (string) $this->nodeNameResolver->getName($targetEntityExpr->class);
            if (!$this->reflectionProvider->hasClass($targetEntity)) {
                return null;
            }
            return $targetEntity;
        }
        if ($targetEntityExpr instanceof String_) {
            $targetEntity = $targetEntityExpr->value;
            if (!$this->reflectionProvider->hasClass($targetEntity)) {
                return null;
            }
            return $targetEntity;
        }
        $errorMessage = \sprintf('Add support for "%s" targetEntity in "%s"', \get_class($targetEntityExpr), self::class);
        throw new NotImplementedYetException($errorMessage);
    }
}
 * namespaceAliases\Rector\Doctrine\NodeAnalyzerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameNodeAnalyzer * includes * constants * functions
 * classes2\Rector\Doctrine\NodeAnalyzer\TargetEntityResolver(phpDocumentor\Descriptor\ClassDescriptor#$+%TargetEntityResolver-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./>0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$A\Rector\Doctrine\NodeAnalyzer\TargetEntityResolver::__construct()%55" 
	 
param  ./0z./0Z 	 * parent" * argumentsnodeNameResolver+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$)\Rector\NodeNameResolver\NodeNameResolver%NodeNameResolver
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEFreflectionProvider< H
 
	 
  " "! =">?@#$&\PHPStan\Reflection\ReflectionProvider%ReflectionProviderC DEFGEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  resolveFromAttribute6#$J\Rector\Doctrine\NodeAnalyzer\TargetEntityResolver::resolveFromAttribute()%UU" 
	 
8  ./0`./*0 9":	attribute< W
 
	 
  ";"< ="6>?@#$\PhpParser\Node\Attribute%	AttributeC DEFGEFKL'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realType&phpDocumentor\Reflection\Types\String_ N23OPQ"5T  resolveFromExpr6#$E\Rector\Doctrine\NodeAnalyzer\TargetEntityResolver::resolveFromExpr()%]]" 
	 
8  ./+0./=0S 9":targetEntityExpr< _
 
	 
  "O"P ="J>?@#$\PhpParser\Node\Expr%ExprC DEFGEFKLZ[\ N23OPQ"5T  
 * properties;+phpDocumentor\Descriptor\PropertyDescriptor#$E\Rector\Doctrine\NodeAnalyzer\TargetEntityResolver::$nodeNameResolver%;;+ 
	

readonly e	
 var  ./0 ./0  9"K5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualOPQR'PRIVATET >?@#$A%BC  Hc#$G\Rector\Doctrine\NodeAnalyzer\TargetEntityResolver::$reflectionProvider%HH+ 
	

e e	
 f  ./0 ./0  9"KghijOPQ"qT >?@#$I%JC  (9 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums