1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-fd49ae0cb1fb4f98d5a3241381e6aa94
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameChainCacheClearer.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 7eb64b6b69ca6c2ac046b526aae9027e * path=vendor/symfony/http-kernel/CacheClearer/ChainCacheClearer.php	 * source
<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\CacheClearer;

/**
 * ChainCacheClearer.
 *
 * <AUTHOR> Dobervich <<EMAIL>>
 *
 * @final
 */
class ChainCacheClearer implements CacheClearerInterface
{
    /**
     * @param iterable<mixed, CacheClearerInterface> $clearers
     */
    public function __construct(
        private iterable $clearers = [],
    ) {
    }

    public function clear(string $cacheDir): void
    {
        foreach ($this->clearers as $clearer) {
            $clearer->clear($cacheDir);
        }
    }
}
 * namespaceAliases*\Symfony\Component\HttpKernel\CacheClearerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameCacheClearer * includes * constants * functions
 * classes<\Symfony\Component\HttpKernel\CacheClearer\ChainCacheClearer(phpDocumentor\Descriptor\ClassDescriptor#$+%ChainCacheClearer-"ChainCacheClearer.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	'Dustin Dobervich <<EMAIL>> final 2	
 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 34%5   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$K\Symfony\Component\HttpKernel\CacheClearer\ChainCacheClearer::__construct()%::" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor=	
  * type(phpDocumentor\Reflection\Types\Iterable_ * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$@\Symfony\Component\HttpKernel\CacheClearer\CacheClearerInterface%CacheClearerInterface
 * keyType%phpDocumentor\Reflection\Types\Mixed_  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNameclearers  345345e 	 * parent" * argumentsP+phpDocumentor\Descriptor\ArgumentDescriptor P
 
	"0
  ":"; 3 phpDocumentor\Descriptor\ArgumentDescriptor method"'?"2
 * default[] * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicXY	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnTypeG ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference78
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  clear;#$E\Symfony\Component\HttpKernel\CacheClearer\ChainCacheClearer::clear()%dd" 
	 
=  345l34$5 Q"RcacheDirS f
 
	 
  "L"M T"G?K U WXYZXY[\$phpDocumentor\Reflection\Types\Void_ ]78^_`"Fc  
 * propertiesP+phpDocumentor\Descriptor\PropertyDescriptor#$G\Symfony\Component\HttpKernel\CacheClearer\ChainCacheClearer::$clearers%PP+ 
	 
var  3458345V Q"[5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtual^_`a'PRIVATEc ?@AG F HIJ K L MNUV (Q 
 * implementsD#$D%E
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums