1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0899e925e727aa89e6b65f0894bedb8e
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameBootstrapFilesIncluder.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 60d71fe29d75c6cf482cb6a72dfecc8e * path?vendor/rector/rector/src/Autoloading/BootstrapFilesIncluder.php	 * sources<?php

declare (strict_types=1);
namespace Rector\Autoloading;

use Rector\Configuration\Option;
use Rector\Configuration\Parameter\SimpleParameterProvider;
use Rector\Exception\ShouldNotHappenException;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use SplFileInfo;
use RectorPrefix202507\Webmozart\Assert\Assert;
/**
 * @see \Rector\Tests\Autoloading\BootstrapFilesIncluderTest
 */
final class BootstrapFilesIncluder
{
    /**
     * Inspired by
     * @see https://github.com/phpstan/phpstan-src/commit/aad1bf888ab7b5808898ee5fe2228bb8bb4e4cf1
     */
    public function includeBootstrapFiles() : void
    {
        $bootstrapFiles = SimpleParameterProvider::provideArrayParameter(Option::BOOTSTRAP_FILES);
        Assert::allString($bootstrapFiles);
        /** @var string[] $bootstrapFiles */
        foreach ($bootstrapFiles as $bootstrapFile) {
            if (!\is_file($bootstrapFile)) {
                throw new ShouldNotHappenException(\sprintf('Bootstrap file "%s" does not exist.', $bootstrapFile));
            }
            require $bootstrapFile;
        }
        $this->requireRectorStubs();
    }
    private function requireRectorStubs() : void
    {
        $stubsRectorDirectory = \realpath(__DIR__ . '/../../stubs-rector');
        if ($stubsRectorDirectory === \false) {
            return;
        }
        $dir = new RecursiveDirectoryIterator($stubsRectorDirectory, RecursiveDirectoryIterator::SKIP_DOTS);
        $stubs = new RecursiveIteratorIterator($dir);
        foreach ($stubs as $stub) {
            /** @var SplFileInfo $stub */
            require_once $stub->getRealPath();
        }
    }
}
 * namespaceAliases\Rector\AutoloadingphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameAutoloading * includes * constants * functions
 * classes*\Rector\Autoloading\BootstrapFilesIncluder(phpDocumentor\Descriptor\ClassDescriptor#$+%BootstrapFilesIncluder-"
	

see *phpDocumentor\Descriptor\Tag\SeeDescriptor.	
 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference6phpDocumentor\Reflection\DocBlock\Tags\Reference\Fqsen= phpDocumentor\Reflection\DocBlock\Tags\Reference\Fqsen fqsen#$4\Rector\Tests\Autoloading\BootstrapFilesIncluderTest%BootstrapFilesIncluderTest
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 5607   * readOnly * final * abstract
 * methodsincludeBootstrapFiles)phpDocumentor\Descriptor\MethodDescriptor#$C\Rector\Autoloading\BootstrapFilesIncluder::includeBootstrapFiles()%<<" Inspired by	

. /.	
 04phpDocumentor\Reflection\DocBlock\Tags\Reference\Url9 phpDocumentor\Reflection\DocBlock\Tags\Reference\Url uriVhttps://github.com/phpstan/phpstan-src/commit/aad1bf888ab7b5808898ee5fe2228bb8bb4e4cf1  567@56"7n 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference9:
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  requireRectorStubs=#$@\Rector\Autoloading\BootstrapFilesIncluder::requireRectorStubs()%OO" 
	 
  56#7t56/7o C"DEFG H9:IJKL'PRIVATEN  
 * properties(C 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums