1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-a7132e9ea7495fe768021c380700bf18
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameStringConstraint.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash f9d61ad1567fa1b547a03fe00b0f22c4 * pathPvendor/justinrainbow/json-schema/src/JsonSchema/Constraints/StringConstraint.php	 * sourceo<?php

declare(strict_types=1);

/*
 * This file is part of the JsonSchema package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace JsonSchema\Constraints;

use JsonSchema\ConstraintError;
use JsonSchema\Entity\JsonPointer;

/**
 * The StringConstraint Constraints, validates an string against a given schema
 *
 * <AUTHOR> Schönthal <<EMAIL>>
 * <AUTHOR> Prieto Reis <<EMAIL>>
 */
class StringConstraint extends Constraint
{
    /**
     * {@inheritdoc}
     */
    public function check(&$element, $schema = null, ?JsonPointer $path = null, $i = null): void
    {
        // Verify maxLength
        if (isset($schema->maxLength) && $this->strlen($element) > $schema->maxLength) {
            $this->addError(ConstraintError::LENGTH_MAX(), $path, [
                'maxLength' => $schema->maxLength,
            ]);
        }

        //verify minLength
        if (isset($schema->minLength) && $this->strlen($element) < $schema->minLength) {
            $this->addError(ConstraintError::LENGTH_MIN(), $path, [
                'minLength' => $schema->minLength,
            ]);
        }

        // Verify a regex pattern
        if (isset($schema->pattern) && !preg_match(self::jsonPatternToPhpRegex($schema->pattern), $element)) {
            $this->addError(ConstraintError::PATTERN(), $path, [
                'pattern' => $schema->pattern,
            ]);
        }

        $this->checkFormat($element, $schema, $path, $i);
    }

    private function strlen($string)
    {
        if (extension_loaded('mbstring')) {
            return mb_strlen($string, mb_detect_encoding($string));
        }

        // mbstring is present on all test platforms, so strlen() can be ignored for coverage
        return strlen($string); // @codeCoverageIgnore
    }
}
 * namespaceAliases\JsonSchema\ConstraintsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameConstraints * includes * constants * functions
 * classes(\JsonSchema\Constraints\StringConstraint(phpDocumentor\Descriptor\ClassDescriptor#$+%StringConstraint-"LThe StringConstraint Constraints, validates an string against a given schema	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	+Robert Schönthal <<EMAIL>> 0/	*Bruno Prieto Reis <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 34?5   * readOnly * final * abstract
 * methodscheck)phpDocumentor\Descriptor\MethodDescriptor#$1\JsonSchema\Constraints\StringConstraint::check()%::" 
{@inheritdoc}	

param  345_3445) 	 * parent" * argumentselement+phpDocumentor\Descriptor\ArgumentDescriptor A
 
	 
  ","- 3 phpDocumentor\Descriptor\ArgumentDescriptor method"% * type%phpDocumentor\Reflection\Types\Mixed_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicHIschemaB K
 
	 
  ","- C"%DE FnullGHIJHIpathB M
 
	 
  ","- C"%D'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\JsonSchema\Entity\JsonPointer%JsonPointerFLGHIJHIiB T
 
	 
  ","- C"%DE FLGHIJHI	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference78
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  strlen;#$2\JsonSchema\Constraints\StringConstraint::strlen()%__" 
	 
>  3465034>5k ?"@stringB a
 
	 
  "S"T C"NDE F GHIJHIUVE X78YZ[\'PRIVATE^  
 * properties(?#$"\JsonSchema\Constraints\Constraint%
Constraint
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums