1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-88607af5c30a534f82c3ce801dcdc6c4
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameStaticGuard.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash aee570324b98fe7aeb309b8761b4bb4c * path<vendor/rector/rector/rules/CodingStyle/Guard/StaticGuard.php	 * sourceF<?php

declare (strict_types=1);
namespace Rector\CodingStyle\Guard;

use PhpParser\Node;
use PhpParser\Node\Expr\ArrowFunction;
use PhpParser\Node\Expr\Closure;
use PhpParser\Node\Expr\StaticCall;
use PhpParser\Node\Expr\Variable;
use PHPStan\Reflection\MethodReflection;
use Rector\PhpParser\Node\BetterNodeFinder;
use Rector\Reflection\ReflectionResolver;
final class StaticGuard
{
    /**
     * @readonly
     */
    private BetterNodeFinder $betterNodeFinder;
    /**
     * @readonly
     */
    private ReflectionResolver $reflectionResolver;
    public function __construct(BetterNodeFinder $betterNodeFinder, ReflectionResolver $reflectionResolver)
    {
        $this->betterNodeFinder = $betterNodeFinder;
        $this->reflectionResolver = $reflectionResolver;
    }
    /**
     * @param \PhpParser\Node\Expr\Closure|\PhpParser\Node\Expr\ArrowFunction $node
     */
    public function isLegal($node) : bool
    {
        if ($node->static) {
            return \false;
        }
        $nodes = $node instanceof Closure ? $node->stmts : [$node->expr];
        return !(bool) $this->betterNodeFinder->findFirst($nodes, function (Node $subNode) : bool {
            if (!$subNode instanceof StaticCall) {
                return $subNode instanceof Variable && $subNode->name === 'this';
            }
            $methodReflection = $this->reflectionResolver->resolveMethodReflectionFromStaticCall($subNode);
            if (!$methodReflection instanceof MethodReflection) {
                return \false;
            }
            return !$methodReflection->isStatic();
        });
    }
}
 * namespaceAliases\Rector\CodingStyle\GuardphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameGuard * includes * constants * functions
 * classes%\Rector\CodingStyle\Guard\StaticGuard(phpDocumentor\Descriptor\ClassDescriptor#$+%StaticGuard-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./10   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$4\Rector\CodingStyle\Guard\StaticGuard::__construct()%55" 
	 
param  ./0+./0 	 * parent" * argumentsbetterNodeFinder+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$'\Rector\PhpParser\Node\BetterNodeFinder%BetterNodeFinder
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEFreflectionResolver< H
 
	 
  " "! =">?@#$%\Rector\Reflection\ReflectionResolver%ReflectionResolverC DEFGEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  isLegal6#$0\Rector\CodingStyle\Guard\StaticGuard::isLegal()%UU" 
	

8 ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
 >'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types ?@#$\PhpParser\Node\Expr\Closure%Closure?@#$"\PhpParser\Node\Expr\ArrowFunction%
ArrowFunction4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNamenode  ./ 0u./00B 9":a< a
 
	"?
  "G"H ="6>"AC DEFGEFKL&phpDocumentor\Reflection\Types\Boolean N23OPQ"5T  
 * properties;+phpDocumentor\Descriptor\PropertyDescriptor#$8\Rector\CodingStyle\Guard\StaticGuard::$betterNodeFinder%;;+ 
	

readonly f	
 var  ./0 ./0  9"K5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualOPQR'PRIVATET >?@#$A%BC  Hd#$:\Rector\CodingStyle\Guard\StaticGuard::$reflectionResolver%HH+ 
	

f f	
 g  ./0 ./0  9"KhijkOPQ"fT >?@#$I%JC  (9 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums