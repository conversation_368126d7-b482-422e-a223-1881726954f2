1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-cc5f62c4ec309e948dba7702c2784d0b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
MatchKind.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ea663bbd0c5752eecb4b26a47e7793f8 * path3vendor/rector/rector/rules/Php80/Enum/MatchKind.php	 * sources<?php

declare (strict_types=1);
namespace Rector\Php80\Enum;

final class MatchKind
{
    /**
     * @var string
     */
    public const NORMAL = 'normal';
    /**
     * @var string
     */
    public const ASSIGN = 'assign';
    /**
     * @var string
     */
    public const RETURN = 'return';
    /**
     * @var string
     */
    public const THROW = 'throw';
}
 * namespaceAliases\Rector\Php80\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes\Rector\Php80\Enum\MatchKind(phpDocumentor\Descriptor\ClassDescriptor#$+%	MatchKind-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(NORMAL+phpDocumentor\Descriptor\ConstantDescriptor#$$\Rector\Php80\Enum\MatchKind::NORMAL%66+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor9	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  ./0 ./0   * value'normal'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent";  ASSIGN7#$$\Rector\Php80\Enum\MatchKind::ASSIGN%GG+ 
	

9 :9	
 ;< =
  ./0 ./0  >'assign'2@AB"-E F";  RETURN7#$$\Rector\Php80\Enum\MatchKind::RETURN%JJ+ 
	

9 :9	
 ;< =
  ./0 ./0  >'return'2@AB"-E F";  THROW7#$#\Rector\Php80\Enum\MatchKind::THROW%MM+ 
	

9 :9	
 ;< =
  ./0 ./0  >'throw'2@AB"-E F";  F 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums