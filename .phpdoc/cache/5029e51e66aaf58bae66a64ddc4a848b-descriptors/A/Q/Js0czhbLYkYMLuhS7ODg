1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-1ea2ebde4b1d5d7acc9eb188150a3c9d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePHPUnitClassName.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 5154648fc76e2501eb06c5f780171d37 * pathOvendor/rector/rector/vendor/rector/rector-phpunit/src/Enum/PHPUnitClassName.php	 * source<?php

declare (strict_types=1);
namespace Rector\PHPUnit\Enum;

final class PHPUnitClassName
{
    /**
     * @var string
     */
    public const TEST_CASE = 'PHPUnit\\Framework\\TestCase';
    /**
     * @var string
     */
    public const ASSERT = 'PHPUnit\\Framework\\Assert';
}
 * namespaceAliases\Rector\PHPUnit\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes%\Rector\PHPUnit\Enum\PHPUnitClassName(phpDocumentor\Descriptor\ClassDescriptor#$+%PHPUnitClassName-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(	TEST_CASE+phpDocumentor\Descriptor\ConstantDescriptor#$0\Rector\PHPUnit\Enum\PHPUnitClassName::TEST_CASE%66+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor9	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  ./0 ./0   * value'PHPUnit\Framework\TestCase'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent";  ASSERT7#$-\Rector\PHPUnit\Enum\PHPUnitClassName::ASSERT%GG+ 
	

9 :9	
 ;< =
  ./0 ./0  >'PHPUnit\Framework\Assert'2@AB"-E F";  F 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums