1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-53c868c157f355c483a1789b288cb90d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameNotJson.php * namespace

 * packageApplication
 * summary'Copyright (c) 2022-2024 Andreas <PERSON>ller * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate{For the full copyright and license information, please view
the LICENSE.md file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * itemssee *phpDocumentor\Descriptor\Tag\SeeDescriptor


 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference4phpDocumentor\Reflection\DocBlock\Tags\Reference\Url9 phpDocumentor\Reflection\DocBlock\Tags\Reference\Url uri https://github.com/ergebnis/jsonpackage &phpDocumentor\Descriptor\TagDescriptor

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4d8d0faa196c5de6efeb5deecb21ebfc * path.vendor/ergebnis/json/src/Exception/NotJson.php	 * source3<?php

declare(strict_types=1);

/**
 * Copyright (c) 2022-2024 Andreas Möller
 *
 * For the full copyright and license information, please view
 * the LICENSE.md file that was distributed with this source code.
 *
 * @see https://github.com/ergebnis/json
 */

namespace Ergebnis\Json\Exception;

final class NotJson extends \InvalidArgumentException implements Exception
{
    public static function value(string $value): self
    {
        return new self(\sprintf(
            'Value "%s" is not a valid JSON string.',
            $value,
        ));
    }
}
 * namespaceAliases\Ergebnis\Json\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen*$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classes \Ergebnis\Json\Exception\NotJson(phpDocumentor\Descriptor\ClassDescriptor+,3-NotJson5*

 ""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber  678 !"  * readOnly * final * abstract
 * methodsvalue)phpDocumentor\Descriptor\MethodDescriptor+,)\Ergebnis\Json\Exception\NotJson::value()-==* 

 param  678{ 678/!" 	 * parent" * arguments=+phpDocumentor\Descriptor\ArgumentDescriptor =
 

   "( ")!" 3 phpDocumentor\Descriptor\ArgumentDescriptor method"# * type&phpDocumentor\Reflection\Types\String_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicIJ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Self_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference:;
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties0A+,\InvalidArgumentException-InvalidArgumentException
 * implements"\Ergebnis\Json\Exception\Exception+,Z-.
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums