1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-a2520f895cda97aae65071dfe81c8366
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameValidatorAwareRule.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 5e3320400acbbb04a97bb2e3f4ba7c0d * path=vendor/illuminate/contracts/Validation/ValidatorAwareRule.php	 * source<<?php

namespace Illuminate\Contracts\Validation;

use Illuminate\Validation\Validator;

interface ValidatorAwareRule
{
    /**
     * Set the current validator.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return $this
     */
    public function setValidator(Validator $validator);
}
 * namespaceAliases \Illuminate\Contracts\ValidationphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Validation * includes * constants * functions
 * classes
 * interfaces3\Illuminate\Contracts\Validation\ValidatorAwareRule,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ValidatorAwareRule."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methodssetValidator)phpDocumentor\Descriptor\MethodDescriptor#$C\Illuminate\Contracts\Validation\ValidatorAwareRule::setValidator()%44" Set the current validator.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$ \Illuminate\Validation\Validator%	Validator * variableName	validatorreturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorA	
 :#phpDocumentor\Reflection\Types\This   /01/018 	 * parent" * arguments@+phpDocumentor\Descriptor\ArgumentDescriptor @
 
	"'
  "1"2 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicJK	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums