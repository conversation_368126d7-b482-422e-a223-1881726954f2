1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-3bf30b7f78a5154e5dedf7de74f07cf9
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameMigrationEvent.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 05c906d58f20437dfe4d3dffbc3bbe6c * pathSvendor/rector/rector/vendor/illuminate/contracts/Database/Events/MigrationEvent.php	 * sourceo<?php

namespace RectorPrefix202507\Illuminate\Contracts\Database\Events;

interface MigrationEvent
{
    //
}
 * namespaceAliases8\RectorPrefix202507\Illuminate\Contracts\Database\EventsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEvents * includes * constants * functions
 * classes
 * interfacesG\RectorPrefix202507\Illuminate\Contracts\Database\Events\MigrationEvent,phpDocumentor\Descriptor\InterfaceDescriptor#$,%MigrationEvent."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums