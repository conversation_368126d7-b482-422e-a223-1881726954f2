1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-654612cf39cfbf218cd1d9fd9a0cbf9a
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
symfony43.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash c2accd3cd5661c122f76a2a3d75e4684 * path\vendor/rector/rector/vendor/rector/rector-symfony/config/sets/symfony/symfony4/symfony43.php	 * source.<?php

declare (strict_types=1);
namespace RectorPrefix202507;

use Rector\Config\RectorConfig;
# https://github.com/symfony/symfony/blob/4.4/UPGRADE-4.3.md
return static function (RectorConfig $rectorConfig) : void {
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-browser-kit.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-cache.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-event-dispatcher.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-framework-bundle.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-http-foundation.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-http-kernel.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-intl.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-security-core.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-security-http.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-twig-bundle.php');
    $rectorConfig->import(__DIR__ . '/symfony43/symfony43-workflow.php');
};
 * namespaceAliases\RectorPrefix202507phpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRectorPrefix202507 * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums