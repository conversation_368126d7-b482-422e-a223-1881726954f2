1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-9a67ce579459a473f6f0f6d209169b2b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameDurationOptions.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash a474442b9cbbaed29a1355058cfd78c2 * path=vendor/pestphp/pest-plugin-stressless/src/DurationOptions.php	 * sourceR<?php

declare(strict_types=1);

namespace Pest\Stressless;

/**
 * @internal
 */
final readonly class DurationOptions
{
    /**
     * Creates a new stage duration options instance.
     */
    public function __construct(
        private Factory $factory,
        private int $duration,
    ) {
        //
    }

    /**
     * Specifies that the stage should run for 1 second.
     */
    public function second(): Factory
    {
        assert($this->duration === 1, 'The duration must be 1 second.');

        return $this->seconds();
    }

    /**
     * Specifies that the stage should run for the given number of seconds.
     */
    public function seconds(): Factory
    {
        return $this->factory->duration($this->duration);
    }

    /**
     * Specifies that the stage should run for 1 minute.
     */
    public function minute(): Factory
    {
        assert($this->duration === 1, 'The duration must be 1 minute.');

        return $this->minutes();
    }

    /**
     * Specifies that the stage should run for the given number of minutes.
     */
    public function minutes(): Factory
    {
        return $this->factory->duration($this->duration * 60);
    }

    /**
     * Specifies that the stage should run for 1 hour.
     */
    public function hour(): Factory
    {
        assert($this->duration === 1, 'The duration must be 1 hour.');

        return $this->hours();
    }

    /**
     * Specifies that the stage should run for the given number of hours.
     */
    public function hours(): Factory
    {
        return $this->factory->duration($this->duration * 60 * 60);
    }
}
 * namespaceAliases\Pest\StresslessphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Stressless * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums