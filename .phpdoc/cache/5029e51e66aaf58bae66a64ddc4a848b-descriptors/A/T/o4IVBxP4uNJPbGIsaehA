1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-d37fff19d703a7f87c0cdfad0600d797
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameSensioAttribute.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 62ddd40266be48ca9e166c0926d8f247 * pathNvendor/rector/rector/vendor/rector/rector-symfony/src/Enum/SensioAttribute.php	 * sourcei<?php

declare (strict_types=1);
namespace Rector\Symfony\Enum;

final class SensioAttribute
{
    /**
     * @var string
     */
    public const PARAM_CONVERTER = 'Sensio\\Bundle\\FrameworkExtraBundle\\Configuration\\ParamConverter';
    /**
     * @var string
     */
    public const ENTITY = 'Sensio\\Bundle\\FrameworkExtraBundle\\Configuration\\Entity';
    /**
     * @var string
     */
    public const METHOD = 'Sensio\\Bundle\\FrameworkExtraBundle\\Configuration\\Method';
    /**
     * @var string
     */
    public const TEMPLATE = 'Sensio\\Bundle\\FrameworkExtraBundle\\Configuration\\Template';
    /**
     * @var string
     */
    public const IS_GRANTED = 'Sensio\\Bundle\\FrameworkExtraBundle\\Configuration\\IsGranted';
    /**
     * @var string
     */
    public const SECURITY = 'Sensio\\Bundle\\FrameworkExtraBundle\\Configuration\\Security';
}
 * namespaceAliases\Rector\Symfony\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes$\Rector\Symfony\Enum\SensioAttribute(phpDocumentor\Descriptor\ClassDescriptor#$+%SensioAttribute-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./ 0   * readOnly * final * abstract
 * methods
 * properties(PARAM_CONVERTER+phpDocumentor\Descriptor\ConstantDescriptor#$5\Rector\Symfony\Enum\SensioAttribute::PARAM_CONVERTER%66+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor9	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  ./0 ./0   * valueA'Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent";  ENTITY7#$,\Rector\Symfony\Enum\SensioAttribute::ENTITY%GG+ 
	

9 :9	
 ;< =
  ./0 ./0  >9'Sensio\Bundle\FrameworkExtraBundle\Configuration\Entity'2@AB"-E F";  METHOD7#$,\Rector\Symfony\Enum\SensioAttribute::METHOD%JJ+ 
	

9 :9	
 ;< =
  ./0 ./0  >9'Sensio\Bundle\FrameworkExtraBundle\Configuration\Method'2@AB"-E F";  TEMPLATE7#$.\Rector\Symfony\Enum\SensioAttribute::TEMPLATE%MM+ 
	

9 :9	
 ;< =
  ./0 ./0  >;'Sensio\Bundle\FrameworkExtraBundle\Configuration\Template'2@AB"-E F";  
IS_GRANTED7#$0\Rector\Symfony\Enum\SensioAttribute::IS_GRANTED%PP+ 
	

9 :9	
 ;< =
  ./0 ./0  ><'Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted'2@AB"-E F";  SECURITY7#$.\Rector\Symfony\Enum\SensioAttribute::SECURITY%SS+ 
	

9 :9	
 ;< =
  ./0 ./0  >;'Sensio\Bundle\FrameworkExtraBundle\Configuration\Security'2@AB"-E F";  F 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums