1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-82db6340d495f753520ec999734b2b4a
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePharExtractor.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 6697f02ed522dcbabe943398cfc17bf4 * path4vendor/jolicode/jolinotif/src/Util/PharExtractor.php	 * sourceW<?php

/*
 * This file is part of the JoliNotif project.
 *
 * (c) Loïck Piera <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Joli\JoliNotif\Util;

/**
 * @internal
 */
class PharExtractor
{
    /**
     * Return whether the file path is located inside a phar.
     */
    public static function isLocatedInsideAPhar(string $filePath): bool
    {
        return str_starts_with($filePath, 'phar://');
    }

    /**
     * Extract the file from the phar archive to make it accessible for native commands.
     *
     * The absolute file path to extract should be passed in the first argument.
     */
    public static function extractFile(string $filePath, bool $overwrite = false): string
    {
        $pharPath = \Phar::running(false);

        if (!$pharPath) {
            return '';
        }

        $relativeFilePath = substr($filePath, strpos($filePath, $pharPath) + \strlen($pharPath) + 1);
        $tmpDir = sys_get_temp_dir() . '/jolinotif';
        $extractedFilePath = $tmpDir . '/' . $relativeFilePath;

        if (!file_exists($extractedFilePath) || $overwrite) {
            $phar = new \Phar($pharPath);
            $phar->extractTo($tmpDir, $relativeFilePath, $overwrite);
        }

        return $extractedFilePath;
    }
}
 * namespaceAliases\Joli\JoliNotif\UtilphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameUtil * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums