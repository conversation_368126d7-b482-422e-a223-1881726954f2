1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b38633f263868d87ddaaa6c87eafd4a7
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameMethodParameterFactory.php * namespace

 * packageApplication
 * summary#This file is part of phpDocumentor. * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplatexFor the full copyright and license information, please view the LICENSE
file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * itemslink +phpDocumentor\Descriptor\Tag\LinkDescriptor


  * linkhttp://phpdoc.orgpackage &phpDocumentor\Descriptor\TagDescriptor

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ded3f9571c75863dd166f314ca4fec0a * path]vendor/phpdocumentor/reflection-docblock/src/DocBlock/Tags/Factory/MethodParameterFactory.php	 * source	B<?php

declare(strict_types=1);

/**
 * This file is part of phpDocumentor.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @link      http://phpdoc.org
 */

namespace phpDocumentor\Reflection\DocBlock\Tags\Factory;

use function array_key_last;
use function get_class;
use function gettype;
use function method_exists;
use function ucfirst;
use function var_export;

/**
 * @internal This class is not part of the BC promise of this library.
 */
final class MethodParameterFactory
{
    /**
     * Formats the given default value to a string-able mixin
     *
     * @param mixed $defaultValue
     */
    public function format($defaultValue): string
    {
        $method = 'format' . ucfirst(gettype($defaultValue));
        if (method_exists($this, $method)) {
            return ' = ' . $this->{$method}($defaultValue);
        }

        return '';
    }

    private function formatDouble(float $defaultValue): string
    {
        return var_export($defaultValue, true);
    }

    /**
     * @param mixed $defaultValue
     */
    private function formatNull($defaultValue): string
    {
        return 'null';
    }

    private function formatInteger(int $defaultValue): string
    {
        return var_export($defaultValue, true);
    }

    private function formatString(string $defaultValue): string
    {
        return var_export($defaultValue, true);
    }

    private function formatBoolean(bool $defaultValue): string
    {
        return var_export($defaultValue, true);
    }

    /**
     * @param array<(array<mixed>|int|float|bool|string|object|null)> $defaultValue
     */
    private function formatArray(array $defaultValue): string
    {
        $formatedValue = '[';

        foreach ($defaultValue as $key => $value) {
            $method = 'format' . ucfirst(gettype($value));
            if (!method_exists($this, $method)) {
                continue;
            }

            $formatedValue .= $this->{$method}($value);

            if ($key === array_key_last($defaultValue)) {
                continue;
            }

            $formatedValue .= ',';
        }

        return $formatedValue . ']';
    }

    private function formatObject(object $defaultValue): string
    {
        return 'new ' . get_class($defaultValue) . '()';
    }
}
 * namespaceAliases/\phpDocumentor\Reflection\DocBlock\Tags\FactoryphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen($ phpDocumentor\Reflection\Fqsen nameFactory * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums