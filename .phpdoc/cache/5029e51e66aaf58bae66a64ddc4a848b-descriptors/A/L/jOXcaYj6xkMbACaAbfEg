1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-ab8c6834692989029d57dd1a695b5e59
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameUriFactoryInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4e587117e3edfd030a80e3b883cb75f9 * path3vendor/psr/http-factory/src/UriFactoryInterface.php	 * sourceE<?php

namespace Psr\Http\Message;

interface UriFactoryInterface
{
    /**
     * Create a new URI.
     *
     * @param string $uri
     *
     * @return UriInterface
     *
     * @throws \InvalidArgumentException If the given URI cannot be parsed.
     */
    public function createUri(string $uri = ''): UriInterface;
}
 * namespaceAliases\Psr\Http\MessagephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameMessage * includes * constants * functions
 * classes
 * interfaces%\Psr\Http\Message\UriFactoryInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%UriFactoryInterface."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods	createUri)phpDocumentor\Descriptor\MethodDescriptor#$2\Psr\Http\Message\UriFactoryInterface::createUri()%44" Create a new URI.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type&phpDocumentor\Reflection\Types\String_  * variableNameurireturn -phpDocumentor\Descriptor\Tag\ReturnDescriptor>	
 :&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\Psr\Http\Message\UriInterface%UriInterfacethrows -phpDocumentor\Descriptor\Tag\ThrowsDescriptorD	"If the given URI cannot be parsed. :@A#$\InvalidArgumentException%InvalidArgumentException  /01/01A 	 * parent" * arguments=+phpDocumentor\Descriptor\ArgumentDescriptor =
 
	"'
  "8"9 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * default'' * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicPQ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType@A#$B%C? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums