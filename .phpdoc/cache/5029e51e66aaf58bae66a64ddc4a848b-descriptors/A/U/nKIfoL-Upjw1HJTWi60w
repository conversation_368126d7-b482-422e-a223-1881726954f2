1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-7286c74ccf8b65cba8a7fc37feae4299
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameDataDumperInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 0d6420c0fc377476ac95db1cd6594b28 * path8vendor/symfony/var-dumper/Dumper/DataDumperInterface.php	 * source$<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\VarDumper\Dumper;

use Symfony\Component\VarDumper\Cloner\Data;

/**
 * DataDumperInterface for dumping Data objects.
 *
 * <AUTHOR> Grekas <<EMAIL>>
 */
interface DataDumperInterface
{
    /**
     * @return string|null
     */
    public function dump(Data $data);
}
 * namespaceAliases#\Symfony\Component\VarDumper\DumperphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDumper * includes * constants * functions
 * classes
 * interfaces7\Symfony\Component\VarDumper\Dumper\DataDumperInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%DataDumperInterface."
-DataDumperInterface for dumping Data objects.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor0	Nicolas Grekas <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 345  
 * parents(
 * methodsdump)phpDocumentor\Descriptor\MethodDescriptor#$?\Symfony\Component\VarDumper\Dumper\DataDumperInterface::dump()%88" 
	

return -phpDocumentor\Descriptor\Tag\ReturnDescriptor;	
  * type'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ $phpDocumentor\Reflection\Types\Null_ 4 phpDocumentor\Reflection\Types\AggregatedType token|param  345 345  	 * parent" * argumentsdata+phpDocumentor\Descriptor\ArgumentDescriptor G
 
	 
  "5"6 3 phpDocumentor\Descriptor\ArgumentDescriptor method"%=&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$(\Symfony\Component\VarDumper\Cloner\Data%Data
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicPQ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums