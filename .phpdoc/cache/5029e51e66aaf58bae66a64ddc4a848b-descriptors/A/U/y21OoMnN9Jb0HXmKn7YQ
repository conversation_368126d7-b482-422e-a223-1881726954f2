1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-77ec0d6296ea4c17ab48ab442552fd73
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameGenericArray.php * namespace

 * packageApplication
 * summary2This file is part of the ramsey/collection library * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplatexFor the full copyright and license information, please view the LICENSE
file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * items	copyright &phpDocumentor\Descriptor\TagDescriptor

,Copyright (c) <PERSON> <<EMAIL>> license 

&http://opensource.org/licenses/MIT MIT package 

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 2c9ac9d4d5bfec68968e0845607295f7 * path-vendor/ramsey/collection/src/GenericArray.php	 * source <?php

/**
 * This file is part of the ramsey/collection library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) Ben Ramsey <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 */

declare(strict_types=1);

namespace Ramsey\Collection;

/**
 * `GenericArray` represents a standard array object.
 *
 * @extends AbstractArray<mixed>
 */
class GenericArray extends AbstractArray
{
}
 * namespaceAliases\Ramsey\CollectionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen($ phpDocumentor\Reflection\Fqsen name
Collection * includes * constants * functions
 * classes\Ramsey\Collection\GenericArray(phpDocumentor\Descriptor\ClassDescriptor)*1+GenericArray3(2`GenericArray` represents a standard array object.


extends 5


 ""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 678    * readOnly * final * abstract
 * methods
 * properties.	 * parent)* \Ramsey\Collection\AbstractArray+
AbstractArray
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums