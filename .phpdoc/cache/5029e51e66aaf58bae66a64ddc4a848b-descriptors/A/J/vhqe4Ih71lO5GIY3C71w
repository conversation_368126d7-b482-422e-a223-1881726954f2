1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-d1467b5eb42fb9728276682a58b13482
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameInvalidArgumentException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash f070de2116d9d1ca836e68a404414d10 * path8vendor/psr/simple-cache/src/InvalidArgumentException.php	 * source<?php

namespace Psr\SimpleCache;

/**
 * Exception interface for invalid cache arguments.
 *
 * When an invalid argument is passed it must throw an exception which implements
 * this interface
 */
interface InvalidArgumentException extends CacheException
{
}
 * namespaceAliases\Psr\SimpleCachephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSimpleCache * includes * constants * functions
 * classes
 * interfaces)\Psr\SimpleCache\InvalidArgumentException,phpDocumentor\Descriptor\InterfaceDescriptor#$,%InvalidArgumentException."
0Exception interface for invalid cache arguments.	]When an invalid argument is passed it must throw an exception which implements
this interface

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 12
3  
 * parents\Psr\SimpleCache\CacheException#$5%CacheException(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums