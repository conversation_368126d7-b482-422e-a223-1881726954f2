**********
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-845561329e2b0f2c159c4e7bfdd965c0
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameFallbackNodeProvider.php * namespace

 * packageApplication
 * summary,This file is part of the ramsey/uuid library * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplatexFor the full copyright and license information, please view the LICENSE
file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * items	copyright &phpDocumentor\Descriptor\TagDescriptor

,Copyright (c) Ben <PERSON> <<EMAIL>> license 

&http://opensource.org/licenses/MIT MIT package 

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 0a79eba6dea4a36ddfbee8edf9d77880 * path=vendor/ramsey/uuid/src/Provider/Node/FallbackNodeProvider.php	 * source@<?php

/**
 * This file is part of the ramsey/uuid library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) Ben Ramsey <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 */

declare(strict_types=1);

namespace Ramsey\Uuid\Provider\Node;

use Ramsey\Uuid\Exception\NodeException;
use Ramsey\Uuid\Provider\NodeProviderInterface;
use Ramsey\Uuid\Type\Hexadecimal;

/**
 * FallbackNodeProvider retrieves the system node ID by stepping through a list of providers until a node ID can be obtained
 */
class FallbackNodeProvider implements NodeProviderInterface
{
    /**
     * @param iterable<NodeProviderInterface> $providers Array of node providers
     */
    public function __construct(private iterable $providers)
    {
    }

    public function getNode(): Hexadecimal
    {
        $lastProviderException = null;

        foreach ($this->providers as $provider) {
            try {
                return $provider->getNode();
            } catch (NodeException $exception) {
                $lastProviderException = $exception;

                continue;
            }
        }

        throw new NodeException(message: 'Unable to find a suitable node provider', previous: $lastProviderException);
    }
}
 * namespaceAliases\Ramsey\Uuid\Provider\NodephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen($ phpDocumentor\Reflection\Fqsen nameNode * includes * constants * functions
 * classes/\Ramsey\Uuid\Provider\Node\FallbackNodeProvider(phpDocumentor\Descriptor\ClassDescriptor)*1+FallbackNodeProvider3(yFallbackNodeProvider retrieves the system node ID by stepping through a list of providers until a node ID can be obtained


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 5617    * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor)*>\Ramsey\Uuid\Provider\Node\FallbackNodeProvider::__construct()+<<( 



param ,phpDocumentor\Descriptor\Tag\ParamDescriptor?

Array of node providers  * type(phpDocumentor\Reflection\Types\Iterable_ * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen)*+\Ramsey\Uuid\Provider\NodeProviderInterface+NodeProviderInterface
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableName	providers  567567Z  	 * parent" * argumentsR+phpDocumentor\Descriptor\ArgumentDescriptor R
 

"2  ";"<  3 phpDocumentor\Descriptor\ArgumentDescriptor method")B"4
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicYZ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference9:
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  getNode=)*:\Ramsey\Uuid\Provider\Node\FallbackNodeProvider::getNode()+ff( 

   56!7a5607<  S"T\]EF)*\Ramsey\Uuid\Type\Hexadecimal+Hexadecimal_9:`ab"Ge  
 * propertiesR+phpDocumentor\Descriptor\PropertyDescriptor)*;\Ramsey\Uuid\Provider\Node\FallbackNodeProvider::$providers+RR1 

 var  5673567M  S"\5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtual`abc'PRIVATEe BCD^ I JKL M N OPW  .S 
 * implementsG)*G+H
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums