1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-3aafe114533b42c228955f7dced8c760
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameInvalidArgumentException.php * namespace

 * packageApplication
 * summary2This file is part of the ramsey/collection library * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplatexFor the full copyright and license information, please view the LICENSE
file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * items	copyright &phpDocumentor\Descriptor\TagDescriptor

,Copyright (c) <PERSON> <<EMAIL>> license 

&http://opensource.org/licenses/MIT MIT package 

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ac5f3b18370a9e49a7232d1e90a4ab75 * pathCvendor/ramsey/collection/src/Exception/InvalidArgumentException.php	 * sourcef<?php

/**
 * This file is part of the ramsey/collection library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) Ben Ramsey <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 */

declare(strict_types=1);

namespace Ramsey\Collection\Exception;

use InvalidArgumentException as PhpInvalidArgumentException;

/**
 * Thrown to indicate an argument is not of the expected type.
 */
class InvalidArgumentException extends PhpInvalidArgumentException implements CollectionException
{
}
 * namespaceAliases\Ramsey\Collection\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen($ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classes5\Ramsey\Collection\Exception\InvalidArgumentException(phpDocumentor\Descriptor\ClassDescriptor)*1+InvalidArgumentException3(;Thrown to indicate an argument is not of the expected type.


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 567    * readOnly * final * abstract
 * methods
 * properties.	 * parent)*\InvalidArgumentException+3
 * implements0\Ramsey\Collection\Exception\CollectionException)*@+CollectionException
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums