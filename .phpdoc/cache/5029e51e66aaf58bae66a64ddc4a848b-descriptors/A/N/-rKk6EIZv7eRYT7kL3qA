1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c53a24f7a53f6578bcd6cff0353384c8
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameSymfonyAnnotation.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4acf5d0016d2a9d9ec3c6bbb0fa28014 * pathPvendor/rector/rector/vendor/rector/rector-symfony/src/Enum/SymfonyAnnotation.php	 * source7<?php

declare (strict_types=1);
namespace Rector\Symfony\Enum;

final class SymfonyAnnotation
{
    /**
     * @var string
     */
    public const ROUTE = 'Symfony\\Component\\Routing\\Annotation\\Route';
    /**
     * @var string
     */
    public const TWIG_TEMPLATE = 'Symfony\\Bridge\\Twig\\Attribute\\Template';
    /**
     * @var string
     */
    public const MAP_ENTITY = 'Symfony\\Bridge\\Doctrine\\Attribute\\MapEntity';
    /**
     * @var string
     */
    public const TEMPLATE = 'Sensio\\Bundle\\FrameworkExtraBundle\\Configuration\\Template';
}
 * namespaceAliases\Rector\Symfony\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes&\Rector\Symfony\Enum\SymfonyAnnotation(phpDocumentor\Descriptor\ClassDescriptor#$+%SymfonyAnnotation-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(ROUTE+phpDocumentor\Descriptor\ConstantDescriptor#$-\Rector\Symfony\Enum\SymfonyAnnotation::ROUTE%66+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor9	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  ./0 ./0   * value,'Symfony\Component\Routing\Annotation\Route'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent";  
TWIG_TEMPLATE7#$5\Rector\Symfony\Enum\SymfonyAnnotation::TWIG_TEMPLATE%GG+ 
	

9 :9	
 ;< =
  ./0 ./0  >('Symfony\Bridge\Twig\Attribute\Template'2@AB"-E F";  
MAP_ENTITY7#$2\Rector\Symfony\Enum\SymfonyAnnotation::MAP_ENTITY%JJ+ 
	

9 :9	
 ;< =
  ./0 ./0  >-'Symfony\Bridge\Doctrine\Attribute\MapEntity'2@AB"-E F";  TEMPLATE7#$0\Rector\Symfony\Enum\SymfonyAnnotation::TEMPLATE%MM+ 
	

9 :9	
 ;< =
  ./0 ./0  >;'Sensio\Bundle\FrameworkExtraBundle\Configuration\Template'2@AB"-E F";  F 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums