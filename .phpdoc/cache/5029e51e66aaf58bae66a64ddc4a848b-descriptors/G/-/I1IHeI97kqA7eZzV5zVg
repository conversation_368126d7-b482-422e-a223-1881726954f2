1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-e2b0351c8fd4f35201bf0963423c32a3
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRepositoryHelper.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 7fe0e399e5dd3b4780c85d84a17db46b * path1vendor/rector/rector/src/Git/RepositoryHelper.php	 * source+<?php

declare (strict_types=1);
namespace Rector\Git;

use RectorPrefix202507\Nette\Utils\Strings;
use RectorPrefix202507\Symfony\Component\Process\Process;
final class RepositoryHelper
{
    /**
     * @var string
     * @see https://regex101.com/r/etcmog/2
     */
    private const GITHUB_REPOSITORY_REGEX = '#github\\.com[:\\/](?<repository_name>.*?)\\.git#';
    public static function resolveGithubRepositoryName(string $currentDirectory) : ?string
    {
        // resolve current repository name
        $process = new Process(['git', 'remote', 'get-url', 'origin'], $currentDirectory, null, null, null);
        $process->run();
        $output = $process->getOutput();
        $match = Strings::match($output, self::GITHUB_REPOSITORY_REGEX);
        return $match['repository_name'] ?? null;
    }
}
 * namespaceAliases\Rector\GitphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameGit * includes * constants * functions
 * classes\Rector\Git\RepositoryHelper(phpDocumentor\Descriptor\ClassDescriptor#$+%RepositoryHelper-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methodsresolveGithubRepositoryName)phpDocumentor\Descriptor\MethodDescriptor#$;\Rector\Git\RepositoryHelper::resolveGithubRepositoryName()%55" 
	 
param  ./0q./0' 	 * parent" * argumentscurrentDirectory+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\String_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicBC	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realType? ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(GITHUB_REPOSITORY_REGEX+phpDocumentor\Descriptor\ConstantDescriptor#$5\Rector\Git\RepositoryHelper::GITHUB_REPOSITORY_REGEX%QQ+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptorT	
 >?  * variableName
see *phpDocumentor\Descriptor\Tag\SeeDescriptorW	
 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference4phpDocumentor\Reflection\DocBlock\Tags\Reference\Url9 phpDocumentor\Reflection\DocBlock\Tags\Reference\Url urihttps://regex101.com/r/etcmog/2  ./0 ./0   * value0'#github\.com[:\/](?<repository_name>.*?)\.git#'2JKLM'PRIVATEO 9">  9 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums