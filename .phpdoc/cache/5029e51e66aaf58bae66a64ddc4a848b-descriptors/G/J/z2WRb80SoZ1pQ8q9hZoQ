1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0870e37cf3cb911cd8d795ac602afb41
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePhpDocAttributeKey.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash c65f6b8cfba446ed05da166216e9bcce * pathUvendor/rector/rector/src/PhpDocParser/PhpDocParser/ValueObject/PhpDocAttributeKey.php	 * source<?php

declare (strict_types=1);
namespace Rector\PhpDocParser\PhpDocParser\ValueObject;

final class PhpDocAttributeKey
{
    /**
     * @var string
     */
    public const PARENT = 'parent';
    /**
     * @var string
     */
    public const ORIG_NODE = 'orig_node';
}
 * namespaceAliases-\Rector\PhpDocParser\PhpDocParser\ValueObjectphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameValueObject * includes * constants * functions
 * classes@\Rector\PhpDocParser\PhpDocParser\ValueObject\PhpDocAttributeKey(phpDocumentor\Descriptor\ClassDescriptor#$+%PhpDocAttributeKey-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(PARENT+phpDocumentor\Descriptor\ConstantDescriptor#$H\Rector\PhpDocParser\PhpDocParser\ValueObject\PhpDocAttributeKey::PARENT%66+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor9	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  ./0 ./0   * value'parent'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent";  	ORIG_NODE7#$K\Rector\PhpDocParser\PhpDocParser\ValueObject\PhpDocAttributeKey::ORIG_NODE%GG+ 
	

9 :9	
 ;< =
  ./0 ./0  >'orig_node'2@AB"-E F";  F 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums