1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-3880730c1eb129e3a13d7bcba974a2ad
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameAdapterInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ed61e4d20ab05cc156d8ef9b850b2915 * pathCvendor/vlucas/phpdotenv/src/Repository/Adapter/AdapterInterface.php	 * sourceZ<?php

declare(strict_types=1);

namespace Dotenv\Repository\Adapter;

interface AdapterInterface extends ReaderInterface, WriterInterface
{
    /**
     * Create a new instance of the adapter, if it is available.
     *
     * @return \PhpOption\Option<\Dotenv\Repository\Adapter\AdapterInterface>
     */
    public static function create();
}
 * namespaceAliases\Dotenv\Repository\AdapterphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameAdapter * includes * constants * functions
 * classes
 * interfaces+\Dotenv\Repository\Adapter\AdapterInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%AdapterInterface."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents*\Dotenv\Repository\Adapter\ReaderInterface#$3%ReaderInterface*\Dotenv\Repository\Adapter\WriterInterface#$5%WriterInterface(
 * methodscreate)phpDocumentor\Descriptor\MethodDescriptor#$5\Dotenv\Repository\Adapter\AdapterInterface::create()%88" 9Create a new instance of the adapter, if it is available.	

return -phpDocumentor\Descriptor\Tag\ReturnDescriptor<	
  * type)phpDocumentor\Reflection\Types\Collection * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$,%.
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|0 phpDocumentor\Reflection\Types\Collection fqsen#$\PhpOption\Option%Option  /017/01V 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums