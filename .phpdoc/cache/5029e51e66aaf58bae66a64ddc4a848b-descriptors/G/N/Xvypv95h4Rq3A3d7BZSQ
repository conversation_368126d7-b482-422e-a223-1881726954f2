1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-99a3d7b11e02b7e618e94271b3b8a193
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRunProcessFailedException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 951941d7498763ed5d614ce4686424b4 * pathSvendor/rector/rector/vendor/symfony/process/Exception/RunProcessFailedException.php	 * source<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace RectorPrefix202507\Symfony\Component\Process\Exception;

use RectorPrefix202507\Symfony\Component\Process\Messenger\RunProcessContext;
/**
 * <AUTHOR> Bond <<EMAIL>>
 */
final class RunProcessFailedException extends RuntimeException
{
    /**
     * @readonly
     */
    public RunProcessContext $context;
    public function __construct(ProcessFailedException $exception, RunProcessContext $context)
    {
        $this->context = $context;
        parent::__construct($exception->getMessage(), $exception->getCode());
    }
}
 * namespaceAliases7\RectorPrefix202507\Symfony\Component\Process\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classesQ\RectorPrefix202507\Symfony\Component\Process\Exception\RunProcessFailedException(phpDocumentor\Descriptor\ClassDescriptor#$+%RunProcessFailedException-"
	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor.	 Kevin Bond <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 123   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$`\RectorPrefix202507\Symfony\Component\Process\Exception\RunProcessFailedException::__construct()%88" 
	 
param  123?123 	 * parent" * arguments	exception+phpDocumentor\Descriptor\ArgumentDescriptor >
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$N\RectorPrefix202507\Symfony\Component\Process\Exception\ProcessFailedException%ProcessFailedException
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicHIcontext? K
 
	 
  "'"( @""ABC#$I\RectorPrefix202507\Symfony\Component\Process\Messenger\RunProcessContext%RunProcessContextF GHIJHI	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference56
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * propertiesK+phpDocumentor\Descriptor\PropertyDescriptor#$[\RectorPrefix202507\Symfony\Component\Process\Exception\RunProcessFailedException::$context%KK+ 
	

readonly [	
 var  123 123  <"N5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualRST"<W ABC#$L%MF  (<#$H\RectorPrefix202507\Symfony\Component\Process\Exception\RuntimeException%RuntimeException
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums