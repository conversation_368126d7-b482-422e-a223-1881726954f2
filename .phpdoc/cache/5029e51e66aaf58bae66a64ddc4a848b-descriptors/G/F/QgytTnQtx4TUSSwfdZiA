1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-84eced085f25b676a6a05a394de5ef3a
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameConditionalTypeNode.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ad8d73af475a364dc28a32fec9464584 * pathAvendor/phpstan/phpdoc-parser/src/Ast/Type/ConditionalTypeNode.php	 * source<<?php declare(strict_types = 1);

namespace PHPStan\PhpDocParser\Ast\Type;

use PHPStan\PhpDocParser\Ast\NodeAttributes;
use function sprintf;

class ConditionalTypeNode implements TypeNode
{

	use NodeAttributes;

	public TypeNode $subjectType;

	public TypeNode $targetType;

	public TypeNode $if;

	public TypeNode $else;

	public bool $negated;

	public function __construct(TypeNode $subjectType, TypeNode $targetType, TypeNode $if, TypeNode $else, bool $negated)
	{
		$this->subjectType = $subjectType;
		$this->targetType = $targetType;
		$this->if = $if;
		$this->else = $else;
		$this->negated = $negated;
	}

	public function __toString(): string
	{
		return sprintf(
			'(%s %s %s ? %s : %s)',
			$this->subjectType,
			$this->negated ? 'is not' : 'is',
			$this->targetType,
			$this->if,
			$this->else,
		);
	}

}
 * namespaceAliases\PHPStan\PhpDocParser\Ast\TypephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameType * includes * constants * functions
 * classes2\PHPStan\PhpDocParser\Ast\Type\ConditionalTypeNode(phpDocumentor\Descriptor\ClassDescriptor#$+%ConditionalTypeNode-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./,0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$A\PHPStan\PhpDocParser\Ast\Type\ConditionalTypeNode::__construct()%55" 
	 
param  ./0_./0h 	 * parent" * argumentssubjectType+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$'\PHPStan\PhpDocParser\Ast\Type\TypeNode%TypeNode
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEF
targetType< H
 
	 
  " "! =">?@#$A%BC DEFGEFif< I
 
	 
  " "! =">?@#$A%BC DEFGEFelse< J
 
	 
  " "! =">?@#$A%BC DEFGEFnegated< K
 
	 
  " "! =">&phpDocumentor\Reflection\Types\Boolean C DEFGEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
__toString6#$@\PHPStan\PhpDocParser\Ast\Type\ConditionalTypeNode::__toString()%WW" 
	 
  ./ 0l./*07 9":MN&phpDocumentor\Reflection\Types\String_ P23QRS"IV  
 * properties;+phpDocumentor\Descriptor\PropertyDescriptor#$@\PHPStan\PhpDocParser\Ast\Type\ConditionalTypeNode::$subjectType%;;+ 
	 
var  ./
0 ./
0  9"M5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualQRS"IV >?@#$A%BC  H[#$?\PHPStan\PhpDocParser\Ast\Type\ConditionalTypeNode::$targetType%HH+ 
	 
]  ./0 ./0  9"M^_`aQRS"IV >?@#$A%BC  I[#$7\PHPStan\PhpDocParser\Ast\Type\ConditionalTypeNode::$if%II+ 
	 
]  ./0 ./0  9"M^_`aQRS"IV >?@#$A%BC  J[#$9\PHPStan\PhpDocParser\Ast\Type\ConditionalTypeNode::$else%JJ+ 
	 
]  ./0 ./0  9"M^_`aQRS"IV >?@#$A%BC  K[#$<\PHPStan\PhpDocParser\Ast\Type\ConditionalTypeNode::$negated%KK+ 
	 
]  ./0 ./0  9"M^_`aQRS"IV >L C  (9 
 * implementsA#$A%B
 * usedTraits(\PHPStan\PhpDocParser\Ast\NodeAttributes#$h%NodeAttributes 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums