1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-ae36ff6f53696024903c0d455cf1f92d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name	Null_.php * namespace

 * packageApplication
 * summary#This file is part of phpDocumentor. * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplatexFor the full copyright and license information, please view the LICENSE
file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * itemslink +phpDocumentor\Descriptor\Tag\LinkDescriptor


  * linkhttp://phpdoc.orgpackage &phpDocumentor\Descriptor\TagDescriptor

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ec163f94d446e003a860bfc1a05805cd * path6vendor/phpdocumentor/type-resolver/src/Types/Null_.php	 * sourcei<?php

declare(strict_types=1);

/**
 * This file is part of phpDocumentor.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @link      http://phpdoc.org
 */

namespace phpDocumentor\Reflection\Types;

use phpDocumentor\Reflection\Type;

/**
 * Value Object representing a null value or type.
 *
 * @psalm-immutable
 */
final class Null_ implements Type
{
    /**
     * Returns a rendered output of the Type as it would be used in a DocBlock.
     */
    public function __toString(): string
    {
        return 'null';
    }
}
 * namespaceAliases\phpDocumentor\Reflection\TypesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen($ phpDocumentor\Reflection\Fqsen nameTypes * includes * constants * functions
 * classes%\phpDocumentor\Reflection\Types\Null_(phpDocumentor\Descriptor\ClassDescriptor)*1+Null_3(/Value Object representing a null value or type.


psalm-immutable 5


 ""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 67 8    * readOnly * final * abstract
 * methods
__toString)phpDocumentor\Descriptor\MethodDescriptor)*3\phpDocumentor\Reflection\Types\Null_::__toString()+==( HReturns a rendered output of the Type as it would be used in a DocBlock.


  678678e  	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\String_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference:;
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties.A 
 * implements\phpDocumentor\Reflection\Type)*O+Type
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums