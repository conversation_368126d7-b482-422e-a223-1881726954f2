1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-dc6598348a01618aa8bfc638eb8b8792
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameInvalidArgumentException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 2ce0453cb021d474b8b68031d4639a25 * pathAvendor/symfony/translation/Exception/InvalidArgumentException.php	 * source	<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Translation\Exception;

/**
 * Base InvalidArgumentException for the Translation component.
 *
 * <AUTHOR> Ait boudad <<EMAIL>>
 */
class InvalidArgumentException extends \InvalidArgumentException implements ExceptionInterface
{
}
 * namespaceAliases(\Symfony\Component\Translation\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classesA\Symfony\Component\Translation\Exception\InvalidArgumentException(phpDocumentor\Descriptor\ClassDescriptor#$+%InvalidArgumentException-"<Base InvalidArgumentException for the Translation component.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	-Abdellatif Ait boudad <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 234   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$\InvalidArgumentException%-
 * implements;\Symfony\Component\Translation\Exception\ExceptionInterface#$=%ExceptionInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums