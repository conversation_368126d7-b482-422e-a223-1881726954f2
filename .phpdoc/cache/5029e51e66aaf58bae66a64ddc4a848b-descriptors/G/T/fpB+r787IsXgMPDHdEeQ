1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-24fd06b5e68f47ca505c5bec1337ac39
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameCacheMissed.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4199ae6a2e52aaa4959a2ede80017213 * path.vendor/illuminate/cache/Events/CacheMissed.php	 * source[<?php

namespace Illuminate\Cache\Events;

class CacheMissed extends CacheEvent
{
    //
}
 * namespaceAliases\Illuminate\Cache\EventsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEvents * includes * constants * functions
 * classes$\Illuminate\Cache\Events\CacheMissed(phpDocumentor\Descriptor\ClassDescriptor#$+%CacheMissed-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$#\Illuminate\Cache\Events\CacheEvent%
CacheEvent
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums