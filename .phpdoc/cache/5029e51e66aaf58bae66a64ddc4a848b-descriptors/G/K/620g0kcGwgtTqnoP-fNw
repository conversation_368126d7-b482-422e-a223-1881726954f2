1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-95dc5859d8cb9004871bf3e70acb52b5
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRunProcessMessageHandler.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash f8787bdaaaf48158cca4760eb741bf93 * pathRvendor/rector/rector/vendor/symfony/process/Messenger/RunProcessMessageHandler.php	 * source5<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace RectorPrefix202507\Symfony\Component\Process\Messenger;

use RectorPrefix202507\Symfony\Component\Process\Exception\ProcessFailedException;
use RectorPrefix202507\Symfony\Component\Process\Exception\RunProcessFailedException;
use RectorPrefix202507\Symfony\Component\Process\Process;
/**
 * <AUTHOR> Bond <<EMAIL>>
 */
final class RunProcessMessageHandler
{
    public function __invoke(RunProcessMessage $message) : RunProcessContext
    {
        $process = new Process($message->command, $message->cwd, $message->env, $message->input, $message->timeout);
        try {
            return new RunProcessContext($message, $process->mustRun());
        } catch (ProcessFailedException $e) {
            throw new RunProcessFailedException($e, new RunProcessContext($message, $e->getProcess()));
        }
    }
}
 * namespaceAliases7\RectorPrefix202507\Symfony\Component\Process\MessengerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Messenger * includes * constants * functions
 * classesP\RectorPrefix202507\Symfony\Component\Process\Messenger\RunProcessMessageHandler(phpDocumentor\Descriptor\ClassDescriptor#$+%RunProcessMessageHandler-"
	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor.	 Kevin Bond <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 123   * readOnly * final * abstract
 * methods__invoke)phpDocumentor\Descriptor\MethodDescriptor#$\\RectorPrefix202507\Symfony\Component\Process\Messenger\RunProcessMessageHandler::__invoke()%88" 
	 
param  123r1231 	 * parent" * argumentsmessage+phpDocumentor\Descriptor\ArgumentDescriptor >
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$I\RectorPrefix202507\Symfony\Component\Process\Messenger\RunProcessMessage%RunProcessMessage
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicHI	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnTypeBC#$I\RectorPrefix202507\Symfony\Component\Process\Messenger\RunProcessContext%RunProcessContext? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference56
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(< 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums