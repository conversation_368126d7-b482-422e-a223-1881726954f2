1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-40f373a39638d616c27fbea85441b874
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameNumberConstraint.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 5c635473df550e3674f9015f9f5ff3ce * pathPvendor/justinrainbow/json-schema/src/JsonSchema/Constraints/NumberConstraint.php	 * sourceI<?php

declare(strict_types=1);

/*
 * This file is part of the JsonSchema package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace JsonSchema\Constraints;

use JsonSchema\ConstraintError;
use JsonSchema\Entity\JsonPointer;

/**
 * The NumberConstraint Constraints, validates an number against a given schema
 *
 * <AUTHOR> Schönthal <<EMAIL>>
 * <AUTHOR> Prieto Reis <<EMAIL>>
 */
class NumberConstraint extends Constraint
{
    /**
     * {@inheritdoc}
     */
    public function check(&$element, $schema = null, ?JsonPointer $path = null, $i = null): void
    {
        // Verify minimum
        if (isset($schema->exclusiveMinimum)) {
            if (isset($schema->minimum)) {
                if ($schema->exclusiveMinimum && $element <= $schema->minimum) {
                    $this->addError(ConstraintError::EXCLUSIVE_MINIMUM(), $path, ['minimum' => $schema->minimum]);
                } elseif ($element < $schema->minimum) {
                    $this->addError(ConstraintError::MINIMUM(), $path, ['minimum' => $schema->minimum]);
                }
            } else {
                $this->addError(ConstraintError::MISSING_MINIMUM(), $path);
            }
        } elseif (isset($schema->minimum) && $element < $schema->minimum) {
            $this->addError(ConstraintError::MINIMUM(), $path, ['minimum' => $schema->minimum]);
        }

        // Verify maximum
        if (isset($schema->exclusiveMaximum)) {
            if (isset($schema->maximum)) {
                if ($schema->exclusiveMaximum && $element >= $schema->maximum) {
                    $this->addError(ConstraintError::EXCLUSIVE_MAXIMUM(), $path, ['maximum' => $schema->maximum]);
                } elseif ($element > $schema->maximum) {
                    $this->addError(ConstraintError::MAXIMUM(), $path, ['maximum' => $schema->maximum]);
                }
            } else {
                $this->addError(ConstraintError::MISSING_MAXIMUM(), $path);
            }
        } elseif (isset($schema->maximum) && $element > $schema->maximum) {
            $this->addError(ConstraintError::MAXIMUM(), $path, ['maximum' => $schema->maximum]);
        }

        // Verify divisibleBy - Draft v3
        if (isset($schema->divisibleBy) && $this->fmod($element, $schema->divisibleBy) != 0) {
            $this->addError(ConstraintError::DIVISIBLE_BY(), $path, ['divisibleBy' => $schema->divisibleBy]);
        }

        // Verify multipleOf - Draft v4
        if (isset($schema->multipleOf) && $this->fmod($element, $schema->multipleOf) != 0) {
            $this->addError(ConstraintError::MULTIPLE_OF(), $path, ['multipleOf' => $schema->multipleOf]);
        }

        $this->checkFormat($element, $schema, $path, $i);
    }

    private function fmod($number1, $number2)
    {
        $modulus = ($number1 - round($number1 / $number2) * $number2);
        $precision = 0.0000000001;

        if (-$precision < $modulus && $modulus < $precision) {
            return 0.0;
        }

        return $modulus;
    }
}
 * namespaceAliases\JsonSchema\ConstraintsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameConstraints * includes * constants * functions
 * classes(\JsonSchema\Constraints\NumberConstraint(phpDocumentor\Descriptor\ClassDescriptor#$+%NumberConstraint-"LThe NumberConstraint Constraints, validates an number against a given schema	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	+Robert Schönthal <<EMAIL>> 0/	*Bruno Prieto Reis <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 34T5   * readOnly * final * abstract
 * methodscheck)phpDocumentor\Descriptor\MethodDescriptor#$1\JsonSchema\Constraints\NumberConstraint::check()%::" 
{@inheritdoc}	

param  345_34G5$ 	 * parent" * argumentselement+phpDocumentor\Descriptor\ArgumentDescriptor A
 
	 
  ","- 3 phpDocumentor\Descriptor\ArgumentDescriptor method"% * type%phpDocumentor\Reflection\Types\Mixed_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicHIschemaB K
 
	 
  ","- C"%DE FnullGHIJHIpathB M
 
	 
  ","- C"%D'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\JsonSchema\Entity\JsonPointer%JsonPointerFLGHIJHIiB T
 
	 
  ","- C"%DE FLGHIJHI	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference78
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  fmod;#$0\JsonSchema\Constraints\NumberConstraint::fmod()%__" 
	 
>  34I5+34S5E ?"@number1B a
 
	 
  "S"T C"NDE F GHIJHInumber2B b
 
	 
  "S"T C"NDE F GHIJHIUVE X78YZ[\'PRIVATE^  
 * properties(?#$"\JsonSchema\Constraints\Constraint%
Constraint
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums