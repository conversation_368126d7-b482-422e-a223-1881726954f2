1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-4aabe048e16e735894248d595256d2e1
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameDatabaseBusy.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 329e221bd413b2eb816c5ba64d60990b * path2vendor/illuminate/database/Events/DatabaseBusy.php	 * sourcet<?php

namespace Illuminate\Database\Events;

class DatabaseBusy
{
    /**
     * Create a new event instance.
     *
     * @param  string  $connectionName  The database connection name.
     * @param  int  $connections  The number of open connections.
     */
    public function __construct(
        public $connectionName,
        public $connections,
    ) {
    }
}
 * namespaceAliases\Illuminate\Database\EventsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEvents * includes * constants * functions
 * classes(\Illuminate\Database\Events\DatabaseBusy(phpDocumentor\Descriptor\ClassDescriptor#$+%DatabaseBusy-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$7\Illuminate\Database\Events\DatabaseBusy::__construct()%55" Create a new event instance.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor9	The database connection name.  * type&phpDocumentor\Reflection\Types\String_  * variableNameconnectionName:9	The number of open connections. <&phpDocumentor\Reflection\Types\Integer >connections  ./
0
./0p 	 * parent" * arguments?+phpDocumentor\Descriptor\ArgumentDescriptor ?
 
	"$
  "+", 3 phpDocumentor\Descriptor\ArgumentDescriptor method"<"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicIJBE B
 
	"(
  "+", F"<"*G HIJKIJ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties?+phpDocumentor\Descriptor\PropertyDescriptor#$9\Illuminate\Database\Events\DatabaseBusy::$connectionName%??+ 
	 
var  ./0/./0D C"L5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualPQR"<U < G  BW#$6\Illuminate\Database\Events\DatabaseBusy::$connections%BB+ 
	 
Y  ./0O./0a C"LZ[\]PQR"<U < G  (C 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums