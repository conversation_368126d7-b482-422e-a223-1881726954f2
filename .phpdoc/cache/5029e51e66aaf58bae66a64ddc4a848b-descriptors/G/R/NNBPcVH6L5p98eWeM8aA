1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-63159b2585305804454167a12a82d6c4
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameView.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4143ec5d64c81f75c3b4d921c1a1a533 * path(vendor/pestphp/pest/src/Support/View.php	 * source}<?php

declare(strict_types=1);

namespace Pest\Support;

use Symfony\Component\Console\Output\OutputInterface;
use Termwind\Termwind;

use function Termwind\render;
use function Termwind\renderUsing;

/**
 * @internal
 */
final class View
{
    /**
     * The implementation of the output.
     */
    private static OutputInterface $output;

    /**
     * Renders views using the given Output instance.
     */
    public static function renderUsing(OutputInterface $output): void
    {
        self::$output = $output;
    }

    /**
     * Renders the given view.
     *
     * @param  array<string, mixed>  $data
     */
    public static function render(string $path, array $data = []): void
    {
        $contents = self::compile($path, $data);

        $existing = Termwind::getRenderer();

        renderUsing(self::$output);

        try {
            render($contents);
        } finally {
            renderUsing($existing);
        }
    }

    /**
     * Compiles the given view.
     *
     * @param  array<string, mixed>  $data
     */
    private static function compile(string $path, array $data): string
    {
        extract($data);

        ob_start();

        $path = str_replace('.', '/', $path);

        include sprintf('%s/../../resources/views/%s.php', __DIR__, $path);

        $contents = ob_get_contents();

        ob_clean();

        return (string) $contents;
    }
}
 * namespaceAliases
\Pest\SupportphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSupport * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums