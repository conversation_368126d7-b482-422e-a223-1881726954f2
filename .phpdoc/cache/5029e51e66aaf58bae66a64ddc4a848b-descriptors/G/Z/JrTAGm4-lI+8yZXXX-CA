1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0fe66b396f0e575110fc6453dad1f45c
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameCacheKey.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4ea6eae7fd4f8dcf5fdec80d40802eb0 * path2vendor/rector/rector/src/Caching/Enum/CacheKey.php	 * sourcer<?php

declare (strict_types=1);
namespace Rector\Caching\Enum;

/**
 * @enum
 */
final class CacheKey
{
    /**
     * @var string
     */
    public const CONFIGURATION_HASH_KEY = 'configuration_hash';
    /**
     * @var string
     */
    public const FILE_HASH_KEY = 'file_hash';
    /**
     * @var string
     */
    public const KAIZEN_RULES = 'kaizen_rules';
}
 * namespaceAliases\Rector\Caching\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes\Rector\Caching\Enum\CacheKey(phpDocumentor\Descriptor\ClassDescriptor#$+%CacheKey-"
	

enum .	
 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber	/ phpDocumentor\Reflection\Location columnNumber /01   * readOnly * final * abstract
 * methods
 * properties(CONFIGURATION_HASH_KEY+phpDocumentor\Descriptor\ConstantDescriptor#$5\Rector\Caching\Enum\CacheKey::CONFIGURATION_HASH_KEY%77+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor:	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  /01 /01   * value'configuration_hash'3
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent"<  
FILE_HASH_KEY8#$,\Rector\Caching\Enum\CacheKey::FILE_HASH_KEY%HH+ 
	

: ;:	
 <= >
  /01 /01  ?'file_hash'3ABC"4F G"<  KAIZEN_RULES8#$+\Rector\Caching\Enum\CacheKey::KAIZEN_RULES%KK+ 
	

: ;:	
 <= >
  /01 /01  ?'kaizen_rules'3ABC"4F G"<  G 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums