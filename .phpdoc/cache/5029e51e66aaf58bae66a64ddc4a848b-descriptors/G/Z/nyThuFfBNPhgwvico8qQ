1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-cd74890c2e4a8b69bbcfd6bc21aa6bdb
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameCacheStorageInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 9f28aeca5a4d0182dc8cfd432d4a2865 * pathWvendor/rector/rector/src/Caching/Contract/ValueObject/Storage/CacheStorageInterface.php	 * source><?php

declare (strict_types=1);
namespace Rector\Caching\Contract\ValueObject\Storage;

/**
 * inspired by https://github.com/phpstan/phpstan-src/blob/560652088406d7461c2c4ad4897784e33f8ab312/src/Cache/CacheStorage.php
 * @internal
 */
interface CacheStorageInterface
{
    /**
     * @return mixed|null
     */
    public function load(string $key, string $variableKey);
    /**
     * @param mixed $data
     */
    public function save(string $key, string $variableKey, $data) : void;
    public function clean(string $key) : void;
    public function clear() : void;
}
 * namespaceAliases,\Rector\Caching\Contract\ValueObject\StoragephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameStorage * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums