1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-baef36efff0a8dbd3b05c4982dfbf88b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameShouldBeUnique.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4dbc5e2829dfca416edfe5914b4f7a84 * pathIvendor/rector/rector/vendor/illuminate/contracts/Queue/ShouldBeUnique.php	 * sourcee<?php

namespace RectorPrefix202507\Illuminate\Contracts\Queue;

interface ShouldBeUnique
{
    //
}
 * namespaceAliases.\RectorPrefix202507\Illuminate\Contracts\QueuephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameQueue * includes * constants * functions
 * classes
 * interfaces=\RectorPrefix202507\Illuminate\Contracts\Queue\ShouldBeUnique,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ShouldBeUnique."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums