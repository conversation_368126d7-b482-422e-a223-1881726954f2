1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c9f9b1078fe8020a8ebd24960c4a9e20
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameInflectorInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 9ffeb5f363bf54b78039ef865aa546f2 * path6vendor/symfony/string/Inflector/InflectorInterface.php	 * sourceC<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\String\Inflector;

interface InflectorInterface
{
    /**
     * Returns the singular forms of a string.
     *
     * If the method can't determine the form with certainty, several possible singulars are returned.
     *
     * @return string[]
     */
    public function singularize(string $plural): array;

    /**
     * Returns the plural forms of a string.
     *
     * If the method can't determine the form with certainty, several possible plurals are returned.
     *
     * @return string[]
     */
    public function pluralize(string $singular): array;
}
 * namespaceAliases#\Symfony\Component\String\InflectorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Inflector * includes * constants * functions
 * classes
 * interfaces6\Symfony\Component\String\Inflector\InflectorInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%InflectorInterface."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /0!1  
 * parents(
 * methodssingularize)phpDocumentor\Descriptor\MethodDescriptor#$E\Symfony\Component\String\Inflector\InflectorInterface::singularize()%44" 'Returns the singular forms of a string.	_If the method can't determine the form with certainty, several possible singulars are returned.
return -phpDocumentor\Descriptor\Tag\ReturnDescriptor9	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType&phpDocumentor\Reflection\Types\String_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types > &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|param  /01/01> 	 * parent" * argumentsplural+phpDocumentor\Descriptor\ArgumentDescriptor I
 
	 
  "0"1 3 phpDocumentor\Descriptor\ArgumentDescriptor method";> 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicNO	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType<=%phpDocumentor\Reflection\Types\Mixed_ ? @AB > C DE? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  	pluralize5#$C\Symfony\Component\String\Inflector\InflectorInterface::pluralize()%]]" %Returns the plural forms of a string.	]If the method can't determine the form with certainty, several possible plurals are returned.
9 :9	
 ;<=> ? @AB > C DEF  /0 1
/0 1? G"HsingularJ a
 
	 
  "U"V K"C;> L MNOPNOQR<=S ? @AB > C DETUVWXY"B\   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums