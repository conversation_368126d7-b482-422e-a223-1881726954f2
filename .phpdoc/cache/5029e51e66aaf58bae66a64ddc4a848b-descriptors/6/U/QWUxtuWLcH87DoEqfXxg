1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-e2b56a2681c91f2da772f9affb7ebfbc
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
symfony42.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 70296f39c99857a77037410476e36795 * path\vendor/rector/rector/vendor/rector/rector-symfony/config/sets/symfony/symfony4/symfony42.php	 * source\<?php

declare (strict_types=1);
namespace RectorPrefix202507;

use Rector\Config\RectorConfig;
# https://github.com/symfony/symfony/pull/28447
return static function (RectorConfig $rectorConfig) : void {
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-http-foundation.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-http-kernel.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-framework-bundle.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-translation.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-process.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-config.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-dom-crawler.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-finder.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-monolog-bridge.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-serializer.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-form.php');
    $rectorConfig->import(__DIR__ . '/symfony42/symfony42-cache.php');
};
 * namespaceAliases\RectorPrefix202507phpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRectorPrefix202507 * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums