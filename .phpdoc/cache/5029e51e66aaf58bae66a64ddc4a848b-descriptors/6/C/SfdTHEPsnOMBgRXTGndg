1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-177e82aacdf8826c38dc91111a38b841
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameShouldBeUnique.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 4748ba1a0c68cf0a4763327f51035868 * path;vendor/illuminate/contracts/Broadcasting/ShouldBeUnique.php	 * sourceY<?php

namespace Illuminate\Contracts\Broadcasting;

interface ShouldBeUnique
{
    //
}
 * namespaceAliases"\Illuminate\Contracts\BroadcastingphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameBroadcasting * includes * constants * functions
 * classes
 * interfaces1\Illuminate\Contracts\Broadcasting\ShouldBeUnique,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ShouldBeUnique."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums