1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-d00b5ef3b50470357eba81420dc7e804
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namesymfony62-http-kernel.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash e563eacef9dc6a313c8adcbb6f0c3cab * pathrvendor/rector/rector/vendor/rector/rector-symfony/config/sets/symfony/symfony6/symfony62/symfony62-http-kernel.php	 * source&<?php

declare (strict_types=1);
namespace RectorPrefix202507;

use Rector\Config\RectorConfig;
use Rector\Renaming\Rector\Name\RenameClassRector;
use Rector\Symfony\Symfony62\Rector\ClassMethod\ClassMethod\ArgumentValueResolverToValueResolverRector;
return static function (RectorConfig $rectorConfig) : void {
    $rectorConfig->rules([
        // @see https://github.com/symfony/symfony/pull/47363
        ArgumentValueResolverToValueResolverRector::class,
    ]);
    // https://symfony.com/blog/new-in-symfony-6-2-built-in-cache-security-template-and-doctrine-attributes
    $rectorConfig->ruleWithConfiguration(RenameClassRector::class, [
        // @see https://github.com/symfony/symfony/pull/46880
        'Sensio\\Bundle\\FrameworkExtraBundle\\Configuration\\Cache' => 'Symfony\\Component\\HttpKernel\\Attribute\\Cache',
        // @see https://github.com/symfony/symfony/pull/47363
        'Symfony\\Component\\HttpKernel\\Controller\\ArgumentValueResolverInterface' => 'Symfony\\Component\\HttpKernel\\Controller\\ValueResolverInterface',
    ]);
};
 * namespaceAliases\RectorPrefix202507phpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRectorPrefix202507 * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums