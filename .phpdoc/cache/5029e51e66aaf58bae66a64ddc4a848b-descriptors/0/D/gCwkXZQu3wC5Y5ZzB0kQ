1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-1e93500c7e4bd1fd0ee8a8b5cb6b0ccf
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRunCommandContext.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 3ceb24ed9f7ef6619dc2a959fbbe89bf * pathKvendor/rector/rector/vendor/symfony/console/Messenger/RunCommandContext.php	 * source<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace RectorPrefix202507\Symfony\Component\Console\Messenger;

/**
 * <AUTHOR> Bond <<EMAIL>>
 */
final class RunCommandContext
{
    /**
     * @readonly
     */
    public RunCommandMessage $message;
    /**
     * @readonly
     */
    public int $exitCode;
    /**
     * @readonly
     */
    public string $output;
    public function __construct(RunCommandMessage $message, int $exitCode, string $output)
    {
        $this->message = $message;
        $this->exitCode = $exitCode;
        $this->output = $output;
    }
}
 * namespaceAliases7\RectorPrefix202507\Symfony\Component\Console\MessengerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Messenger * includes * constants * functions
 * classesI\RectorPrefix202507\Symfony\Component\Console\Messenger\RunCommandContext(phpDocumentor\Descriptor\ClassDescriptor#$+%RunCommandContext-"
	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor.	 Kevin Bond <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 12$3   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$X\RectorPrefix202507\Symfony\Component\Console\Messenger\RunCommandContext::__construct()%88" 
	 
param  123G12#3 	 * parent" * argumentsmessage+phpDocumentor\Descriptor\ArgumentDescriptor >
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$I\RectorPrefix202507\Symfony\Component\Console\Messenger\RunCommandMessage%RunCommandMessage
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicHIexitCode? K
 
	 
  "'"( @""A&phpDocumentor\Reflection\Types\Integer F GHIJHIoutput? M
 
	 
  "'"( @""A&phpDocumentor\Reflection\Types\String_ F GHIJHI	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference56
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties>+phpDocumentor\Descriptor\PropertyDescriptor#$S\RectorPrefix202507\Symfony\Component\Console\Messenger\RunCommandContext::$message%>>+ 
	

readonly \	
 var  123 123  <"O5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualSTU"AX ABC#$D%EF  KZ#$T\RectorPrefix202507\Symfony\Component\Console\Messenger\RunCommandContext::$exitCode%KK+ 
	

\ \	
 ]  123 123  <"O^_`aSTU"AX AL F  MZ#$R\RectorPrefix202507\Symfony\Component\Console\Messenger\RunCommandContext::$output%MM+ 
	

\ \	
 ]  123 123  <"O^_`aSTU"AX AN F  (< 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums