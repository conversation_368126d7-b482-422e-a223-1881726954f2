1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-339502a65a07630fdf71e341bfab521f
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name!FragmentUriGeneratorInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 97014b90b2bf79a7475d08e7f6c36362 * pathEvendor/symfony/http-kernel/Fragment/FragmentUriGeneratorInterface.php	 * source<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\Fragment;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ControllerReference;

/**
 * Interface implemented by rendering strategies able to generate a URL for a fragment.
 *
 * <AUTHOR> Dunglas <<EMAIL>>
 */
interface FragmentUriGeneratorInterface
{
    /**
     * Generates a fragment URI for a given controller.
     *
     * @param bool $absolute Whether to generate an absolute URL or not
     * @param bool $strict   Whether to allow non-scalar attributes or not
     * @param bool $sign     Whether to sign the URL or not
     */
    public function generate(ControllerReference $controller, ?Request $request = null, bool $absolute = false, bool $strict = true, bool $sign = true): string;
}
 * namespaceAliases&\Symfony\Component\HttpKernel\FragmentphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameFragment * includes * constants * functions
 * classes
 * interfacesD\Symfony\Component\HttpKernel\Fragment\FragmentUriGeneratorInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%FragmentUriGeneratorInterface."
TInterface implemented by rendering strategies able to generate a URL for a fragment.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor0	!Kévin Dunglas <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 34 5  
 * parents(
 * methodsgenerate)phpDocumentor\Descriptor\MethodDescriptor#$P\Symfony\Component\HttpKernel\Fragment\FragmentUriGeneratorInterface::generate()%88" 0Generates a fragment URI for a given controller.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor<	*Whether to generate an absolute URL or not  * type&phpDocumentor\Reflection\Types\Boolean  * variableNameabsolute=<	-Whether to allow non-scalar attributes or not ?@ Astrict=<	Whether to sign the URL or not ?@ Asign  345l345 	 * parent" * arguments
controller+phpDocumentor\Descriptor\ArgumentDescriptor I
 
	 
  "9": 3 phpDocumentor\Descriptor\ArgumentDescriptor method"%?&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$<\Symfony\Component\HttpKernel\Controller\ControllerReference%ControllerReference
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicRSrequestJ U
 
	 
  "9": K"%?'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realTypeLM#$)\Symfony\Component\HttpFoundation\Request%RequestPnullQRSTRSBJ B
 
	".
  "9": K"%?"0PfalseQRSTRSDJ D
 
	"2
  "9": K"%?"4PtrueQRSTRSFJ F
 
	"6
  "9": K"%?"8P\QRSTRS	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\String_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums