1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-645cc2b3d94273ab978113ec87c5d3ae
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameConfigurableRuleInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 87a4d2deb9651acc39da066481cf1394 * pathlvendor/rector/rector/vendor/symplify/rule-doc-generator-contracts/src/Contract/ConfigurableRuleInterface.php	 * sourcex<?php

declare (strict_types=1);
namespace Symplify\RuleDocGenerator\Contract;

interface ConfigurableRuleInterface
{
}
 * namespaceAliases#\Symplify\RuleDocGenerator\ContractphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameContract * includes * constants * functions
 * classes
 * interfaces=\Symplify\RuleDocGenerator\Contract\ConfigurableRuleInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ConfigurableRuleInterface."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums