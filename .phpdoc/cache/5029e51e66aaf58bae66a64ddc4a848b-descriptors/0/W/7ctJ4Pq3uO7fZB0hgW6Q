1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b75b7b2309faf013dc4e6f8a76f50c01
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameNumberInterface.php * namespace

 * packageApplication
 * summary,This file is part of the ramsey/uuid library * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplatexFor the full copyright and license information, please view the LICENSE
file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * items	copyright &phpDocumentor\Descriptor\TagDescriptor

,Copyright (c) <PERSON> <<EMAIL>> license 

&http://opensource.org/licenses/MIT MIT package 

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 717862ddafecf8be10247e29c182d0f2 * path/vendor/ramsey/uuid/src/Type/NumberInterface.php	 * sourceu<?php

/**
 * This file is part of the ramsey/uuid library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) Ben Ramsey <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 */

declare(strict_types=1);

namespace Ramsey\Uuid\Type;

/**
 * NumberInterface ensures consistency in numeric values returned by ramsey/uuid
 *
 * @immutable
 */
interface NumberInterface extends TypeInterface
{
    /**
     * Returns true if this number is less than zero
     */
    public function isNegative(): bool;
}
 * namespaceAliases\Ramsey\Uuid\TypephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen($ phpDocumentor\Reflection\Fqsen nameType * includes * constants * functions
 * classes
 * interfaces!\Ramsey\Uuid\Type\NumberInterface,phpDocumentor\Descriptor\InterfaceDescriptor)*2+NumberInterface4(
MNumberInterface ensures consistency in numeric values returned by ramsey/uuid


	immutable 6


 ""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 789   
 * parents\Ramsey\Uuid\Type\TypeInterface)*;+
TypeInterface.
 * methods
isNegative)phpDocumentor\Descriptor\MethodDescriptor)*/\Ramsey\Uuid\Type\NumberInterface::isNegative()+>>( -Returns true if this number is less than zero


  789O789q  	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\Boolean ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums