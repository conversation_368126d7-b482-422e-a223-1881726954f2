1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-213b84c5bb0b413391936186fe6b6f46
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
laravel60.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 2a3ecd755190e10ca86fc55d2c58a461 * path:vendor/driftingly/rector-laravel/config/sets/laravel60.php	 * sources<?php

declare(strict_types=1);

use Rector\Arguments\Rector\ClassMethod\ArgumentAdderRector;
use Rector\Arguments\ValueObject\ArgumentAdder;
use Rector\Config\RectorConfig;
use Rector\Renaming\Rector\MethodCall\RenameMethodRector;
use Rector\Renaming\Rector\Name\RenameClassRector;
use Rector\Renaming\Rector\StaticCall\RenameStaticMethodRector;
use Rector\Renaming\ValueObject\MethodCallRename;
use Rector\Renaming\ValueObject\RenameStaticMethod;
use Rector\ValueObject\Visibility;
use Rector\Visibility\Rector\ClassMethod\ChangeMethodVisibilityRector;
use Rector\Visibility\ValueObject\ChangeMethodVisibility;

// see https://laravel.com/docs/6.x/upgrade
// https://github.com/laravel/docs/pull/5531/files

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->import(__DIR__ . '/../config.php');

    $rectorConfig
        ->ruleWithConfiguration(RenameMethodRector::class, [
            new MethodCallRename(
                'Illuminate\Auth\Access\Gate',
                // https://github.com/laravel/framework/commit/69de466ddc25966a0f6551f48acab1afa7bb9424
                'access',
                'inspect'
            ),
            new MethodCallRename(
                'Illuminate\Support\Facades\Lang',
                // https://github.com/laravel/framework/commit/efbe23c4116f86846ad6edc0d95cd56f4175a446
                'trans',
                'get'
            ),
            new MethodCallRename('Illuminate\Support\Facades\Lang', 'transChoice', 'choice'),
            new MethodCallRename(
                'Illuminate\Translation\Translator',
                // https://github.com/laravel/framework/commit/697b898a1c89881c91af83ecc4493fa681e2aa38
                'getFromJson',
                'get'
            ),
        ]);

    $rectorConfig
        ->ruleWithConfiguration(RenameStaticMethodRector::class, [
            // https://github.com/laravel/framework/commit/55785d3514a8149d4858acef40c56a31b6b2ccd1
            new RenameStaticMethod(
                'Illuminate\Support\Facades\Input',
                'get',
                'Illuminate\Support\Facades\Request',
                'input'
            ),
        ]);

    $rectorConfig
        ->ruleWithConfiguration(RenameClassRector::class, [
            'Illuminate\Support\Facades\Input' => 'Illuminate\Support\Facades\Request',
        ]);

    $rectorConfig
        ->ruleWithConfiguration(ChangeMethodVisibilityRector::class, [
            new ChangeMethodVisibility(
                'Illuminate\Foundation\Http\FormRequest',
                'validationData',
                Visibility::PUBLIC
            ),
        ]);

    $rectorConfig
        ->ruleWithConfiguration(ArgumentAdderRector::class, [                // https://github.com/laravel/framework/commit/6c1e014943a508afb2c10869c3175f7783a004e1
            new ArgumentAdder('Illuminate\Database\Capsule\Manager', 'table', 1, 'as', null),
        ]);
};
 * namespaceAliases * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums