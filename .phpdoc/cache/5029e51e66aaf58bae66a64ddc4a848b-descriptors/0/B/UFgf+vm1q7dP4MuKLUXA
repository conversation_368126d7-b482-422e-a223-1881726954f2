1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-1409c85de570fbde25d4ad97eba8a8e4
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name UndefinedMethodErrorEnhancer.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash d6999dfd87e82327917db9defe59db10 * pathKvendor/symfony/error-handler/ErrorEnhancer/UndefinedMethodErrorEnhancer.php	 * sourcea<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\ErrorHandler\ErrorEnhancer;

use Symfony\Component\ErrorHandler\Error\FatalError;
use Symfony\Component\ErrorHandler\Error\UndefinedMethodError;

/**
 * <AUTHOR> Pineau <<EMAIL>>
 */
class UndefinedMethodErrorEnhancer implements ErrorEnhancerInterface
{
    public function enhance(\Throwable $error): ?\Throwable
    {
        if ($error instanceof FatalError) {
            return null;
        }

        $message = $error->getMessage();
        preg_match('/^Call to undefined method (.*)::(.*)\(\)$/', $message, $matches);
        if (!$matches) {
            return null;
        }

        $className = $matches[1];
        $methodName = $matches[2];

        $message = \sprintf('Attempted to call an undefined method named "%s" of class "%s".', $methodName, $className);

        if ('' === $methodName || !class_exists($className) || null === $methods = get_class_methods($className)) {
            // failed to get the class or its methods on which an unknown method was called (for example on an anonymous class)
            return new UndefinedMethodError($message, $error);
        }

        $candidates = [];
        foreach ($methods as $definedMethodName) {
            $lev = levenshtein($methodName, $definedMethodName);
            if ($lev <= \strlen($methodName) / 3 || str_contains($definedMethodName, $methodName)) {
                $candidates[] = $definedMethodName;
            }
        }

        if ($candidates) {
            sort($candidates);
            $last = array_pop($candidates).'"?';
            if ($candidates) {
                $candidates = 'e.g. "'.implode('", "', $candidates).'" or "'.$last;
            } else {
                $candidates = '"'.$last;
            }

            $message .= "\nDid you mean to call ".$candidates;
        }

        return new UndefinedMethodError($message, $error);
    }
}
 * namespaceAliases-\Symfony\Component\ErrorHandler\ErrorEnhancerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
ErrorEnhancer * includes * constants * functions
 * classesJ\Symfony\Component\ErrorHandler\ErrorEnhancer\UndefinedMethodErrorEnhancer(phpDocumentor\Descriptor\ClassDescriptor#$+%UndefinedMethodErrorEnhancer-"
	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor.	%Grégoire Pineau <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 12B3   * readOnly * final * abstract
 * methodsenhance)phpDocumentor\Descriptor\MethodDescriptor#$U\Symfony\Component\ErrorHandler\ErrorEnhancer\UndefinedMethodErrorEnhancer::enhance()%88" 
	 
param  123 12A3] 	 * parent" * argumentserror+phpDocumentor\Descriptor\ArgumentDescriptor >
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$
\Throwable%	Throwable
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicHI	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realTypeBC#$D%E? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference56
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(< 
 * implementsD\Symfony\Component\ErrorHandler\ErrorEnhancer\ErrorEnhancerInterface#$X%ErrorEnhancerInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums