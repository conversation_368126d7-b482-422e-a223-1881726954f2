1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-9e7f7d4efadc1d2188336d2554e87567
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameLockTimeoutException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 9fa489db49b6f1a072b18e477bacb403 * path?vendor/illuminate/contracts/Filesystem/LockTimeoutException.php	 * source{<?php

namespace Illuminate\Contracts\Filesystem;

use Exception;

class LockTimeoutException extends Exception
{
    //
}
 * namespaceAliases \Illuminate\Contracts\FilesystemphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Filesystem * includes * constants * functions
 * classes5\Illuminate\Contracts\Filesystem\LockTimeoutException(phpDocumentor\Descriptor\ClassDescriptor#$+%LockTimeoutException-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./
0   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$
\Exception%	Exception
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums