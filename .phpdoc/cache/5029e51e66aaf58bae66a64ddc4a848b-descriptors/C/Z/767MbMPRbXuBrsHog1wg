1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-bea78b748060a47e08a91026900931ec
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameConditionExpression.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 0987d83537d398558baca83f27998416 * pathBvendor/illuminate/contracts/Database/Query/ConditionExpression.php	 * sourcel<?php

namespace Illuminate\Contracts\Database\Query;

interface ConditionExpression extends Expression
{
}
 * namespaceAliases$\Illuminate\Contracts\Database\QueryphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameQuery * includes * constants * functions
 * classes
 * interfaces8\Illuminate\Contracts\Database\Query\ConditionExpression,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ConditionExpression."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents/\Illuminate\Contracts\Database\Query\Expression#$3%
Expression(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums