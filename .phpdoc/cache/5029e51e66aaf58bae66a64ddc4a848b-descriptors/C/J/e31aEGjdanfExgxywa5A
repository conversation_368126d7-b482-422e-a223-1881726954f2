1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-435205dc90cded48e55160059d7f9bda
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Queueable.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 7c41519846b7500aefea83a61bc5ddb5 * pathLvendor/laravel-zero/foundation/src/Illuminate/Foundation/Queue/Queueable.php	 * source<<?php

namespace Illuminate\Foundation\Queue;

use Illuminate\Bus\Queueable as QueueableByBus;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

trait Queueable
{
    use Dispatchable, InteractsWithQueue, QueueableByBus, SerializesModels;
}
 * namespaceAliases\Illuminate\Foundation\QueuephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameQueue * includes * constants * functions
 * classes
 * interfaces	 * traits&\Illuminate\Foundation\Queue\Queueable(phpDocumentor\Descriptor\TraitDescriptor#$-%	Queueable/"

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber 01
2     
 * usedTraits'\Illuminate\Foundation\Bus\Dispatchable#$4%Dispatchable$\Illuminate\Queue\InteractsWithQueue#$6%InteractsWithQueue\Illuminate\Bus\Queueable#$8%/"\Illuminate\Queue\SerializesModels#$9%SerializesModels 
 * markers. phpDocumentor\Descriptor\FileDescriptor enums