1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c98f34f8b13f795f6c804d161b6f975b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namecompatibility.php * namespace

 * packageApplication
 * summarytThis file is part of the Nette Framework (https://nette.org)
Copyright (c) 2004 David <PERSON>l (https://davidgrudl.com) * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate
3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 0a80fe997b0858ed151145cfb5041cb8 * path=vendor/rector/rector/vendor/nette/utils/src/compatibility.php	 * source<?php

/**
 * This file is part of the Nette Framework (https://nette.org)
 * Copyright (c) 2004 David Grudl (https://davidgrudl.com)
 */
declare (strict_types=1);
namespace RectorPrefix202507\Nette\Utils;

use RectorPrefix202507\Nette;
if (\false) {
    /** @deprecated use Nette\HtmlStringable */
    interface IHtmlString extends Nette\HtmlStringable
    {
    }
} elseif (!\interface_exists(IHtmlString::class)) {
    \class_alias(Nette\HtmlStringable::class, IHtmlString::class);
}
namespace RectorPrefix202507\Nette\Localization;

if (\false) {
    /** @deprecated use Nette\Localization\Translator */
    interface ITranslator extends Translator
    {
    }
} elseif (!\interface_exists(ITranslator::class)) {
    \class_alias(Translator::class, ITranslator::class);
}
 * namespaceAliases\RectorPrefix202507\Nette\UtilsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen#$ phpDocumentor\Reflection\Fqsen nameUtils&\RectorPrefix202507\Nette\Localization$%(&Localization * includes * constants * functions
 * classes
 * interfaces+\RectorPrefix202507\Nette\Utils\IHtmlString,phpDocumentor\Descriptor\InterfaceDescriptor$%/&IHtmlString1#





deprecated 1phpDocumentor\Descriptor\Tag\DeprecatedDescriptor2

use Nette\HtmlStringable : phpDocumentor\Descriptor\Tag\DeprecatedDescriptor version
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber 678  
 * parents(\RectorPrefix202507\Nette\HtmlStringable$%:&HtmlStringable+
 * methods 2\RectorPrefix202507\Nette\Localization\ITranslator0$%=&ITranslator>(




2 32

!use Nette\Localization\Translator 5
""  678 678  91\RectorPrefix202507\Nette\Localization\Translator$%@&
Translator+< 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums