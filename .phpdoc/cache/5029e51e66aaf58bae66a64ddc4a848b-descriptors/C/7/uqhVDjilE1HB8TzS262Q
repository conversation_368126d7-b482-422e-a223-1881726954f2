1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0ff444896586f8a4d5f2c52ee28540c7
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameShiftLeftToShiftRight.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 1d64031c11d96929cb228d6caa0d79be * pathSvendor/pestphp/pest-plugin-mutate/src/Mutators/Arithmetic/ShiftLeftToShiftRight.php	 * source<?php

declare(strict_types=1);

namespace Pest\Mutate\Mutators\Arithmetic;

use Pest\Mutate\Mutators\Abstract\AbstractMutator;
use PhpParser\Node;
use PhpParser\Node\Expr\BinaryOp\ShiftLeft;
use PhpParser\Node\Expr\BinaryOp\ShiftRight;

class ShiftLeftToShiftRight extends AbstractMutator
{
    public const SET = 'Arithmetic';

    public const DESCRIPTION = 'Replaces `<<` with `>>`.';

    public const DIFF = <<<'DIFF'
        $b = $a << 1;  // [tl! remove]
        $b = $a >> 1;  // [tl! add]
        DIFF;

    public static function nodesToHandle(): array
    {
        return [ShiftLeft::class];
    }

    public static function mutate(Node $node): Node
    {
        /** @var ShiftLeft $node */

        return new ShiftRight($node->left, $node->right, $node->getAttributes());
    }
}
 * namespaceAliases \Pest\Mutate\Mutators\ArithmeticphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Arithmetic * includes * constants * functions
 * classes6\Pest\Mutate\Mutators\Arithmetic\ShiftLeftToShiftRight(phpDocumentor\Descriptor\ClassDescriptor#$+%ShiftLeftToShiftRight-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./"0   * readOnly * final * abstract
 * methods
nodesToHandle)phpDocumentor\Descriptor\MethodDescriptor#$G\Pest\Mutate\Mutators\Arithmetic\ShiftLeftToShiftRight::nodesToHandle()%55" 
	 
  ./0./0a 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  mutate6#$@\Pest\Mutate\Mutators\Arithmetic\ShiftLeftToShiftRight::mutate()%NN" 
	 
param  ./0h./!0 8"9node+phpDocumentor\Descriptor\ArgumentDescriptor Q
 
	 
  "/"0 3 phpDocumentor\Descriptor\ArgumentDescriptor method"* * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\PhpParser\Node%Node
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadic[\:;UV#$W%XG23HIJ")M  
 * properties(SET+phpDocumentor\Descriptor\ConstantDescriptor#$;\Pest\Mutate\Mutators\Arithmetic\ShiftLeftToShiftRight::SET%__+ 
	 
  ./0 ./0   * value'Arithmetic'2HIJ")M 8"T  DESCRIPTION`#$C\Pest\Mutate\Mutators\Arithmetic\ShiftLeftToShiftRight::DESCRIPTION%dd+ 
	 
  ./0 ./0  b'Replaces `<<` with `>>`.'2HIJ")M 8"T  DIFF`#$<\Pest\Mutate\Mutators\Arithmetic\ShiftLeftToShiftRight::DIFF%gg+ 
	 
  ./0 ./0  bI<<<'DIFF'
$b = $a << 1;  // [tl! remove]
$b = $a >> 1;  // [tl! add]
DIFF2HIJ")M 8"T  8#$.\Pest\Mutate\Mutators\Abstract\AbstractMutator%AbstractMutator
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums