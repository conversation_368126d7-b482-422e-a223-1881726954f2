1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c8b5d36282e582149ac0be0568238843
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameObservedBy.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash fe0bb908162a1e84a6840869e2ff5a5b * path=vendor/illuminate/database/Eloquent/Attributes/ObservedBy.php	 * sourceP<?php

namespace Illuminate\Database\Eloquent\Attributes;

use Attribute;

#[Attribute(Attribute::TARGET_CLASS | Attribute::IS_REPEATABLE)]
class ObservedBy
{
    /**
     * Create a new attribute instance.
     *
     * @param  array|string  $classes
     */
    public function __construct(public array|string $classes)
    {
    }
}
 * namespaceAliases(\Illuminate\Database\Eloquent\AttributesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Attributes * includes * constants * functions
 * classes3\Illuminate\Database\Eloquent\Attributes\ObservedBy(phpDocumentor\Descriptor\ClassDescriptor#$+%
ObservedBy-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$B\Illuminate\Database\Eloquent\Attributes\ObservedBy::__construct()%55"  Create a new attribute instance.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor9	
  * type'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types %phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType<= &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|C EF * variableNameclasses  ./0./0L 	 * parent" * argumentsH+phpDocumentor\Descriptor\ArgumentDescriptor H
 
	"$
  "/"0 3 phpDocumentor\Descriptor\ArgumentDescriptor method";"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicOP	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType@ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * propertiesH+phpDocumentor\Descriptor\PropertyDescriptor#$=\Illuminate\Database\Eloquent\Attributes\ObservedBy::$classes%HH+ 
	 
var  ./0$./0? I"R5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualUVW";Z ;<= >?@ A B<= C D EFC EFM  (I 
 * implements
 * usedTraits
 * attributes ,phpDocumentor\Descriptor\AttributeDescriptor7 phpDocumentor\Descriptor\AttributeDescriptor arguments 2phpDocumentor\Descriptor\ValueObjects\CallArgument9 phpDocumentor\Descriptor\ValueObjects\CallArgument value4\Attribute::TARGET_CLASS | \Attribute::IS_REPEATABLE8 phpDocumentor\Descriptor\ValueObjects\CallArgument name < phpDocumentor\Descriptor\AttributeDescriptor attributeClass 	Attribute	 #$
\Attribute%m
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums