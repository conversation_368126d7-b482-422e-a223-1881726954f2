1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-05730068d7e9f483416c17f513fc7a68
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePhpFileLoader.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 48700df42f6f7442620d7907c83ada78 * path3vendor/symfony/translation/Loader/PhpFileLoader.php	 * source<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Translation\Loader;

/**
 * PhpFileLoader loads translations from PHP files returning an array of translations.
 *
 * <AUTHOR> Potencier <<EMAIL>>
 */
class PhpFileLoader extends FileLoader
{
    private static ?array $cache = [];

    protected function loadResource(string $resource): array
    {
        if ([] === self::$cache && \function_exists('opcache_invalidate') && filter_var(\ini_get('opcache.enable'), \FILTER_VALIDATE_BOOL) && (!\in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) || filter_var(\ini_get('opcache.enable_cli'), \FILTER_VALIDATE_BOOL))) {
            self::$cache = null;
        }

        if (null === self::$cache) {
            return require $resource;
        }

        return self::$cache[$resource] ??= require $resource;
    }
}
 * namespaceAliases%\Symfony\Component\Translation\LoaderphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameLoader * includes * constants * functions
 * classes3\Symfony\Component\Translation\Loader\PhpFileLoader(phpDocumentor\Descriptor\ClassDescriptor#$+%
PhpFileLoader-"SPhpFileLoader loads translations from PHP files returning an array of translations.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	%Fabien Potencier <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 23#4   * readOnly * final * abstract
 * methodsloadResource)phpDocumentor\Descriptor\MethodDescriptor#$C\Symfony\Component\Translation\Loader\PhpFileLoader::loadResource()%99" 
	 
param  23423"4 	 * parent" * argumentsresource+phpDocumentor\Descriptor\ArgumentDescriptor ?
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type&phpDocumentor\Reflection\Types\String_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicFG	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types C &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference67
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'	PROTECTED7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * propertiescache+phpDocumentor\Descriptor\PropertyDescriptor#$;\Symfony\Component\Translation\Loader\PhpFileLoader::$cache%]]+ 
	 
var  234 234  ="I5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualVWXY'PRIVATE[ B'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realTypeKLM N OPQ C R STD[] (=#$0\Symfony\Component\Translation\Loader\FileLoader%
FileLoader
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums