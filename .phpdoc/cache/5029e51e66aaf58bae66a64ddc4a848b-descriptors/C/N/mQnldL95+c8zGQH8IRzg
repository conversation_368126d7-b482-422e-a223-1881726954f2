1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-69864cc366d88feedba294652c92f112
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameImplementsTagValueNode.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 3f7dd4b51e42ab8465aab74cd96f410a * pathFvendor/phpstan/phpdoc-parser/src/Ast/PhpDoc/ImplementsTagValueNode.php	 * sourceh<?php declare(strict_types = 1);

namespace PHPStan\PhpDocParser\Ast\PhpDoc;

use PHPStan\PhpDocParser\Ast\NodeAttributes;
use PHPStan\PhpDocParser\Ast\Type\GenericTypeNode;
use function trim;

class ImplementsTagValueNode implements PhpDocTagValueNode
{

	use NodeAttributes;

	public GenericTypeNode $type;

	/** @var string (may be empty) */
	public string $description;

	public function __construct(GenericTypeNode $type, string $description)
	{
		$this->type = $type;
		$this->description = $description;
	}


	public function __toString(): string
	{
		return trim("{$this->type} {$this->description}");
	}

}
 * namespaceAliases \PHPStan\PhpDocParser\Ast\PhpDocphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen namePhpDoc * includes * constants * functions
 * classes7\PHPStan\PhpDocParser\Ast\PhpDoc\ImplementsTagValueNode(phpDocumentor\Descriptor\ClassDescriptor#$+%ImplementsTagValueNode-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber	/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$F\PHPStan\PhpDocParser\Ast\PhpDoc\ImplementsTagValueNode::__construct()%55" 
	 
param  ./0x./0  	 * parent" * argumentstype+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$.\PHPStan\PhpDocParser\Ast\Type\GenericTypeNode%GenericTypeNode
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEFdescription< H
 
	 
  " "! =">&phpDocumentor\Reflection\Types\String_ C DEFGEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
__toString6#$E\PHPStan\PhpDocParser\Ast\PhpDoc\ImplementsTagValueNode::__toString()%TT" 
	 
  ./0./0c 9":JKI M23NOP"4S  
 * properties;+phpDocumentor\Descriptor\PropertyDescriptor#$>\PHPStan\PhpDocParser\Ast\PhpDoc\ImplementsTagValueNode::$type%;;+ 
	 
var  ./0 ./0  9"J5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualNOP"4S >?@#$A%BC  HW#$E\PHPStan\PhpDocParser\Ast\PhpDoc\ImplementsTagValueNode::$description%HH+ 
	

Y *phpDocumentor\Descriptor\Tag\VarDescriptorY	(may be empty) >I  * variableName
  ./0 ./0  9"JZ[\]NOP"4S >"WC  (9 
 * implements3\PHPStan\PhpDocParser\Ast\PhpDoc\PhpDocTagValueNode#$c%PhpDocTagValueNode
 * usedTraits(\PHPStan\PhpDocParser\Ast\NodeAttributes#$f%NodeAttributes 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums