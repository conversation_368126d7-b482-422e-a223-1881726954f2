1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-eaf8cb4eb534138136094e5c3cc9a619
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name3TimeEfficientLongestCommonSubsequenceCalculator.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 94cf70204f3061332ff347ff808fb314 * pathMvendor/sebastian/diff/src/TimeEfficientLongestCommonSubsequenceCalculator.php	 * source	@<?php declare(strict_types=1);
/*
 * This file is part of sebastian/diff.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SebastianBergmann\Diff;

use function array_reverse;
use function count;
use function max;
use SplFixedArray;

final class TimeEfficientLongestCommonSubsequenceCalculator implements LongestCommonSubsequenceCalculator
{
    /**
     * @inheritDoc
     */
    public function calculate(array $from, array $to): array
    {
        $common     = [];
        $fromLength = count($from);
        $toLength   = count($to);
        $width      = $fromLength + 1;
        $matrix     = new SplFixedArray($width * ($toLength + 1));

        for ($i = 0; $i <= $fromLength; $i++) {
            $matrix[$i] = 0;
        }

        for ($j = 0; $j <= $toLength; $j++) {
            $matrix[$j * $width] = 0;
        }

        for ($i = 1; $i <= $fromLength; $i++) {
            for ($j = 1; $j <= $toLength; $j++) {
                $o = ($j * $width) + $i;

                // don't use max() to avoid function call overhead
                $firstOrLast = $from[$i - 1] === $to[$j - 1] ? $matrix[$o - $width - 1] + 1 : 0;

                if ($matrix[$o - 1] > $matrix[$o - $width]) {
                    if ($firstOrLast > $matrix[$o - 1]) {
                        $matrix[$o] = $firstOrLast;
                    } else {
                        $matrix[$o] = $matrix[$o - 1];
                    }
                } else {
                    if ($firstOrLast > $matrix[$o - $width]) {
                        $matrix[$o] = $firstOrLast;
                    } else {
                        $matrix[$o] = $matrix[$o - $width];
                    }
                }
            }
        }

        $i = $fromLength;
        $j = $toLength;

        while ($i > 0 && $j > 0) {
            if ($from[$i - 1] === $to[$j - 1]) {
                $common[] = $from[$i - 1];
                $i--;
                $j--;
            } else {
                $o = ($j * $width) + $i;

                if ($matrix[$o - $width] > $matrix[$o - 1]) {
                    $j--;
                } else {
                    $i--;
                }
            }
        }

        return array_reverse($common);
    }
}
 * namespaceAliases\SebastianBergmann\DiffphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDiff * includes * constants * functions
 * classesG\SebastianBergmann\Diff\TimeEfficientLongestCommonSubsequenceCalculator(phpDocumentor\Descriptor\ClassDescriptor#$+%/TimeEfficientLongestCommonSubsequenceCalculator-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./R0   * readOnly * final * abstract
 * methods	calculate)phpDocumentor\Descriptor\MethodDescriptor#$T\SebastianBergmann\Diff\TimeEfficientLongestCommonSubsequenceCalculator::calculate()%55" 
	


inheritDoc 8	
 param  ./0./Q0	< 	 * parent" * argumentsfrom+phpDocumentor\Descriptor\ArgumentDescriptor <
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicMNto= P
 
	 
  "'"( >"?@AB C DEF G H IJK LMNOMN	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType@AB C DEF G H IJ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(: 
 * implements:\SebastianBergmann\Diff\LongestCommonSubsequenceCalculator#$\%"LongestCommonSubsequenceCalculator
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums