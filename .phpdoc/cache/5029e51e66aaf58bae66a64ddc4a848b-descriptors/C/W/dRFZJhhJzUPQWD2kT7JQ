1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-1ae810e9d2a43c70f293a4da049f0dc5
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameClosureScope.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 31549af806f83afd169cf0a35dd05a7f * path@vendor/laravel/serializable-closure/src/Support/ClosureScope.php	 * sourcez<?php

namespace Laravel\SerializableClosure\Support;

use SplObjectStorage;

class ClosureScope extends SplObjectStorage
{
    /**
     * The number of serializations in current scope.
     *
     * @var int
     */
    public $serializations = 0;

    /**
     * The number of closures that have to be serialized.
     *
     * @var int
     */
    public $toSerialize = 0;
}
 * namespaceAliases$\Laravel\SerializableClosure\SupportphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSupport * includes * constants * functions
 * classes1\Laravel\SerializableClosure\Support\ClosureScope(phpDocumentor\Descriptor\ClassDescriptor#$+%ClosureScope-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * propertiesserializations+phpDocumentor\Descriptor\PropertyDescriptor#$B\Laravel\SerializableClosure\Support\ClosureScope::$serializations%66+ .The number of serializations in current scope.	

var *phpDocumentor\Descriptor\Tag\VarDescriptor:	
  * type&phpDocumentor\Reflection\Types\Integer  * variableName
  ./0 ./0  	 * parent"	 * static5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtual
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write <"'
 * default0 toSerialize7#$?\Laravel\SerializableClosure\Support\ClosureScope::$toSerialize%MM+ 2The number of closures that have to be serialized.	

: ;:	
 <= >
  ./0 ./0  ?"@ABCDEFG"-J <"9KL (?#$\SplObjectStorage%SplObjectStorage
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums