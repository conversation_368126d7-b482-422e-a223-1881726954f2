1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-de4fa28d7f38bc29e567d7c20a08e621
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name#InvalidApplicationNameException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 04641603863bc2cdeb87f8fb9c72f256 * pathJvendor/phar-io/manifest/src/exceptions/InvalidApplicationNameException.php	 * source1<?php declare(strict_types = 1);
/*
 * This file is part of PharIo\Manifest.
 *
 * Copyright (c) Arne Blankerts <<EMAIL>>, Sebastian Heuer <<EMAIL>>, Sebastian Bergmann <<EMAIL>> and contributors
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 */
namespace PharIo\Manifest;

use InvalidArgumentException;

class InvalidApplicationNameException extends InvalidArgumentException implements Exception {
    public const InvalidFormat = 2;
}
 * namespaceAliases\PharIo\ManifestphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameManifest * includes * constants * functions
 * classes0\PharIo\Manifest\InvalidApplicationNameException(phpDocumentor\Descriptor\ClassDescriptor#$+%InvalidApplicationNameException-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(
InvalidFormat+phpDocumentor\Descriptor\ConstantDescriptor#$?\PharIo\Manifest\InvalidApplicationNameException::InvalidFormat%66+ 
	 
  ./0 ./0   * value22
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent" * type  A#$\InvalidArgumentException%InvalidArgumentException
 * implements\PharIo\Manifest\Exception#$F%	Exception
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums