1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-4904e4b09560f0722025503311ec6029
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameProcessFailedException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 110e5299d453157f26434882a90cce2b * pathPvendor/rector/rector/vendor/symfony/process/Exception/ProcessFailedException.php	 * sourcex<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace RectorPrefix202507\Symfony\Component\Process\Exception;

use RectorPrefix202507\Symfony\Component\Process\Process;
/**
 * Exception for failed processes.
 *
 * <AUTHOR> M. Schmitt <<EMAIL>>
 */
class ProcessFailedException extends RuntimeException
{
    private Process $process;
    public function __construct(Process $process)
    {
        if ($process->isSuccessful()) {
            throw new InvalidArgumentException('Expected a failed process, but the given process was successful.');
        }
        $error = \sprintf('The command "%s" failed.' . "\n\nExit Code: %s(%s)\n\nWorking directory: %s", $process->getCommandLine(), $process->getExitCode(), $process->getExitCodeText(), $process->getWorkingDirectory());
        if (!$process->isOutputDisabled()) {
            $error .= \sprintf("\n\nOutput:\n================\n%s\n\nError Output:\n================\n%s", $process->getOutput(), $process->getErrorOutput());
        }
        parent::__construct($error);
        $this->process = $process;
    }
    /**
     * @return Process
     */
    public function getProcess()
    {
        return $this->process;
    }
}
 * namespaceAliases7\RectorPrefix202507\Symfony\Component\Process\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classesN\RectorPrefix202507\Symfony\Component\Process\Exception\ProcessFailedException(phpDocumentor\Descriptor\ClassDescriptor#$+%ProcessFailedException-"Exception for failed processes.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	*Johannes M. Schmitt <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 23)4   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$]\RectorPrefix202507\Symfony\Component\Process\Exception\ProcessFailedException::__construct()%99" 
	 
param  234(23!4 	 * parent" * argumentsprocess+phpDocumentor\Descriptor\ArgumentDescriptor ?
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$5\RectorPrefix202507\Symfony\Component\Process\Process%Process
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicIJ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference67
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
getProcess:#$\\RectorPrefix202507\Symfony\Component\Process\Exception\ProcessFailedException::getProcess()%VV" 
	

return -phpDocumentor\Descriptor\Tag\ReturnDescriptorX	
 BCD#$E%F  23%4.23(4t =">LMN O67PQR"5U  
 * properties?+phpDocumentor\Descriptor\PropertyDescriptor#$X\RectorPrefix202507\Symfony\Component\Process\Exception\ProcessFailedException::$process%??+ 
	 
var  234 234  ="L5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualPQRS'PRIVATEU BCD#$E%FG  (=#$H\RectorPrefix202507\Symfony\Component\Process\Exception\RuntimeException%RuntimeException
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums