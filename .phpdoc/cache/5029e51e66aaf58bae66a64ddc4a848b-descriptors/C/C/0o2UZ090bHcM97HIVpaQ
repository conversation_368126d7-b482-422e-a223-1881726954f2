1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-1d889811b7b8786069a016c0e41a26a6
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
functions.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash d2cbf1e07d5aab7272e32b36cc0ee832 * path&vendor/illuminate/events/functions.php	 * sourcej<?php

namespace Illuminate\Events;

use Closure;

if (! function_exists('Illuminate\Events\queueable')) {
    /**
     * Create a new queued Closure event listener.
     *
     * @param  \Closure  $closure
     * @return \Illuminate\Events\QueuedClosure
     */
    function queueable(Closure $closure)
    {
        return new QueuedClosure($closure);
    }
}
 * namespaceAliases\Illuminate\EventsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEvents * includes * constants * functions\Illuminate\Events\queueable()+phpDocumentor\Descriptor\FunctionDescriptor#$*%	queueable," +Create a new queued Closure event listener.	


"param ,phpDocumentor\Descriptor\Tag\ParamDescriptor.	
  * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\Closure%Closure * variableNameclosurereturn -phpDocumentor\Descriptor\Tag\ReturnDescriptor7	
 012#$ \Illuminate\Events\QueuedClosure%
QueuedClosure"  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ;<=   * arguments6+phpDocumentor\Descriptor\ArgumentDescriptor 6
 
	"
  "%"&  0"
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicBC7 phpDocumentor\Descriptor\FunctionDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ A phpDocumentor\Descriptor\FunctionDescriptor hasReturnByReference
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums