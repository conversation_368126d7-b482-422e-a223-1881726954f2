1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-ee1024951467fd8f8b2c0f81877cd675
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameContextualAttribute.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 2bab0c019822e157bcea6c9ca8a201b4 * pathRvendor/rector/rector/vendor/illuminate/contracts/Container/ContextualAttribute.php	 * sourcen<?php

namespace RectorPrefix202507\Illuminate\Contracts\Container;

interface ContextualAttribute
{
    //
}
 * namespaceAliases2\RectorPrefix202507\Illuminate\Contracts\ContainerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Container * includes * constants * functions
 * classes
 * interfacesF\RectorPrefix202507\Illuminate\Contracts\Container\ContextualAttribute,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ContextualAttribute."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums