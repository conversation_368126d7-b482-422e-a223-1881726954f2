1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b689dfda0d1bdb68d7b3c86d55c37805
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameCsvFileDumper.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 900e63adb50f8e23b881bf21b182eea8 * path3vendor/symfony/translation/Dumper/CsvFileDumper.php	 * source^<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Translation\Dumper;

use Symfony\Component\Translation\MessageCatalogue;

/**
 * CsvFileDumper generates a csv formatted string representation of a message catalogue.
 *
 * <AUTHOR>
 */
class CsvFileDumper extends FileDumper
{
    private string $delimiter = ';';
    private string $enclosure = '"';

    public function formatCatalogue(MessageCatalogue $messages, string $domain, array $options = []): string
    {
        $handle = fopen('php://memory', 'r+');

        foreach ($messages->all($domain) as $source => $target) {
            fputcsv($handle, [$source, $target], $this->delimiter, $this->enclosure, '\\');
        }

        rewind($handle);
        $output = stream_get_contents($handle);
        fclose($handle);

        return $output;
    }

    /**
     * Sets the delimiter and escape character for CSV.
     */
    public function setCsvControl(string $delimiter = ';', string $enclosure = '"'): void
    {
        $this->delimiter = $delimiter;
        $this->enclosure = $enclosure;
    }

    protected function getExtension(): string
    {
        return 'csv';
    }
}
 * namespaceAliases%\Symfony\Component\Translation\DumperphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDumper * includes * constants * functions
 * classes3\Symfony\Component\Translation\Dumper\CsvFileDumper(phpDocumentor\Descriptor\ClassDescriptor#$+%
CsvFileDumper-"UCsvFileDumper generates a csv formatted string representation of a message catalogue.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/		Stealth35 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 2364   * readOnly * final * abstract
 * methodsformatCatalogue)phpDocumentor\Descriptor\MethodDescriptor#$F\Symfony\Component\Translation\Dumper\CsvFileDumper::formatCatalogue()%99" 
	 
param  234E23'4 	 * parent" * argumentsmessages+phpDocumentor\Descriptor\ArgumentDescriptor ?
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$/\Symfony\Component\Translation\MessageCatalogue%MessageCatalogue
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicIJdomain@ L
 
	 
  "'"( A""B&phpDocumentor\Reflection\Types\String_ G HIJKIJoptions@ N
 
	 
  "'"( A""B%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types M &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|G[]HIJKIJ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnTypeM ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference67
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
setCsvControl:#$D\Symfony\Component\Translation\Dumper\CsvFileDumper::setCsvControl()%cc" 0Sets the delimiter and escape character for CSV.	

<  23,4[2304	 =">	delimiter@ f
 
	 
  "N"O A"GBM G';'HIJKIJ	enclosure@ h
 
	 
  "N"O A"GBM G'"'HIJKIJZ[$phpDocumentor\Reflection\Types\Void_ \67]^_"Fb  getExtension:#$C\Symfony\Component\Translation\Dumper\CsvFileDumper::getExtension()%kk" 
	 
  23242354Z =">Z[M \67]^_`'	PROTECTEDb  
 * propertiesf+phpDocumentor\Descriptor\PropertyDescriptor#$?\Symfony\Component\Translation\Dumper\CsvFileDumper::$delimiter%ff+ 
	 
var  234 234  ="Z5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtual]^_`'PRIVATEb BM Gg ho#$?\Symfony\Component\Translation\Dumper\CsvFileDumper::$enclosure%hh+ 
	 
q  234 234  ="Zrstu]^_"wb BM Gi (=#$0\Symfony\Component\Translation\Dumper\FileDumper%
FileDumper
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums