1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b341e5ac3d31531f47f75d81fc72e06d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameVarTagValueNodeRenamer.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 9acd873c02813944622f60f608d65161 * pathCvendor/rector/rector/rules/Naming/PhpDoc/VarTagValueNodeRenamer.php	 * sourceY<?php

declare (strict_types=1);
namespace Rector\Naming\PhpDoc;

use PHPStan\PhpDocParser\Ast\PhpDoc\VarTagValueNode;
use Rector\BetterPhpDocParser\PhpDocInfo\PhpDocInfo;
use Rector\BetterPhpDocParser\ValueObject\PhpDocAttributeKey;
final class VarTagValueNodeRenamer
{
    public function renameAssignVarTagVariableName(PhpDocInfo $phpDocInfo, string $originalName, string $expectedName) : void
    {
        $varTagValueNode = $phpDocInfo->getVarTagValueNode();
        if (!$varTagValueNode instanceof VarTagValueNode) {
            return;
        }
        if ($varTagValueNode->variableName !== '$' . $originalName) {
            return;
        }
        $varTagValueNode->variableName = '$' . $expectedName;
        // invoke node reprint - same as in php-parser
        $varTagValueNode->setAttribute(PhpDocAttributeKey::ORIG_NODE, null);
    }
}
 * namespaceAliases\Rector\Naming\PhpDocphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen namePhpDoc * includes * constants * functions
 * classes,\Rector\Naming\PhpDoc\VarTagValueNodeRenamer(phpDocumentor\Descriptor\ClassDescriptor#$+%VarTagValueNodeRenamer-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber	/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methodsrenameAssignVarTagVariableName)phpDocumentor\Descriptor\MethodDescriptor#$N\Rector\Naming\PhpDoc\VarTagValueNodeRenamer::renameAssignVarTagVariableName()%55" 
	 
param  ./0./0U 	 * parent" * arguments
phpDocInfo+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$0\Rector\BetterPhpDocParser\PhpDocInfo\PhpDocInfo%
PhpDocInfo
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEForiginalName< H
 
	 
  " "! =">&phpDocumentor\Reflection\Types\String_ C DEFGEFexpectedName< J
 
	 
  " "! =">I C DEFGEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(9 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums