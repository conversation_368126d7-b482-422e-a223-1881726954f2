1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-1c9d96aa6f55162657f7c1373ae175c0
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRetrievingManyKeys.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash dc3ef419699b2c6f74ef624299939472 * path5vendor/illuminate/cache/Events/RetrievingManyKeys.php	 * source
<?php

namespace Illuminate\Cache\Events;

class RetrievingManyKeys extends CacheEvent
{
    /**
     * The keys that are being retrieved.
     *
     * @var array
     */
    public $keys;

    /**
     * Create a new event instance.
     *
     * @param  string|null  $storeName
     * @param  array  $keys
     * @param  array  $tags
     */
    public function __construct($storeName, $keys, array $tags = [])
    {
        parent::__construct($storeName, $keys[0] ?? '', $tags);

        $this->keys = $keys;
    }
}
 * namespaceAliases\Illuminate\Cache\EventsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEvents * includes * constants * functions
 * classes+\Illuminate\Cache\Events\RetrievingManyKeys(phpDocumentor\Descriptor\ClassDescriptor#$+%RetrievingManyKeys-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$:\Illuminate\Cache\Events\RetrievingManyKeys::__construct()%55" Create a new event instance.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor9	
  * type'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ $phpDocumentor\Reflection\Types\Null_ 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableName	storeName:9	
 ;%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType<= > &phpDocumentor\Reflection\Types\Integer @ABkeys:9	
 ;DEF G H<= > I @ABtags  ./0]./0 	 * parent" * argumentsC+phpDocumentor\Descriptor\ArgumentDescriptor C
 
	"$
  "<"= 3 phpDocumentor\Descriptor\ArgumentDescriptor method";"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicRSJN J
 
	"+
  "<"= O";"-P QRSTRSKN K
 
	"4
  "<"= O";"6P[]QRSTRS	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnTypeF ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * propertiesJ+phpDocumentor\Descriptor\PropertyDescriptor#$2\Illuminate\Cache\Events\RetrievingManyKeys::$keys%JJ+ "The keys that are being retrieved.	

var *phpDocumentor\Descriptor\Tag\VarDescriptorc	
 ;DEF G H<= > I @AB
  ./0 ./0  L"V5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualYZ["R^ ;"`P  (L#$#\Illuminate\Cache\Events\CacheEvent%
CacheEvent
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums