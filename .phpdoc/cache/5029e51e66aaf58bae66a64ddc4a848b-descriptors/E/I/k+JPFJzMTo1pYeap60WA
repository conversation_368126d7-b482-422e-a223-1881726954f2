1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-59fc548e0b65a44679407a5eedc80965
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePipeContract.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 20e7a8b47597673835b5122542f1d6a4 * pathCvendor/larastan/larastan/src/Contracts/Types/Pipes/PipeContract.php	 * source<?php

declare(strict_types=1);

namespace Larastan\Larastan\Contracts\Types\Pipes;

use Closure;
use Larastan\Larastan\Contracts\Types\PassableContract;

/** @internal */
interface PipeContract
{
    public function handle(PassableContract $passable, Closure $next): void;
}
 * namespaceAliases(\Larastan\Larastan\Contracts\Types\PipesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen namePipes * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums