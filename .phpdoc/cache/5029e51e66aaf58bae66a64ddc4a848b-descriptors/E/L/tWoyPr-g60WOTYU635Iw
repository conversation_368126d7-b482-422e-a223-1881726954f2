1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-a411c1a8b33182905141172c840fe4e0
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name!DateInvalidOperationException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 009f48a89ac63f5be0295165eeb39ba0 * pathOvendor/symfony/polyfill-php83/Resources/stubs/DateInvalidOperationException.php	 * sourceY<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

if (\PHP_VERSION_ID < 80300) {
    class DateInvalidOperationException extends DateException
    {
    }
}
 * namespaceAliases * includes * constants * functions
 * classes\DateInvalidOperationException(phpDocumentor\Descriptor\ClassDescriptorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen&$ phpDocumentor\Reflection\Fqsen nameDateInvalidOperationException+

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ,-.   * readOnly * final * abstract
 * methods
 * properties#	 * parent()\DateException*
DateException
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums