1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-ce62ce9cb8260de759a561afe4aa340b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRelatedPolyfillInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash c03b47353f3e8bf20f1131c8533d6727 * pathMvendor/rector/rector/src/VersionBonding/Contract/RelatedPolyfillInterface.php	 * sourceR<?php

declare (strict_types=1);
namespace Rector\VersionBonding\Contract;

use Rector\ValueObject\PolyfillPackage;
/**
 * Can be implemented by @see \Rector\Contract\Rector\RectorInterface
 */
interface RelatedPolyfillInterface
{
    /**
     * @return PolyfillPackage::*
     */
    public function providePolyfillPackage() : string;
}
 * namespaceAliases\Rector\VersionBonding\ContractphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameContract * includes * constants * functions
 * classes
 * interfaces8\Rector\VersionBonding\Contract\RelatedPolyfillInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%RelatedPolyfillInterface."
BCan be implemented by @see \Rector\Contract\Rector\RectorInterface	


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber 012  
 * parents(
 * methodsprovidePolyfillPackage)phpDocumentor\Descriptor\MethodDescriptor#$R\Rector\VersionBonding\Contract\RelatedPolyfillInterface::providePolyfillPackage()%55" 
	

return -phpDocumentor\Descriptor\Tag\ReturnDescriptor8	
  * type4phpDocumentor\Reflection\PseudoTypes\ConstExpression; phpDocumentor\Reflection\PseudoTypes\ConstExpression owner&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$#\Rector\ValueObject\PolyfillPackage%PolyfillPackage@ phpDocumentor\Reflection\PseudoTypes\ConstExpression expression*  012012N 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\String_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums