1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0d0f0f0858fe9073b95d047719b13235
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameBeforeAllRepository.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash e0622b8c8a1be4042715d0e948b97e73 * path<vendor/pestphp/pest/src/Repositories/BeforeAllRepository.php	 * sourcee<?php

declare(strict_types=1);

namespace Pest\Repositories;

use Closure;
use Pest\Exceptions\BeforeAllAlreadyExist;
use Pest\Support\NullClosure;
use Pest\Support\Reflection;

/**
 * @internal
 */
final class BeforeAllRepository
{
    /**
     * @var array<string, Closure>
     */
    private array $state = [];

    /**
     * Runs one before all closure, and unsets it from the repository.
     */
    public function pop(string $filename): Closure
    {
        $closure = $this->get($filename);

        unset($this->state[$filename]);

        return $closure;
    }

    /**
     * Sets a before all closure.
     */
    public function set(Closure $closure): void
    {
        $filename = Reflection::getFileNameFromClosure($closure);

        if (array_key_exists($filename, $this->state)) {
            throw new BeforeAllAlreadyExist($filename);
        }

        $this->state[$filename] = $closure;
    }

    /**
     * Gets a before all closure by the given filename.
     */
    public function get(string $filename): Closure
    {
        return $this->state[$filename] ?? NullClosure::create();
    }
}
 * namespaceAliases\Pest\RepositoriesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRepositories * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums