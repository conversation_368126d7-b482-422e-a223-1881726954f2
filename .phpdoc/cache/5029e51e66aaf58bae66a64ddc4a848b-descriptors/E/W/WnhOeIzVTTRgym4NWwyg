1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-fabe812b62664b0e758469cacde9a0d8
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameNoEmailAddressException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 1dc3a57a9c6f9af67a9f2de4048e13b1 * pathBvendor/phar-io/manifest/src/exceptions/NoEmailAddressException.php	 * source<?php declare(strict_types = 1);
/*
 * This file is part of PharIo\Manifest.
 *
 * Copyright (c) Arne Blankerts <<EMAIL>>, Sebastian Heuer <<EMAIL>>, Sebastian Bergmann <<EMAIL>> and contributors
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 */
namespace PharIo\Manifest;

use InvalidArgumentException;

class NoEmailAddressException extends InvalidArgumentException implements Exception {
}
 * namespaceAliases\PharIo\ManifestphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameManifest * includes * constants * functions
 * classes(\PharIo\Manifest\NoEmailAddressException(phpDocumentor\Descriptor\ClassDescriptor#$+%NoEmailAddressException-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$\InvalidArgumentException%InvalidArgumentException
 * implements\PharIo\Manifest\Exception#$:%	Exception
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums