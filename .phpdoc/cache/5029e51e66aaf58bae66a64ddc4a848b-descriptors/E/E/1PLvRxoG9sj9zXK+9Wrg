1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-107067a2d8935ed5f60dec93333f31be
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameStringEncrypter.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash b15a7798ce3c9360677df194695979b3 * path:vendor/illuminate/contracts/Encryption/StringEncrypter.php	 * sourceQ<?php

namespace Illuminate\Contracts\Encryption;

interface StringEncrypter
{
    /**
     * Encrypt a string without serialization.
     *
     * @param  string  $value
     * @return string
     *
     * @throws \Illuminate\Contracts\Encryption\EncryptException
     */
    public function encryptString(#[\SensitiveParameter] $value);

    /**
     * Decrypt the given string without unserialization.
     *
     * @param  string  $payload
     * @return string
     *
     * @throws \Illuminate\Contracts\Encryption\DecryptException
     */
    public function decryptString($payload);
}
 * namespaceAliases \Illuminate\Contracts\EncryptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Encryption * includes * constants * functions
 * classes
 * interfaces0\Illuminate\Contracts\Encryption\StringEncrypter,phpDocumentor\Descriptor\InterfaceDescriptor#$,%StringEncrypter."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods
encryptString)phpDocumentor\Descriptor\MethodDescriptor#$A\Illuminate\Contracts\Encryption\StringEncrypter::encryptString()%44" 'Encrypt a string without serialization.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type&phpDocumentor\Reflection\Types\String_  * variableNamevaluereturn -phpDocumentor\Descriptor\Tag\ReturnDescriptor>	
 :; throws -phpDocumentor\Descriptor\Tag\ThrowsDescriptor@	
 :&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$1\Illuminate\Contracts\Encryption\EncryptException%EncryptException  /01/01Q 	 * parent" * arguments=+phpDocumentor\Descriptor\ArgumentDescriptor =
 
	"'
  "7"8 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicLM	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
decryptString5#$A\Illuminate\Contracts\Encryption\StringEncrypter::decryptString()%[[" 1Decrypt the given string without unserialization.	

8 98	
 :; <payload> ?>	
 :; @ A@	
 :BC#$1\Illuminate\Contracts\Encryption\DecryptException%DecryptException  /01&/01M F"G^H ^
 
	"M
  "]"^ I"D:"OJ KLMNLMOPQ RSTUVW"CZ   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums