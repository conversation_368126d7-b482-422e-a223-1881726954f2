1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-240527c64532de3df66384e1839e4794
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Bridge.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ae850e1a163083f8086e464f57160d2e * path8vendor/rector/rector/src/Parallel/ValueObject/Bridge.php	 * source<?php

declare (strict_types=1);
namespace Rector\Parallel\ValueObject;

/**
 * @enum
 */
final class Bridge
{
    /**
     * @var string
     */
    public const FILE_DIFFS = 'file_diffs';
    /**
     * @var string
     */
    public const SYSTEM_ERRORS = 'system_errors';
    /**
     * @var string
     */
    public const SYSTEM_ERRORS_COUNT = 'system_errors_count';
    /**
     * @var string
     */
    public const FILES = 'files';
    /**
     * @var string
     */
    public const FILES_COUNT = 'files_count';
}
 * namespaceAliases\Rector\Parallel\ValueObjectphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameValueObject * includes * constants * functions
 * classes#\Rector\Parallel\ValueObject\Bridge(phpDocumentor\Descriptor\ClassDescriptor#$+%Bridge-"
	

enum .	
 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber	/ phpDocumentor\Reflection\Location columnNumber /01   * readOnly * final * abstract
 * methods
 * properties(
FILE_DIFFS+phpDocumentor\Descriptor\ConstantDescriptor#$/\Rector\Parallel\ValueObject\Bridge::FILE_DIFFS%77+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor:	
  * type&phpDocumentor\Reflection\Types\String_  * variableName
  /01 /01   * value'file_diffs'3
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent"<  
SYSTEM_ERRORS8#$2\Rector\Parallel\ValueObject\Bridge::SYSTEM_ERRORS%HH+ 
	

: ;:	
 <= >
  /01 /01  ?'system_errors'3ABC"4F G"<  SYSTEM_ERRORS_COUNT8#$8\Rector\Parallel\ValueObject\Bridge::SYSTEM_ERRORS_COUNT%KK+ 
	

: ;:	
 <= >
  /01 /01  ?'system_errors_count'3ABC"4F G"<  FILES8#$*\Rector\Parallel\ValueObject\Bridge::FILES%NN+ 
	

: ;:	
 <= >
  /01 /01  ?'files'3ABC"4F G"<  FILES_COUNT8#$0\Rector\Parallel\ValueObject\Bridge::FILES_COUNT%QQ+ 
	

: ;:	
 <= >
  /01 /01  ?
'files_count'3ABC"4F G"<  G 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums