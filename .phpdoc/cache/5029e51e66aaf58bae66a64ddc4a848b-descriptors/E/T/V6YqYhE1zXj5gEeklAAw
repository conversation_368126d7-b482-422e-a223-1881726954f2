1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-cfbb3b456c2e0cb65f853e217a6f846f
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameLayerLeave.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash d2e42f88f835fe2f7cb17082e6425071 * pathLvendor/ta-tikoma/phpunit-architecture-test/src/Elements/Layer/LayerLeave.php	 * source@<?php

declare(strict_types=1);

namespace PHPUnit\Architecture\Elements\Layer;

use Closure;
use Exception;
use PHPUnit\Architecture\Elements\ObjectDescription;
use PHPUnit\Architecture\Enums\ObjectType;
use PHPUnit\Architecture\Storage\Filesystem;

trait LayerLeave
{
    abstract public function leave(Closure $closure): Layer;

    public function leaveByPathStart(string $path): Layer
    {
        $path = realpath(Filesystem::getBaseDir() . $path);
        if ($path === false) {
            throw new Exception("Path '{$path}' not found");
        }

        $length = strlen($path);

        return $this->leave(static function (ObjectDescription $objectDescription) use ($path, $length): bool {
            return substr($objectDescription->path, 0, $length) === $path;
        });
    }

    public function leaveByNameStart(string $name): Layer
    {
        $length = strlen($name);

        return $this->leave(static function (ObjectDescription $objectDescription) use ($name, $length): bool {
            return substr($objectDescription->name, 0, $length) === $name;
        });
    }

    public function leaveByNameRegex(string $name): Layer
    {
        return $this->leave(static function (ObjectDescription $objectDescription) use ($name): bool {
            return preg_match($name, $objectDescription->name) === 1;
        });
    }

    public function leaveByType(ObjectType $type): Layer
    {
        return $this->leave(static function (ObjectDescription $objectDescription) use ($type): bool {
            return $objectDescription->type === $type;
        });
    }
}
 * namespaceAliases$\PHPUnit\Architecture\Elements\LayerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameLayer * includes * constants * functions
 * classes
 * interfaces	 * traits/\PHPUnit\Architecture\Elements\Layer\LayerLeave(phpDocumentor\Descriptor\TraitDescriptor#$-%
LayerLeave/"

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber 0152    
 * methodsleave)phpDocumentor\Descriptor\MethodDescriptor#$8\PHPUnit\Architecture\Elements\Layer\LayerLeave::leave()%44" 
	 
param  012012I 	 * parent" * argumentsclosure+phpDocumentor\Descriptor\ArgumentDescriptor :
 
	 
  """# 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\Closure%Closure
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicDE	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType>?#$*\PHPUnit\Architecture\Elements\Layer\Layer%&? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  leaveByPathStart5#$C\PHPUnit\Architecture\Elements\Layer\LayerLeave::leaveByPathStart()%SS" 
	 
7  012P012 8"9path; U
 
	 
  "7"8 <"2=&phpDocumentor\Reflection\Types\String_ B CDEFDEGH>?#$I%&JKLMNO"1R  leaveByNameStart5#$C\PHPUnit\Architecture\Elements\Layer\LayerLeave::leaveByNameStart()%WW" 
	 
7  012#01&2L 8"9name; Y
 
	 
  "J"K <"E=V B CDEFDEGH>?#$I%&JKLMNO"1R  leaveByNameRegex5#$C\PHPUnit\Architecture\Elements\Layer\LayerLeave::leaveByNameRegex()%ZZ" 
	 
7  01(2S01-2L 8"9Y; Y
 
	 
  "]"^ <"X=V B CDEFDEGH>?#$I%&JKLMNO"1R  leaveByType5#$>\PHPUnit\Architecture\Elements\Layer\LayerLeave::leaveByType()%\\" 
	 
7  01/2S0142< 8"9type; ^
 
	 
  "p"q <"k=>?#$&\PHPUnit\Architecture\Enums\ObjectType%
ObjectTypeB CDEFDEGH>?#$I%&JKLMNO"1R  
 * usedTraits 
 * markers. phpDocumentor\Descriptor\FileDescriptor enums