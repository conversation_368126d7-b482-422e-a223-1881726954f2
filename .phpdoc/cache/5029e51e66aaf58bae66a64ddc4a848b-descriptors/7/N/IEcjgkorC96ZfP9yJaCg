1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-26acf502d52db8b0e61cf59b3a2738be
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameDoctrineClass.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash a8f7e8467776885d4bb6cd05983da3e5 * path[vendor/rector/rector/vendor/rector/rector-doctrine/rules/CodeQuality/Enum/DoctrineClass.php	 * source@<?php

declare (strict_types=1);
namespace Rector\Doctrine\CodeQuality\Enum;

/**
 * @deprecated Switch to @see \Rector\Doctrine\Enum\DoctrineClass instead
 * @api
 */
final class DoctrineClass
{
    /**
     * @deprecated BC only
     */
    public const COLLECTION = \Rector\Doctrine\Enum\DoctrineClass::COLLECTION;
}
 * namespaceAliases!\Rector\Doctrine\CodeQuality\EnumphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEnum * includes * constants * functions
 * classes/\Rector\Doctrine\CodeQuality\Enum\DoctrineClass(phpDocumentor\Descriptor\ClassDescriptor#$+%
DoctrineClass-"
	


deprecated 1phpDocumentor\Descriptor\Tag\DeprecatedDescriptor.	:Switch to @see \Rector\Doctrine\Enum\DoctrineClass instead : phpDocumentor\Descriptor\Tag\DeprecatedDescriptor version
api 2	
 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber 345   * readOnly * final * abstract
 * methods
 * properties(
COLLECTION+phpDocumentor\Descriptor\ConstantDescriptor#$;\Rector\Doctrine\CodeQuality\Enum\DoctrineClass::COLLECTION%;;+ 
	

. /.	BC only 1
  345 345   * value/\Rector\Doctrine\Enum\DoctrineClass::COLLECTION7
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent" * type  G 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums