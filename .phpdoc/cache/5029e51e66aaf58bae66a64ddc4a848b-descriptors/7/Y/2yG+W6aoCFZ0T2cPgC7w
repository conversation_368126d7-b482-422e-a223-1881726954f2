1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-72c7465780a557fafea028d40b1a25c4
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameIssueFilter.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 8480ea39901b620c80ef012da06b05e4 * path1vendor/phpunit/phpunit/src/Runner/IssueFilter.php	 * sourcen<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestRunner;

use PHPUnit\Event\Test\DeprecationTriggered;
use PHPUnit\Event\Test\ErrorTriggered;
use PHPUnit\Event\Test\NoticeTriggered;
use PHPUnit\Event\Test\PhpDeprecationTriggered;
use PHPUnit\Event\Test\PhpNoticeTriggered;
use PHPUnit\Event\Test\PhpWarningTriggered;
use PHPUnit\Event\Test\WarningTriggered;
use PHPUnit\TextUI\Configuration\Source;
use PHPUnit\TextUI\Configuration\SourceFilter;

/**
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 *
 * @internal This class is not covered by the backward compatibility promise for PHPUnit
 */
final readonly class IssueFilter
{
    private Source $source;

    public function __construct(Source $source)
    {
        $this->source = $source;
    }

    public function shouldBeProcessed(DeprecationTriggered|ErrorTriggered|NoticeTriggered|PhpDeprecationTriggered|PhpNoticeTriggered|PhpWarningTriggered|WarningTriggered $event, bool $onlyTestMethods = false): bool
    {
        if ($onlyTestMethods && !$event->test()->isTestMethod()) {
            return false;
        }

        if ($event instanceof DeprecationTriggered || $event instanceof PhpDeprecationTriggered) {
            if ($event->ignoredByTest()) {
                return false;
            }

            if ($this->source->ignoreSelfDeprecations() &&
                ($event->trigger()->isTest() || $event->trigger()->isSelf())) {
                return false;
            }

            if ($this->source->ignoreDirectDeprecations() && $event->trigger()->isDirect()) {
                return false;
            }

            if ($this->source->ignoreIndirectDeprecations() && $event->trigger()->isIndirect()) {
                return false;
            }

            if (!$this->source->ignoreSuppressionOfDeprecations() && $event->wasSuppressed()) {
                return false;
            }

            if ($this->source->restrictDeprecations() && !SourceFilter::instance()->includes($event->file())) {
                return false;
            }
        }

        if ($event instanceof NoticeTriggered) {
            if (!$this->source->ignoreSuppressionOfNotices() && $event->wasSuppressed()) {
                return false;
            }

            if ($this->source->restrictNotices() && !SourceFilter::instance()->includes($event->file())) {
                return false;
            }
        }

        if ($event instanceof PhpNoticeTriggered) {
            if (!$this->source->ignoreSuppressionOfPhpNotices() && $event->wasSuppressed()) {
                return false;
            }

            if ($this->source->restrictNotices() && !SourceFilter::instance()->includes($event->file())) {
                return false;
            }
        }

        if ($event instanceof WarningTriggered) {
            if (!$this->source->ignoreSuppressionOfWarnings() && $event->wasSuppressed()) {
                return false;
            }

            if ($this->source->restrictWarnings() && !SourceFilter::instance()->includes($event->file())) {
                return false;
            }
        }

        if ($event instanceof PhpWarningTriggered) {
            if (!$this->source->ignoreSuppressionOfPhpWarnings() && $event->wasSuppressed()) {
                return false;
            }

            if ($this->source->restrictWarnings() && !SourceFilter::instance()->includes($event->file())) {
                return false;
            }
        }

        if ($event instanceof ErrorTriggered) {
            if (!$this->source->ignoreSuppressionOfErrors() && $event->wasSuppressed()) {
                return false;
            }
        }

        return true;
    }
}
 * namespaceAliases\PHPUnit\TestRunnerphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
TestRunner * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums