1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-41c7fc601fc75b27cbff6180fc24033b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name#RegisterLocaleAwareServicesPass.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 51ee9d54002be933ccf78e7f26ea8816 * pathRvendor/symfony/http-kernel/DependencyInjection/RegisterLocaleAwareServicesPass.php	 * sourcep<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\DependencyInjection;

use Symfony\Component\DependencyInjection\Argument\IteratorArgument;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

/**
 * Register all services that have the "kernel.locale_aware" tag into the listener.
 *
 * <AUTHOR> Bobiet <<EMAIL>>
 */
class RegisterLocaleAwareServicesPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container): void
    {
        if (!$container->hasDefinition('locale_aware_listener')) {
            return;
        }

        $services = [];

        foreach ($container->findTaggedServiceIds('kernel.locale_aware') as $id => $tags) {
            $services[] = new Reference($id);
        }

        if (!$services) {
            $container->removeDefinition('locale_aware_listener');

            return;
        }

        $container
            ->getDefinition('locale_aware_listener')
            ->setArgument(0, new IteratorArgument($services))
        ;
    }
}
 * namespaceAliases1\Symfony\Component\HttpKernel\DependencyInjectionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDependencyInjection * includes * constants * functions
 * classesQ\Symfony\Component\HttpKernel\DependencyInjection\RegisterLocaleAwareServicesPass(phpDocumentor\Descriptor\ClassDescriptor#$+%RegisterLocaleAwareServicesPass-"PRegister all services that have the "kernel.locale_aware" tag into the listener.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	&Pierre Bobiet <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 2314   * readOnly * final * abstract
 * methodsprocess)phpDocumentor\Descriptor\MethodDescriptor#$\\Symfony\Component\HttpKernel\DependencyInjection\RegisterLocaleAwareServicesPass::process()%99" 
	 
param  234
2304l 	 * parent" * arguments	container+phpDocumentor\Descriptor\ArgumentDescriptor ?
 
	 
  "'"( 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$7\Symfony\Component\DependencyInjection\ContainerBuilder%ContainerBuilder
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicIJ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Void_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference67
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(= 
 * implementsE\Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface#$X%CompilerPassInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums