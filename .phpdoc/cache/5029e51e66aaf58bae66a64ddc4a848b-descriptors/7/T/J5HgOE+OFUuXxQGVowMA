1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b1fab06ae8d21efe53aa696a53039d7d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameTraversableContainsEqual.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 744d8ceed6a36b2d70ef1f606d91e7e3 * pathXvendor/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsEqual.php	 * source	<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Framework\Constraint;

use SplObjectStorage;

/**
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
final class TraversableContainsEqual extends TraversableContains
{
    /**
     * Evaluates the constraint for parameter $other. Returns true if the
     * constraint is met, false otherwise.
     */
    protected function matches(mixed $other): bool
    {
        if ($other instanceof SplObjectStorage) {
            return $other->contains($this->value());
        }

        foreach ($other as $element) {
            /* @noinspection TypeUnsafeComparisonInspection */
            if ($this->value() == $element) {
                return true;
            }
        }

        return false;
    }
}
 * namespaceAliases\PHPUnit\Framework\ConstraintphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Constraint * includes * constants * functions
 * classes6\PHPUnit\Framework\Constraint\TraversableContainsEqual(phpDocumentor\Descriptor\ClassDescriptor#$+%TraversableContainsEqual-"
	

no-named-arguments .	QParameter names are not covered by the backward compatibility promise for PHPUnit 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 01&2   * readOnly * final * abstract
 * methodsmatches)phpDocumentor\Descriptor\MethodDescriptor#$A\PHPUnit\Framework\Constraint\TraversableContainsEqual::matches()%77" fEvaluates the constraint for parameter $other. Returns true if the
constraint is met, false otherwise.	

param  012z01%2 	 * parent" * argumentsother+phpDocumentor\Descriptor\ArgumentDescriptor >
 
	 
  ")"* 3 phpDocumentor\Descriptor\ArgumentDescriptor method"" * type%phpDocumentor\Reflection\Types\Mixed_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\Boolean ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference45
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'	PROTECTED7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(<#$1\PHPUnit\Framework\Constraint\TraversableContains%TraversableContains
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums