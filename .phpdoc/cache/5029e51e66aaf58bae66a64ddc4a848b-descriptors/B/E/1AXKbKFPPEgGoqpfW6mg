1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-2530640938ba30a74effede352592136
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameHappyEyeBallsConnector.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash fc355acaaaaee4250135b95766cefa53 * pathGvendor/rector/rector/vendor/react/socket/src/HappyEyeBallsConnector.php	 * sourceY<?php

namespace RectorPrefix202507\React\Socket;

use RectorPrefix202507\React\Dns\Resolver\ResolverInterface;
use RectorPrefix202507\React\EventLoop\Loop;
use RectorPrefix202507\React\EventLoop\LoopInterface;
use RectorPrefix202507\React\Promise;
final class HappyEyeBallsConnector implements ConnectorInterface
{
    private $loop;
    private $connector;
    private $resolver;
    /**
     * @param ?LoopInterface $loop
     * @param ConnectorInterface $connector
     * @param ResolverInterface $resolver
     */
    public function __construct($loop = null, $connector = null, $resolver = null)
    {
        // $connector and $resolver arguments are actually required, marked
        // optional for technical reasons only. Nullable $loop without default
        // requires PHP 7.1, null default is also supported in legacy PHP
        // versions, but required parameters are not allowed after arguments
        // with null default. Mark all parameters optional and check accordingly.
        if ($loop !== null && !$loop instanceof LoopInterface) {
            // manual type check to support legacy PHP < 7.1
            throw new \InvalidArgumentException('Argument #1 ($loop) expected null|React\\EventLoop\\LoopInterface');
        }
        if (!$connector instanceof ConnectorInterface) {
            // manual type check to support legacy PHP < 7.1
            throw new \InvalidArgumentException('Argument #2 ($connector) expected React\\Socket\\ConnectorInterface');
        }
        if (!$resolver instanceof ResolverInterface) {
            // manual type check to support legacy PHP < 7.1
            throw new \InvalidArgumentException('Argument #3 ($resolver) expected React\\Dns\\Resolver\\ResolverInterface');
        }
        $this->loop = $loop ?: Loop::get();
        $this->connector = $connector;
        $this->resolver = $resolver;
    }
    public function connect($uri)
    {
        $original = $uri;
        if (\strpos($uri, '://') === \false) {
            $uri = 'tcp://' . $uri;
            $parts = \parse_url($uri);
            if (isset($parts['scheme'])) {
                unset($parts['scheme']);
            }
        } else {
            $parts = \parse_url($uri);
        }
        if (!$parts || !isset($parts['host'])) {
            return Promise\reject(new \InvalidArgumentException('Given URI "' . $original . '" is invalid (EINVAL)', \defined('SOCKET_EINVAL') ? \SOCKET_EINVAL : (\defined('PCNTL_EINVAL') ? \PCNTL_EINVAL : 22)));
        }
        $host = \trim($parts['host'], '[]');
        // skip DNS lookup / URI manipulation if this URI already contains an IP
        if (@\inet_pton($host) !== \false) {
            return $this->connector->connect($original);
        }
        $builder = new HappyEyeBallsConnectionBuilder($this->loop, $this->connector, $this->resolver, $uri, $host, $parts);
        return $builder->connect();
    }
}
 * namespaceAliases \RectorPrefix202507\React\SocketphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSocket * includes * constants * functions
 * classes7\RectorPrefix202507\React\Socket\HappyEyeBallsConnector(phpDocumentor\Descriptor\ClassDescriptor#$+%HappyEyeBallsConnector-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber	/ phpDocumentor\Reflection\Location columnNumber ./A0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$F\RectorPrefix202507\React\Socket\HappyEyeBallsConnector::__construct()%55" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type'phpDocumentor\Reflection\Types\Nullable1 phpDocumentor\Reflection\Types\Nullable realType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$1\RectorPrefix202507\React\EventLoop\LoopInterface%
LoopInterface * variableNameloop98	
 :=>#$3\RectorPrefix202507\React\Socket\ConnectorInterface%ConnectorInterfaceA	connector98	
 :=>#$8\RectorPrefix202507\React\Dns\Resolver\ResolverInterface%ResolverInterfaceAresolver  ./0./)0Q 	 * parent" * argumentsB+phpDocumentor\Descriptor\ArgumentDescriptor B
 
	"$
  "3"4 3 phpDocumentor\Descriptor\ArgumentDescriptor method":"&
 * defaultnull * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicPQEK E
 
	"*
  "3"4 L":",MNOPQRPQHK H
 
	"/
  "3"4 L":"1MNOPQRPQ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  connect6#$B\RectorPrefix202507\React\Socket\HappyEyeBallsConnector::connect()%]]" 
	 
8  ./*0W./@0U I"JuriK _
 
	 
  "O"P L"J:U M OPQRPQSTU V23WXY"I\  
 * propertiesB+phpDocumentor\Descriptor\PropertyDescriptor#$>\RectorPrefix202507\React\Socket\HappyEyeBallsConnector::$loop%BB+ 
	 
var  ./0 ./0  I"S5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualWXYZ'PRIVATE\ : M  Ea#$C\RectorPrefix202507\React\Socket\HappyEyeBallsConnector::$connector%EE+ 
	 
c  ./0 ./0  I"SdefgWXY"h\ : M  Ha#$B\RectorPrefix202507\React\Socket\HappyEyeBallsConnector::$resolver%HH+ 
	 
c  ./
0 ./
0  I"SdefgWXY"h\ : M  (I 
 * implementsC#$C%D
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums