1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-649eb5256661fa84b41fb801b3b3b2c2
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameShouldBeUnique.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 7eeba130db78426d682054a8e18223f1 * pathPvendor/rector/rector/vendor/illuminate/contracts/Broadcasting/ShouldBeUnique.php	 * sourcel<?php

namespace RectorPrefix202507\Illuminate\Contracts\Broadcasting;

interface ShouldBeUnique
{
    //
}
 * namespaceAliases5\RectorPrefix202507\Illuminate\Contracts\BroadcastingphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameBroadcasting * includes * constants * functions
 * classes
 * interfacesD\RectorPrefix202507\Illuminate\Contracts\Broadcasting\ShouldBeUnique,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ShouldBeUnique."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums