1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-da481fdb0072e5d3adfd9c8980763943
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameArgumentListMatcher.php * namespace

 * packageApplication
 * summary"Mockery (https://docs.mockery.io/) * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate
3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * items	copyright &phpDocumentor\Descriptor\TagDescriptor

9https://github.com/mockery/mockery/blob/HEAD/COPYRIGHT.md license 

Ihttps://github.com/mockery/mockery/blob/HEAD/LICENSE BSD 3-Clause License link +phpDocumentor\Descriptor\Tag\LinkDescriptor

#for the canonical source repository  * link"https://github.com/mockery/mockerypackage 

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 553c81709f0132ffa478deb27a451e30 * pathFvendor/mockery/mockery/library/Mockery/Matcher/ArgumentListMatcher.php	 * sourcee<?php

/**
 * Mockery (https://docs.mockery.io/)
 *
 * @copyright https://github.com/mockery/mockery/blob/HEAD/COPYRIGHT.md
 * @license https://github.com/mockery/mockery/blob/HEAD/LICENSE BSD 3-Clause License
 * @link https://github.com/mockery/mockery for the canonical source repository
 */

namespace Mockery\Matcher;

interface ArgumentListMatcher
{
}
 * namespaceAliases\Mockery\MatcherphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen,$ phpDocumentor\Reflection\Fqsen nameMatcher * includes * constants * functions
 * classes
 * interfaces$\Mockery\Matcher\ArgumentListMatcher,phpDocumentor\Descriptor\InterfaceDescriptor-.6/ArgumentListMatcher8,


 ""   !!phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber "9:; #$ 
 * parents2
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums