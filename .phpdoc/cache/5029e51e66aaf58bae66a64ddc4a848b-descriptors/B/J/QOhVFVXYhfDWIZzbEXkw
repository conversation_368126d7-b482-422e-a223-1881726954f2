1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-67b70330c4cc672f8d61cc4b05806836
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameeditor.blade.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash f19fe89a1eb8ba31123ab2cfd0b2e64b * pathrvendor/laravel-zero/foundation/src/Illuminate/Foundation/resources/exceptions/renderer/components/editor.blade.php	 * source@foreach ($exception->frames() as $frame)
    <div
        class="sm:col-span-2"
        x-show="index === {{ $loop->index }}"
    >
        <div class="mb-3">
            <div class="text-md text-gray-500 dark:text-gray-400">
                <div class="mb-2">

                    @if (config('app.editor'))
                        <a href="{{ $frame->editorHref() }}" class="text-blue-500 hover:underline">
                            <span class="wrap text-gray-900 dark:text-gray-300">{{ $frame->file() }}</span>
                        </a>
                    @else
                        <span class="wrap text-gray-900 dark:text-gray-300">{{ $frame->file() }}</span>
                    @endif

                    <span class="font-mono text-xs">:{{ $frame->line() }}</span>
                </div>
            </div>
        </div>
        <div class="pt-4 text-sm text-gray-500 dark:text-gray-400">
            <pre class="h-[32.5rem] rounded-md dark:bg-gray-800 border dark:border-gray-700"><template x-if="true"><code
                    style="display: none;"
                    id="frame-{{ $loop->index }}"
                    class="language-php highlightable-code @if($loop->index === $exception->defaultFrame()) default-highlightable-code @endif scrollbar-hidden overflow-y-hidden"
                    data-line-number="{{ $frame->line() }}"
                    data-ln-start-from="{{ max($frame->line() - 5, 1) }}"
                >{{ $frame->snippet() }}</code></template></pre>
        </div>
    </div>
@endforeach
 * namespaceAliases * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums