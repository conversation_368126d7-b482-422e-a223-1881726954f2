1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-78ddc3e2c4823e6c2942b49e521775f8
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameBitwiseXorToBitwiseAnd.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 1a3eecf6a5593b527981b459cec59a7d * pathTvendor/pestphp/pest-plugin-mutate/src/Mutators/Assignment/BitwiseXorToBitwiseAnd.php	 * source<?php

declare(strict_types=1);

namespace Pest\Mutate\Mutators\Assignment;

use Pest\Mutate\Mutators\Abstract\AbstractMutator;
use PhpParser\Node;
use PhpParser\Node\Expr\AssignOp\BitwiseAnd;
use PhpParser\Node\Expr\AssignOp\BitwiseXor;

class BitwiseXorToBitwiseAnd extends AbstractMutator
{
    public const SET = 'Assignment';

    public const DESCRIPTION = 'Replaces `^=` with `&=`.';

    public const DIFF = <<<'DIFF'
        $a ^= $b;  // [tl! remove]
        $a &= $b;  // [tl! add]
        DIFF;

    public static function nodesToHandle(): array
    {
        return [BitwiseXor::class];
    }

    public static function mutate(Node $node): Node
    {
        /** @var BitwiseXor $node */
        return new BitwiseAnd($node->var, $node->expr, $node->getAttributes());
    }
}
 * namespaceAliases \Pest\Mutate\Mutators\AssignmentphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Assignment * includes * constants * functions
 * classes7\Pest\Mutate\Mutators\Assignment\BitwiseXorToBitwiseAnd(phpDocumentor\Descriptor\ClassDescriptor#$+%BitwiseXorToBitwiseAnd-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./!0   * readOnly * final * abstract
 * methods
nodesToHandle)phpDocumentor\Descriptor\MethodDescriptor#$H\Pest\Mutate\Mutators\Assignment\BitwiseXorToBitwiseAnd::nodesToHandle()%55" 
	 
  ./0 ./0\ 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  mutate6#$A\Pest\Mutate\Mutators\Assignment\BitwiseXorToBitwiseAnd::mutate()%NN" 
	 
param  ./0c./ 0 8"9node+phpDocumentor\Descriptor\ArgumentDescriptor Q
 
	 
  "/"0 3 phpDocumentor\Descriptor\ArgumentDescriptor method"* * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\PhpParser\Node%Node
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadic[\:;UV#$W%XG23HIJ")M  
 * properties(SET+phpDocumentor\Descriptor\ConstantDescriptor#$<\Pest\Mutate\Mutators\Assignment\BitwiseXorToBitwiseAnd::SET%__+ 
	 
  ./0 ./0   * value'Assignment'2HIJ")M 8"T  DESCRIPTION`#$D\Pest\Mutate\Mutators\Assignment\BitwiseXorToBitwiseAnd::DESCRIPTION%dd+ 
	 
  ./0 ./0  b'Replaces `^=` with `&=`.'2HIJ")M 8"T  DIFF`#$=\Pest\Mutate\Mutators\Assignment\BitwiseXorToBitwiseAnd::DIFF%gg+ 
	 
  ./0 ./0  bA<<<'DIFF'
$a ^= $b;  // [tl! remove]
$a &= $b;  // [tl! add]
DIFF2HIJ")M 8"T  8#$.\Pest\Mutate\Mutators\Abstract\AbstractMutator%AbstractMutator
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums