1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-148f22b6e809c08a8c958154e3745ced
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePrunableBatchRepository.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash edb759f01d950481bf9ef2fdb73c2d6f * path1vendor/illuminate/bus/PrunableBatchRepository.php	 * source><?php

namespace Illuminate\Bus;

use DateTimeInterface;

interface PrunableBatchRepository extends BatchRepository
{
    /**
     * Prune all of the entries older than the given date.
     *
     * @param  \DateTimeInterface  $before
     * @return int
     */
    public function prune(DateTimeInterface $before);
}
 * namespaceAliases\Illuminate\BusphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameBus * includes * constants * functions
 * classes
 * interfaces'\Illuminate\Bus\PrunableBatchRepository,phpDocumentor\Descriptor\InterfaceDescriptor#$,%PrunableBatchRepository."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents\Illuminate\Bus\BatchRepository#$3%BatchRepository(
 * methodsprune)phpDocumentor\Descriptor\MethodDescriptor#$0\Illuminate\Bus\PrunableBatchRepository::prune()%66" 3Prune all of the entries older than the given date.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor:	
  * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\DateTimeInterface%DateTimeInterface * variableNamebeforereturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorC	
 <&phpDocumentor\Reflection\Types\Integer   /01
/01: 	 * parent" * argumentsB+phpDocumentor\Descriptor\ArgumentDescriptor B
 
	")
  "3"4 3 phpDocumentor\Descriptor\ArgumentDescriptor method" <"+
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicLM	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums