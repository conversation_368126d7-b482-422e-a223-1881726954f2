1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-d22f016f435e72880885b0f61e6e9b71
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePasswordBrokerFactory.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash bf23cc208e4d6273fb15828544864489 * pathOvendor/rector/rector/vendor/illuminate/contracts/Auth/PasswordBrokerFactory.php	 * source/<?php

namespace RectorPrefix202507\Illuminate\Contracts\Auth;

interface PasswordBrokerFactory
{
    /**
     * Get a password broker instance by name.
     *
     * @param  string|null  $name
     * @return \Illuminate\Contracts\Auth\PasswordBroker
     */
    public function broker($name = null);
}
 * namespaceAliases-\RectorPrefix202507\Illuminate\Contracts\AuthphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameAuth * includes * constants * functions
 * classes
 * interfacesC\RectorPrefix202507\Illuminate\Contracts\Auth\PasswordBrokerFactory,phpDocumentor\Descriptor\InterfaceDescriptor#$,%PasswordBrokerFactory."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methodsbroker)phpDocumentor\Descriptor\MethodDescriptor#$M\RectorPrefix202507\Illuminate\Contracts\Auth\PasswordBrokerFactory::broker()%44" 'Get a password broker instance by name.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ $phpDocumentor\Reflection\Types\Null_ 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNamenamereturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorC	
 :&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$)\Illuminate\Contracts\Auth\PasswordBroker%PasswordBroker  /0
1/0
1+ 	 * parent" * argumentsB+phpDocumentor\Descriptor\ArgumentDescriptor B
 
	"'
  "4"5 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * defaultnull * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicPQ	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums