1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-269d277c1249ae68aa1ad7a293e009a4
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Attribute.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 9c9dc959934920965a3091740e318505 * path2vendor/phpstan/phpdoc-parser/src/Ast/Attribute.php	 * sourceU<?php declare(strict_types = 1);

namespace PHPStan\PhpDocParser\Ast;

final class Attribute
{

	public const START_LINE = 'startLine';
	public const END_LINE = 'endLine';

	public const START_INDEX = 'startIndex';
	public const END_INDEX = 'endIndex';

	public const ORIGINAL_NODE = 'originalNode';

	public const COMMENTS = 'comments';

}
 * namespaceAliases\PHPStan\PhpDocParser\AstphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameAst * includes * constants * functions
 * classes#\PHPStan\PhpDocParser\Ast\Attribute(phpDocumentor\Descriptor\ClassDescriptor#$+%	Attribute-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(
START_LINE+phpDocumentor\Descriptor\ConstantDescriptor#$/\PHPStan\PhpDocParser\Ast\Attribute::START_LINE%66+ 
	 
  ./0 ./0   * value'startLine'2
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent" * type  END_LINE7#$-\PHPStan\PhpDocParser\Ast\Attribute::END_LINE%CC+ 
	 
  ./	0 ./	0  9	'endLine'2;<="$@ A"B  START_INDEX7#$0\PHPStan\PhpDocParser\Ast\Attribute::START_INDEX%FF+ 
	 
  ./0 ./0  9'startIndex'2;<="$@ A"B  	END_INDEX7#$.\PHPStan\PhpDocParser\Ast\Attribute::END_INDEX%II+ 
	 
  ./0 ./0  9
'endIndex'2;<="$@ A"B  
ORIGINAL_NODE7#$2\PHPStan\PhpDocParser\Ast\Attribute::ORIGINAL_NODE%LL+ 
	 
  ./0 ./0  9'originalNode'2;<="$@ A"B  COMMENTS7#$-\PHPStan\PhpDocParser\Ast\Attribute::COMMENTS%OO+ 
	 
  ./0 ./0  9
'comments'2;<="$@ A"B  A 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums