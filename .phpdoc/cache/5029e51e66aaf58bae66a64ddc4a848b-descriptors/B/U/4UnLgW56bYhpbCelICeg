1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-55f0baa21bff12ed5e9dc1a064aba784
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameEnvironment.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 00f3b8de23ea308e40642e21a6d3839b * path/vendor/pestphp/pest/src/Plugins/Environment.php	 * source<?php

declare(strict_types=1);

namespace Pest\Plugins;

use Pest\Contracts\Plugins\HandlesArguments;

/**
 * @internal
 */
final class Environment implements HandlesArguments
{
    /**
     * The continuous integration environment.
     */
    public const CI = 'ci';

    /**
     * The local environment.
     */
    public const LOCAL = 'local';

    /**
     * The current environment.
     */
    private static ?string $name = null;

    /**
     * {@inheritdoc}
     */
    public function handleArguments(array $arguments): array
    {
        foreach ($arguments as $index => $argument) {
            if ($argument === '--ci') {
                unset($arguments[$index]);

                self::$name = self::CI;
            }
        }

        return array_values($arguments);
    }

    /**
     * Gets the environment name.
     */
    public static function name(?string $name = null): string
    {
        if (is_string($name)) {
            self::$name = $name;
        }

        return self::$name ?? self::LOCAL;
    }
}
 * namespaceAliases
\Pest\PluginsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen namePlugins * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums