1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-f7d9435adaa9347f910c4c842cd1d1b2
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name	Rules.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 3086c82185dc6e8bf2769c6f2a9a9146 * path]vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Spanish/Rules.php	 * sourcem<?php

declare (strict_types=1);
namespace RectorPrefix202507\Doctrine\Inflector\Rules\Spanish;

use RectorPrefix202507\Doctrine\Inflector\Rules\Patterns;
use RectorPrefix202507\Doctrine\Inflector\Rules\Ruleset;
use RectorPrefix202507\Doctrine\Inflector\Rules\Substitutions;
use RectorPrefix202507\Doctrine\Inflector\Rules\Transformations;
final class Rules
{
    public static function getSingularRuleset() : Ruleset
    {
        return new Ruleset(new Transformations(...Inflectible::getSingular()), new Patterns(...Uninflected::getSingular()), (new Substitutions(...Inflectible::getIrregular()))->getFlippedSubstitutions());
    }
    public static function getPluralRuleset() : Ruleset
    {
        return new Ruleset(new Transformations(...Inflectible::getPlural()), new Patterns(...Uninflected::getPlural()), new Substitutions(...Inflectible::getIrregular()));
    }
}
 * namespaceAliases4\RectorPrefix202507\Doctrine\Inflector\Rules\SpanishphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSpanish * includes * constants * functions
 * classes:\RectorPrefix202507\Doctrine\Inflector\Rules\Spanish\Rules(phpDocumentor\Descriptor\ClassDescriptor#$+%Rules-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methodsgetSingularRuleset)phpDocumentor\Descriptor\MethodDescriptor#$P\RectorPrefix202507\Doctrine\Inflector\Rules\Spanish\Rules::getSingularRuleset()%55" 
	 
  ./0l./0y 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$4\RectorPrefix202507\Doctrine\Inflector\Rules\Ruleset%Ruleset? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  getPluralRuleset6#$N\RectorPrefix202507\Doctrine\Inflector\Rules\Spanish\Rules::getPluralRuleset()%GG" 
	 
  ./0./0i 8"9:;<=#$>%?@23ABC"%F  
 * properties(8 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums