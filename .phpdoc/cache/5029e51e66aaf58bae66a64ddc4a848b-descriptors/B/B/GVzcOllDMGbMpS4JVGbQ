1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-17b5a89ac1048903abed5be67d78ccd8
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePropertyProperty.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 0b7af1df766f4f5ee2ee33c1019a77a3 * pathYvendor/rector/rector/vendor/nikic/php-parser/lib/PhpParser/Node/Stmt/PropertyProperty.php	 * source<?php

declare (strict_types=1);
namespace PhpParser\Node\Stmt;

use PhpParser\Node\PropertyItem;
require __DIR__ . '/../PropertyItem.php';
if (\false) {
    // For classmap-authoritative support.
    class PropertyProperty extends PropertyItem
    {
    }
}
 * namespaceAliases\PhpParser\Node\StmtphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameStmt * includes * constants * functions
 * classes%\PhpParser\Node\Stmt\PropertyProperty(phpDocumentor\Descriptor\ClassDescriptor#$+%PropertyProperty-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$\PhpParser\Node\PropertyItem%PropertyItem
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums