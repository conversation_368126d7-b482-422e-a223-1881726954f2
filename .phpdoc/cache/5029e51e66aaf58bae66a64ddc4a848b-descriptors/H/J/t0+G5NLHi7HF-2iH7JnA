1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-9f81884a5bd6a0edd7e32ea9ae9fa6d4
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameResponsable.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 7813b63d7bdf86b6106193539c13bd54 * pathHvendor/rector/rector/vendor/illuminate/contracts/Support/Responsable.php	 * sourceE<?php

namespace RectorPrefix202507\Illuminate\Contracts\Support;

interface Responsable
{
    /**
     * Create an HTTP response that represents the object.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function toResponse($request);
}
 * namespaceAliases0\RectorPrefix202507\Illuminate\Contracts\SupportphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSupport * includes * constants * functions
 * classes
 * interfaces<\RectorPrefix202507\Illuminate\Contracts\Support\Responsable,phpDocumentor\Descriptor\InterfaceDescriptor#$,%Responsable."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methods
toResponse)phpDocumentor\Descriptor\MethodDescriptor#$J\RectorPrefix202507\Illuminate\Contracts\Support\Responsable::toResponse()%44" 3Create an HTTP response that represents the object.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\Illuminate\Http\Request%Request * variableNamerequestreturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorA	
 :;<#$*\Symfony\Component\HttpFoundation\Response%Response  /0
1/0
1A 	 * parent" * arguments@+phpDocumentor\Descriptor\ArgumentDescriptor @
 
	"'
  "2"3 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicKL	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums