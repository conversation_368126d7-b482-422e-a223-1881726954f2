1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-050eb2129a582f68b1c57c646e5be9b5
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameDateRangeError.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash eca57a954b241a12a4de3033a47a98fc * path@vendor/symfony/polyfill-php83/Resources/stubs/DateRangeError.php	 * sourceF<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

if (\PHP_VERSION_ID < 80300) {
    class DateRangeError extends DateError
    {
    }
}
 * namespaceAliases * includes * constants * functions
 * classes\DateRangeError(phpDocumentor\Descriptor\ClassDescriptorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen&$ phpDocumentor\Reflection\Fqsen nameDateRangeError+

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ,-.   * readOnly * final * abstract
 * methods
 * properties#	 * parent()
\DateError*	DateError
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums