1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-f3a3841a3dfd284698df4a42cdfcf8f1
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Constants.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 3bfe7d3312441af4eb9752f7e60bdf32 * path8vendor/rector/rector/stubs-rector/Internal/Constants.php	 * source^<?php

if (PHP_VERSION_ID < 80100) {
    if (! defined('MHASH_XXH32')) {
        define('MHASH_XXH32', 38);
    }

    if (! defined('MHASH_XXH64')) {
        define('MHASH_XXH64', 39);
    }

    if (! defined('MHASH_XXH3')) {
        define('MHASH_XXH3', 40);
    }

    if (! defined('MHASH_XXH128')) {
        define('MHASH_XXH128', 41);
    }
}
 * namespaceAliases * includes * constants\MHASH_XXH32+phpDocumentor\Descriptor\ConstantDescriptorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen$$ phpDocumentor\Reflection\Fqsen nameMHASH_XXH32)
 
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber *+,   * value38 * final
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent  * type  \MHASH_XXH64%&'8(MHASH_XXH649
 
	 

""  *+	, *+	,  -39/012"5 6 7  \MHASH_XXH3%&';(
MHASH_XXH3<
 
	 

""  *+
, *+
,  -40/012"5 6 7  
\MHASH_XXH128%&'>(MHASH_XXH128?
 
	 

""  *+, *+,  -41/012"5 6 7   * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums