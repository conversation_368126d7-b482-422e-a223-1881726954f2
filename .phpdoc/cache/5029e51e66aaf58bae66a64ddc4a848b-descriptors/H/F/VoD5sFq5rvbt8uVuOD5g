1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0b48a8b07e23f44abc4d90b57e38238e
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameSubstitutions.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 34dfa4837b3a9c335999566fbfca49ba * path]vendor/rector/rector/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Substitutions.php	 * sourceJ<?php

declare (strict_types=1);
namespace RectorPrefix202507\Doctrine\Inflector\Rules;

use RectorPrefix202507\Doctrine\Inflector\WordInflector;
use function strtolower;
use function strtoupper;
use function substr;
class Substitutions implements WordInflector
{
    /** @var Substitution[] */
    private $substitutions;
    public function __construct(Substitution ...$substitutions)
    {
        foreach ($substitutions as $substitution) {
            $this->substitutions[$substitution->getFrom()->getWord()] = $substitution;
        }
    }
    public function getFlippedSubstitutions() : Substitutions
    {
        $substitutions = [];
        foreach ($this->substitutions as $substitution) {
            $substitutions[] = new Substitution($substitution->getTo(), $substitution->getFrom());
        }
        return new Substitutions(...$substitutions);
    }
    public function inflect(string $word) : string
    {
        $lowerWord = strtolower($word);
        if (isset($this->substitutions[$lowerWord])) {
            $firstLetterUppercase = $lowerWord[0] !== $word[0];
            $toWord = $this->substitutions[$lowerWord]->getTo()->getWord();
            if ($firstLetterUppercase) {
                return strtoupper($toWord[0]) . substr($toWord, 1);
            }
            return $toWord;
        }
        return $word;
    }
}
 * namespaceAliases,\RectorPrefix202507\Doctrine\Inflector\RulesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRules * includes * constants * functions
 * classes:\RectorPrefix202507\Doctrine\Inflector\Rules\Substitutions(phpDocumentor\Descriptor\ClassDescriptor#$+%
Substitutions-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ./)0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$I\RectorPrefix202507\Doctrine\Inflector\Rules\Substitutions::__construct()%55" 
	 
param  ./0G./0" 	 * parent" * arguments
substitutions+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$9\RectorPrefix202507\Doctrine\Inflector\Rules\Substitution%Substitution
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  getFlippedSubstitutions6#$U\RectorPrefix202507\Doctrine\Inflector\Rules\Substitutions::getFlippedSubstitutions()%RR" 
	 
  ./0(./0e 9":HI?@#$+%-K23LMN".Q  inflect6#$E\RectorPrefix202507\Doctrine\Inflector\Rules\Substitutions::inflect()%TT" 
	 
8  ./0k./(0F 9":word< V
 
	 
  ">"? ="9>&phpDocumentor\Reflection\Types\String_ C DEFGEFHIW K23LMN".Q  
 * properties;+phpDocumentor\Descriptor\PropertyDescriptor#$J\RectorPrefix202507\Doctrine\Inflector\Rules\Substitutions::$substitutions%;;+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptor[	
 >%phpDocumentor\Reflection\Types\Array_ * valueType?@#$A%B
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types W &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableName
  ./
0 ./
0  9"H5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualLMNO'PRIVATEQ >"XC  (9 
 * implements4\RectorPrefix202507\Doctrine\Inflector\WordInflector#$m%
WordInflector
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums