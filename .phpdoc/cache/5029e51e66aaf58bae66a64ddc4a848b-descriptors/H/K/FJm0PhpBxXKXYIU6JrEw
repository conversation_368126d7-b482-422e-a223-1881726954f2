1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-cb33eded95458b19c955354af39af6ea
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameUseEloquentBuilder.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash e774b161179cf24466322acaeb121216 * pathEvendor/illuminate/database/Eloquent/Attributes/UseEloquentBuilder.php	 * sourceh<?php

namespace Illuminate\Database\Eloquent\Attributes;

use Attribute;

#[Attribute(Attribute::TARGET_CLASS)]
class UseEloquentBuilder
{
    /**
     * Create a new attribute instance.
     *
     * @param  class-string<\Illuminate\Database\Eloquent\Builder>  $builderClass
     */
    public function __construct(public string $builderClass)
    {
    }
}
 * namespaceAliases(\Illuminate\Database\Eloquent\AttributesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Attributes * includes * constants * functions
 * classes;\Illuminate\Database\Eloquent\Attributes\UseEloquentBuilder(phpDocumentor\Descriptor\ClassDescriptor#$+%UseEloquentBuilder-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$J\Illuminate\Database\Eloquent\Attributes\UseEloquentBuilder::__construct()%55"  Create a new attribute instance.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor9	
  * type*phpDocumentor\Reflection\Types\ClassString1 phpDocumentor\Reflection\Types\ClassString fqsen#$%\Illuminate\Database\Eloquent\Builder%Builder * variableNamebuilderClass  ./0!./0d 	 * parent" * argumentsA+phpDocumentor\Descriptor\ArgumentDescriptor A
 
	"$
  "(") 3 phpDocumentor\Descriptor\ArgumentDescriptor method";"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicHI	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * propertiesA+phpDocumentor\Descriptor\PropertyDescriptor#$J\Illuminate\Database\Eloquent\Attributes\UseEloquentBuilder::$builderClass%AA+ 
	 
var  ./0=./0W B"K5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualOPQ"4T ;&phpDocumentor\Reflection\Types\String_ F  (B 
 * implements
 * usedTraits
 * attributes ,phpDocumentor\Descriptor\AttributeDescriptor7 phpDocumentor\Descriptor\AttributeDescriptor arguments 2phpDocumentor\Descriptor\ValueObjects\CallArgument9 phpDocumentor\Descriptor\ValueObjects\CallArgument value\Attribute::TARGET_CLASS8 phpDocumentor\Descriptor\ValueObjects\CallArgument name < phpDocumentor\Descriptor\AttributeDescriptor attributeClass 	Attribute	 #$
\Attribute%h
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums