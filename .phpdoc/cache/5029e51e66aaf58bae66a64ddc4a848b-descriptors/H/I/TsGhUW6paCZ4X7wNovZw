**********
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-9e268eaaf213a769cf1a654fe7ae4c76
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name'Php83GarbageCollectorStatusProvider.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash dfd501f419eed6db604f6b159576329e * pathXvendor/phpunit/phpunit/src/Event/Value/Telemetry/Php83GarbageCollectorStatusProvider.php	 * sourcei<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Event\Telemetry;

use function gc_status;

/**
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 *
 * @internal This class is not covered by the backward compatibility promise for PHPUnit
 */
final readonly class Php83GarbageCollectorStatusProvider implements GarbageCollectorStatusProvider
{
    public function status(): GarbageCollectorStatus
    {
        $status = gc_status();

        return new GarbageCollectorStatus(
            $status['runs'],
            $status['collected'],
            $status['threshold'],
            $status['roots'],
            /** @phpstan-ignore offsetAccess.notFound */
            $status['application_time'],
            /** @phpstan-ignore offsetAccess.notFound */
            $status['collector_time'],
            /** @phpstan-ignore offsetAccess.notFound */
            $status['destructor_time'],
            /** @phpstan-ignore offsetAccess.notFound */
            $status['free_time'],
            /** @phpstan-ignore offsetAccess.notFound */
            $status['running'],
            /** @phpstan-ignore offsetAccess.notFound */
            $status['protected'],
            /** @phpstan-ignore offsetAccess.notFound */
            $status['full'],
            /** @phpstan-ignore offsetAccess.notFound */
            $status['buffer_size'],
        );
    }
}
 * namespaceAliases\PHPUnit\Event\TelemetryphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Telemetry * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums