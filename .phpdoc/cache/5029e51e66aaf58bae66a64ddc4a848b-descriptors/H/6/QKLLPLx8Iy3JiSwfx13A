1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-4976909dcd9aecc74b668861ef9f7b13
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameAssertCallFactory.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 3c99a97ee1a4628fd9fb00b402b3f725 * pathWvendor/rector/rector/vendor/rector/rector-phpunit/src/NodeFactory/AssertCallFactory.php	 * sourceL<?php

declare (strict_types=1);
namespace Rector\PHPUnit\NodeFactory;

use PhpParser\Node\Expr\MethodCall;
use PhpParser\Node\Expr\StaticCall;
final class AssertCallFactory
{
    /**
     * @param \PhpParser\Node\Expr\StaticCall|\PhpParser\Node\Expr\MethodCall $node
     * @return \PhpParser\Node\Expr\StaticCall|\PhpParser\Node\Expr\MethodCall
     */
    public function createCallWithName($node, string $name)
    {
        if ($node instanceof MethodCall) {
            return new MethodCall($node->var, $name);
        }
        return new StaticCall($node->class, $name);
    }
}
 * namespaceAliases\Rector\PHPUnit\NodeFactoryphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameNodeFactory * includes * constants * functions
 * classes-\Rector\PHPUnit\NodeFactory\AssertCallFactory(phpDocumentor\Descriptor\ClassDescriptor#$+%AssertCallFactory-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methodscreateCallWithName)phpDocumentor\Descriptor\MethodDescriptor#$C\Rector\PHPUnit\NodeFactory\AssertCallFactory::createCallWithName()%55" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\PhpParser\Node\Expr\StaticCall%
StaticCall=>#$\PhpParser\Node\Expr\MethodCall%
MethodCall4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNamenodereturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorG	
 :;< =>#$?%@=>#$A%BCD  ./0g./0H 	 * parent" * argumentsF+phpDocumentor\Descriptor\ArgumentDescriptor F
 
	"$
  "7"8 3 phpDocumentor\Descriptor\ArgumentDescriptor method":"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicOPnameK R
 
	 
  "7"8 L":&phpDocumentor\Reflection\Types\String_ M NOPQOP	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(I 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums