1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-9004fa022311c5fe7973cf9bc4c7c086
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Connector.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 366b392add71bbb8600ce3ee2d77a4a0 * path/vendor/illuminate/contracts/Redis/Connector.php	 * source|<?php

namespace Illuminate\Contracts\Redis;

interface Connector
{
    /**
     * Create a connection to a Redis cluster.
     *
     * @param  array  $config
     * @param  array  $options
     * @return \Illuminate\Redis\Connections\Connection
     */
    public function connect(array $config, array $options);

    /**
     * Create a connection to a Redis instance.
     *
     * @param  array  $config
     * @param  array  $clusterOptions
     * @param  array  $options
     * @return \Illuminate\Redis\Connections\Connection
     */
    public function connectToCluster(array $config, array $clusterOptions, array $options);
}
 * namespaceAliases\Illuminate\Contracts\RedisphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRedis * includes * constants * functions
 * classes
 * interfaces%\Illuminate\Contracts\Redis\Connector,phpDocumentor\Descriptor\InterfaceDescriptor#$,%	Connector."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methodsconnect)phpDocumentor\Descriptor\MethodDescriptor#$0\Illuminate\Contracts\Redis\Connector::connect()%44" 'Create a connection to a Redis cluster.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNameconfig98	
 :;<= > ?@A B C DEFoptionsreturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorI	
 :&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$(\Illuminate\Redis\Connections\Connection%
Connection  /01/019 	 * parent" * argumentsG+phpDocumentor\Descriptor\ArgumentDescriptor G
 
	"'
  "?"@ 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicUVHQ H
 
	"0
  "?"@ R":"2S TUVWUV	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType= ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  connectToCluster5#$9\Illuminate\Contracts\Redis\Connector::connectToCluster()%cc" (Create a connection to a Redis instance.	

8 98	
 :;<= > ?@A B C DEFG98	
 :;<= > ?@A B C DEFclusterOptions98	
 :;<= > ?@A B C DEFHI JI	
 :KL#$M%N  /01"/01x O"PGQ G
 
	"Z
  "{"| R"Q:"\S TUVWUVfQ f
 
	"c
  "{"| R"Q:"eS TUVWUVHQ H
 
	"l
  "{"| R"Q:"nS TUVWUVXY= Z[\]^_"Pb   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums