1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-9d2767a01ed0b31f9e994c372c30b16c
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name"EnsureConfigurationIsAvailable.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 10bbe778bd32892647b00af1d30d50cc * pathFvendor/pestphp/pest/src/Subscribers/EnsureConfigurationIsAvailable.php	 * source
<?php

declare(strict_types=1);

namespace Pest\Subscribers;

use Pest\Support\Container;
use PHPUnit\Event\TestRunner\Configured;
use PHPUnit\Event\TestRunner\ConfiguredSubscriber;
use PHPUnit\TextUI\Configuration\Configuration;

/**
 * @internal
 */
final class EnsureConfigurationIsAvailable implements ConfiguredSubscriber
{
    /**
     * Runs the subscriber.
     */
    public function notify(Configured $event): void
    {
        Container::getInstance()->add(Configuration::class, $event->configuration());
    }
}
 * namespaceAliases\Pest\SubscribersphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSubscribers * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums