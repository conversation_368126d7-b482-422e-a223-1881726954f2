1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-99f477972e555e4d04af7c38d99d21c9
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePest.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash e1347e464a75aa673fa90a7c04fd2a13 * path:vendor/pestphp/pest/src/Plugins/Parallel/Handlers/Pest.php	 * source<?php

declare(strict_types=1);

namespace Pest\Plugins\Parallel\Handlers;

use Pest\Plugins\Concerns\HandleArguments;
use Pest\Plugins\Parallel\Contracts\HandlersWorkerArguments;

final class Pest implements HandlersWorkerArguments
{
    use HandleArguments;

    /**
     * Handles the arguments, adding the "PEST_PARALLEL" environment variable to the global $_SERVER.
     */
    public function handleWorkerArguments(array $arguments): array
    {
        $_SERVER['PEST_PARALLEL'] = '1';

        return $arguments;
    }
}
 * namespaceAliases\Pest\Plugins\Parallel\HandlersphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameHandlers * includes * constants * functions
 * classes$\Pest\Plugins\Parallel\Handlers\Pest(phpDocumentor\Descriptor\ClassDescriptor#$+%Pest-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methodshandleWorkerArguments)phpDocumentor\Descriptor\MethodDescriptor#$=\Pest\Plugins\Parallel\Handlers\Pest::handleWorkerArguments()%55" ^Handles the arguments, adding the "PEST_PARALLEL" environment variable to the global $_SERVER.	

param  ./0./0
 	 * parent" * arguments	arguments+phpDocumentor\Descriptor\ArgumentDescriptor <
 
	 
  """# 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicMN	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType@AB C DEF G H IJ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(: 
 * implements8\Pest\Plugins\Parallel\Contracts\HandlersWorkerArguments#$[%HandlersWorkerArguments
 * usedTraits&\Pest\Plugins\Concerns\HandleArguments#$^%HandleArguments 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums