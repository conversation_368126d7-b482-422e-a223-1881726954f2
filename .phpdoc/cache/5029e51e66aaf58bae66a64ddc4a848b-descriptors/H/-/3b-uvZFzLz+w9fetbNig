1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0edcf5c6c5df0eee16971374b20161fe
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameComplexType.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 72abe25bb46cf0592494815e936a1ad2 * pathOvendor/rector/rector/vendor/nikic/php-parser/lib/PhpParser/Node/ComplexType.php	 * sourceC<?php

declare (strict_types=1);
namespace PhpParser\Node;

use PhpParser\NodeAbstract;
/**
 * This is a base class for complex types, including nullable types and union types.
 *
 * It does not provide any shared behavior and exists only for type-checking purposes.
 */
abstract class ComplexType extends NodeAbstract
{
}
 * namespaceAliases\PhpParser\NodephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameNode * includes * constants * functions
 * classes\PhpParser\Node\ComplexType(phpDocumentor\Descriptor\ClassDescriptor#$+%ComplexType-"QThis is a base class for complex types, including nullable types and union types.	SIt does not provide any shared behavior and exists only for type-checking purposes.

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 012   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$\PhpParser\NodeAbstract%NodeAbstract
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums