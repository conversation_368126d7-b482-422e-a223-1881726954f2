1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-70cdf7f77e6b81252d39d9df290f9b0f
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameDegradedNumberConverter.php * namespace

 * packageApplication
 * summary,This file is part of the ramsey/uuid library * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplatexFor the full copyright and license information, please view the LICENSE
file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * items	copyright &phpDocumentor\Descriptor\TagDescriptor

,Copyright (c) Ben <PERSON> <<EMAIL>> license 

&http://opensource.org/licenses/MIT MIT package 

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 8fab82172e727093cf847e69feaca72e * pathCvendor/ramsey/uuid/src/Converter/Number/DegradedNumberConverter.php	 * sourceo<?php

/**
 * This file is part of the ramsey/uuid library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) Ben Ramsey <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 */

declare(strict_types=1);

namespace Ramsey\Uuid\Converter\Number;

/**
 * @deprecated DegradedNumberConverter is no longer necessary for converting numbers on 32-bit systems. Please
 *     transition to {@see GenericNumberConverter}.
 *
 * @immutable
 */
class DegradedNumberConverter extends BigNumberConverter
{
}
 * namespaceAliases\Ramsey\Uuid\Converter\NumberphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen($ phpDocumentor\Reflection\Fqsen nameNumber * includes * constants * functions
 * classes5\Ramsey\Uuid\Converter\Number\DegradedNumberConverter(phpDocumentor\Descriptor\ClassDescriptor)*1+DegradedNumberConverter3(




deprecated 1phpDocumentor\Descriptor\Tag\DeprecatedDescriptor4

sDegradedNumberConverter is no longer necessary for converting numbers on 32-bit systems. Please
transition to %1$s. *phpDocumentor\Reflection\DocBlock\Tags\Seesee
 	 * refers6phpDocumentor\Reflection\DocBlock\Tags\Reference\Fqsen= phpDocumentor\Reflection\DocBlock\Tags\Reference\Fqsen fqsen)*4\Ramsey\Uuid\Converter\Number\GenericNumberConverter+GenericNumberConverter *phpDocumentor\Descriptor\Tag\SeeDescriptor8


 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference"+ : phpDocumentor\Descriptor\Tag\DeprecatedDescriptor version
	immutable A


 ""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber BCD    * readOnly * final * abstract
 * methods
 * properties.	 * parent)*0\Ramsey\Uuid\Converter\Number\BigNumberConverter+BigNumberConverter
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums