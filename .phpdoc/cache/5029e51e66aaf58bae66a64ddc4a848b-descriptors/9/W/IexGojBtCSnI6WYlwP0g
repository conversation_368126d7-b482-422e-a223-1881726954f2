1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-4316ff6289a167b170c67af7d5ad1de1
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Exception.php * namespace

 * packageApplication
 * summary'Copyright (c) 2022-2024 Andreas <PERSON> * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate{For the full copyright and license information, please view
the LICENSE.md file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * itemssee *phpDocumentor\Descriptor\Tag\SeeDescriptor


 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference4phpDocumentor\Reflection\DocBlock\Tags\Reference\Url9 phpDocumentor\Reflection\DocBlock\Tags\Reference\Url uri(https://github.com/ergebnis/json-pointerpackage &phpDocumentor\Descriptor\TagDescriptor

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 759ffb0de6aea6679f587541f780ebb7 * path8vendor/ergebnis/json-pointer/src/Exception/Exception.php	 * sourcee<?php

declare(strict_types=1);

/**
 * Copyright (c) 2022-2024 Andreas Möller
 *
 * For the full copyright and license information, please view
 * the LICENSE.md file that was distributed with this source code.
 *
 * @see https://github.com/ergebnis/json-pointer
 */

namespace Ergebnis\Json\Pointer\Exception;

interface Exception extends \Throwable
{
}
 * namespaceAliases \Ergebnis\Json\Pointer\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen*$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classes
 * interfaces*\Ergebnis\Json\Pointer\Exception\Exception,phpDocumentor\Descriptor\InterfaceDescriptor+,4-..*


 ""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber  678 !" 
 * parents
\Throwable+,:-	Throwable0
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums