1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-714f5e7234d97dd5a43ea211f64e531b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameFileDoesNotExist.php * namespace

 * packageApplication
 * summary'Copyright (c) 2022-2024 Andreas M<PERSON>ller * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate{For the full copyright and license information, please view
the LICENSE.md file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * itemssee *phpDocumentor\Descriptor\Tag\SeeDescriptor


 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference4phpDocumentor\Reflection\DocBlock\Tags\Reference\Url9 phpDocumentor\Reflection\DocBlock\Tags\Reference\Url uri https://github.com/ergebnis/jsonpackage &phpDocumentor\Descriptor\TagDescriptor

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 046a55fb8c2ab82c4024de853f693600 * path7vendor/ergebnis/json/src/Exception/FileDoesNotExist.php	 * source<?php

declare(strict_types=1);

/**
 * Copyright (c) 2022-2024 Andreas Möller
 *
 * For the full copyright and license information, please view
 * the LICENSE.md file that was distributed with this source code.
 *
 * @see https://github.com/ergebnis/json
 */

namespace Ergebnis\Json\Exception;

final class FileDoesNotExist extends \InvalidArgumentException
{
    public static function file(string $name): self
    {
        return new self(\sprintf(
            'File "%s" does not exist.',
            $name,
        ));
    }
}
 * namespaceAliases\Ergebnis\Json\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen*$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classes)\Ergebnis\Json\Exception\FileDoesNotExist(phpDocumentor\Descriptor\ClassDescriptor+,3-FileDoesNotExist5*

 ""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber  678 !"  * readOnly * final * abstract
 * methodsfile)phpDocumentor\Descriptor\MethodDescriptor+,1\Ergebnis\Json\Exception\FileDoesNotExist::file()-==* 

 param  678o 678!" 	 * parent" * argumentsname+phpDocumentor\Descriptor\ArgumentDescriptor C
 

   "( ")!" 3 phpDocumentor\Descriptor\ArgumentDescriptor method"# * type&phpDocumentor\Reflection\Types\String_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicJK	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType$phpDocumentor\Reflection\Types\Self_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference:;
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties0A+,\InvalidArgumentException-InvalidArgumentException
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums