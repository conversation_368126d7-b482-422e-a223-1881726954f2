1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0775d16c46df564be4c83a30ce9db36d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namerun.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash e61f797371bab1cbae440eb04138a214 * path.vendor/hamcrest/hamcrest-php/generator/run.php	 * source;<?php

/*
 Copyright (c) 2009 hamcrest.org
 */
require __DIR__ . '/../vendor/autoload.php';

/*
 * Generates the Hamcrest\Matchers factory class and factory functions
 * from the @factory doctags in the various matchers.
 */

define('GENERATOR_BASE', __DIR__);
define('HAMCREST_BASE', realpath(dirname(GENERATOR_BASE) . DIRECTORY_SEPARATOR . 'hamcrest'));

define('GLOBAL_FUNCTIONS_FILE', HAMCREST_BASE . DIRECTORY_SEPARATOR . 'Hamcrest.php');
define('STATIC_MATCHERS_FILE', HAMCREST_BASE . DIRECTORY_SEPARATOR . 'Hamcrest' . DIRECTORY_SEPARATOR . 'Matchers.php');

set_include_path(
    implode(
        PATH_SEPARATOR,
        array(
            GENERATOR_BASE,
            HAMCREST_BASE,
            get_include_path()
        )
    )
);

@unlink(GLOBAL_FUNCTIONS_FILE);
@unlink(STATIC_MATCHERS_FILE);

$generator = new FactoryGenerator(HAMCREST_BASE . DIRECTORY_SEPARATOR . 'Hamcrest');
$generator->addFactoryFile(new StaticMethodFile(STATIC_MATCHERS_FILE));
$generator->addFactoryFile(new GlobalFunctionFile(GLOBAL_FUNCTIONS_FILE));
$generator->generate();
$generator->write();
 * namespaceAliases * includes * constants\GENERATOR_BASE+phpDocumentor\Descriptor\ConstantDescriptorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen$$ phpDocumentor\Reflection\Fqsen nameGENERATOR_BASE)
 
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber *+
,   * value__DIR__ * final
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write 	 * parent  * type  \HAMCREST_BASE%&'8(
HAMCREST_BASE9
 
	 

""  *+, *+,  -H\realpath(\dirname(\GENERATOR_BASE) . \DIRECTORY_SEPARATOR . 'hamcrest')/012"5 6 7  \GLOBAL_FUNCTIONS_FILE%&';(GLOBAL_FUNCTIONS_FILE<
 
	 

""  *+, *+,  -6\HAMCREST_BASE . \DIRECTORY_SEPARATOR . 'Hamcrest.php'/012"5 6 7  \STATIC_MATCHERS_FILE%&'>(STATIC_MATCHERS_FILE?
 
	 

""  *+, *+,  -Z\HAMCREST_BASE . \DIRECTORY_SEPARATOR . 'Hamcrest' . \DIRECTORY_SEPARATOR . 'Matchers.php'/012"5 6 7   * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums