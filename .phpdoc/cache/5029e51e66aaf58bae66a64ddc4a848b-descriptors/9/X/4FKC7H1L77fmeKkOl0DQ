1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-36b9ca8673c8ed5056476cc8242d12d3
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameReaderInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 76c307ae55a5cfd9d4a9938eebe4ec16 * pathBvendor/vlucas/phpdotenv/src/Repository/Adapter/ReaderInterface.php	 * source,<?php

declare(strict_types=1);

namespace Dotenv\Repository\Adapter;

interface ReaderInterface
{
    /**
     * Read an environment variable, if it exists.
     *
     * @param non-empty-string $name
     *
     * @return \PhpOption\Option<string>
     */
    public function read(string $name);
}
 * namespaceAliases\Dotenv\Repository\AdapterphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameAdapter * includes * constants * functions
 * classes
 * interfaces*\Dotenv\Repository\Adapter\ReaderInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%ReaderInterface."

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methodsread)phpDocumentor\Descriptor\MethodDescriptor#$2\Dotenv\Repository\Adapter\ReaderInterface::read()%44" +Read an environment variable, if it exists.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type3phpDocumentor\Reflection\PseudoTypes\NonEmptyString  * variableNamenamereturn -phpDocumentor\Descriptor\Tag\ReturnDescriptor>	
 :)phpDocumentor\Reflection\Types\Collection * valueType&phpDocumentor\Reflection\Types\String_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types B &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|0 phpDocumentor\Reflection\Types\Collection fqsen#$\PhpOption\Option%Option  /01/01( 	 * parent" * arguments=+phpDocumentor\Descriptor\ArgumentDescriptor =
 
	"'
  "6"7 3 phpDocumentor\Descriptor\ArgumentDescriptor method":")
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicST	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums