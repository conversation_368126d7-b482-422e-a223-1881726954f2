1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-fc47fb0aa8f84604b7cfbbc7ca0f3f78
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameCallsTerminable.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash b4d7f8e4981db0030a062079ea7a79b7 * path;vendor/pestphp/pest/src/Plugins/Actions/CallsTerminable.php	 * source <?php

declare(strict_types=1);

namespace Pest\Plugins\Actions;

use Pest\Contracts\Plugins;
use Pest\Plugin\Loader;

/**
 * @internal
 */
final class CallsTerminable
{
    /**
     * Executes the Plugin action.
     *
     * Provides an opportunity for any plugins to terminate.
     */
    public static function execute(): void
    {
        $plugins = Loader::getPlugins(Plugins\Terminable::class);

        /** @var Plugins\Terminable $plugin */
        foreach ($plugins as $plugin) {
            $plugin->terminate();
        }
    }
}
 * namespaceAliases\Pest\Plugins\ActionsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameActions * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums