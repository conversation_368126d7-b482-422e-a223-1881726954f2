1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-f7ee18520f7243c7f292ecd4327ba79a
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameReflectionTypeContainer.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 611a7e97842d4b80274751717492eb77 * pathCvendor/larastan/larastan/src/Properties/ReflectionTypeContainer.php	 * source4<?php

declare(strict_types=1);

namespace Larastan\Larastan\Properties;

use ReflectionNamedType;

/** @internal */
final class ReflectionTypeContainer extends ReflectionNamedType
{
    public function __construct(private string $type)
    {
    }

    public function allowsNull(): bool
    {
        return false;
    }

    public function isBuiltin(): bool
    {
        return false;
    }

    public function __toString(): string
    {
        return $this->getName();
    }

    public function getName(): string
    {
        return $this->type;
    }
}
 * namespaceAliases\Larastan\Larastan\PropertiesphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Properties * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums