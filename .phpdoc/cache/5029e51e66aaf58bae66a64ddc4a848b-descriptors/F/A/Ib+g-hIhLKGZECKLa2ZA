1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-0e97a58ac2787e8317f43515e6b36f61
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePathSkipper.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 45098fb3fe2b5a6ec501d5b9b8451729 * path8vendor/rector/rector/src/Skipper/Skipper/PathSkipper.php	 * source<<?php

declare (strict_types=1);
namespace Rector\Skipper\Skipper;

use Rector\Skipper\Matcher\FileInfoMatcher;
use Rector\Skipper\SkipCriteriaResolver\SkippedPathsResolver;
final class PathSkipper
{
    /**
     * @readonly
     */
    private FileInfoMatcher $fileInfoMatcher;
    /**
     * @readonly
     */
    private SkippedPathsResolver $skippedPathsResolver;
    public function __construct(FileInfoMatcher $fileInfoMatcher, SkippedPathsResolver $skippedPathsResolver)
    {
        $this->fileInfoMatcher = $fileInfoMatcher;
        $this->skippedPathsResolver = $skippedPathsResolver;
    }
    public function shouldSkip(string $filePath) : bool
    {
        $skippedPaths = $this->skippedPathsResolver->resolve();
        return $this->fileInfoMatcher->doesFileInfoMatchPatterns($filePath, $skippedPaths);
    }
}
 * namespaceAliases\Rector\Skipper\SkipperphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSkipper * includes * constants * functions
 * classes#\Rector\Skipper\Skipper\PathSkipper(phpDocumentor\Descriptor\ClassDescriptor#$+%PathSkipper-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$2\Rector\Skipper\Skipper\PathSkipper::__construct()%55" 
	 
param  ./0t./0X 	 * parent" * argumentsfileInfoMatcher+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$'\Rector\Skipper\Matcher\FileInfoMatcher%FileInfoMatcher
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicEFskippedPathsResolver< H
 
	 
  " "! =">?@#$9\Rector\Skipper\SkipCriteriaResolver\SkippedPathsResolver%SkippedPathsResolverC DEFGEF	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
shouldSkip6#$1\Rector\Skipper\Skipper\PathSkipper::shouldSkip()%UU" 
	 
8  ./0^./08 9":filePath< W
 
	 
  ";"< ="6>&phpDocumentor\Reflection\Types\String_ C DEFGEFKL&phpDocumentor\Reflection\Types\Boolean N23OPQ"5T  
 * properties;+phpDocumentor\Descriptor\PropertyDescriptor#$5\Rector\Skipper\Skipper\PathSkipper::$fileInfoMatcher%;;+ 
	

readonly ]	
 var  ./
0 ./
0  9"K5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualOPQR'PRIVATET >?@#$A%BC  H[#$:\Rector\Skipper\Skipper\PathSkipper::$skippedPathsResolver%HH+ 
	

] ]	
 ^  ./0 ./0  9"K_`abOPQ"[T >?@#$I%JC  (9 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums