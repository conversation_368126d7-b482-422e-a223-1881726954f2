1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c1fbc36d39fc3becdc636b4273f3463b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameViewCompilationException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 22bc32e824ba65cd1353930958eed3ec * path=vendor/illuminate/contracts/View/ViewCompilationException.php	 * sourcey<?php

namespace Illuminate\Contracts\View;

use Exception;

class ViewCompilationException extends Exception
{
    //
}
 * namespaceAliases\Illuminate\Contracts\ViewphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameView * includes * constants * functions
 * classes3\Illuminate\Contracts\View\ViewCompilationException(phpDocumentor\Descriptor\ClassDescriptor#$+%ViewCompilationException-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./
0   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$
\Exception%	Exception
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums