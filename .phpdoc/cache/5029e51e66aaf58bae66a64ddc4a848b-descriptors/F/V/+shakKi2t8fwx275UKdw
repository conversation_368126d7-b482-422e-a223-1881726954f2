1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-2f179bdd9290b7a42ee9b08ae3e26031
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameRequestExceptionInterface.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash a5af1075472ac86a825c4a6ccf04ef54 * path8vendor/psr/http-client/src/RequestExceptionInterface.php	 * sourceJ<?php

namespace Psr\Http\Client;

use Psr\Http\Message\RequestInterface;

/**
 * Exception for when a request failed.
 *
 * Examples:
 *      - Request is invalid (e.g. method is missing)
 *      - Runtime request errors (e.g. the body stream is not seekable)
 */
interface RequestExceptionInterface extends ClientExceptionInterface
{
    /**
     * Returns the request.
     *
     * The request object MAY be a different object from the one passed to ClientInterface::sendRequest()
     *
     * @return RequestInterface
     */
    public function getRequest(): RequestInterface;
}
 * namespaceAliases\Psr\Http\ClientphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameClient * includes * constants * functions
 * classes
 * interfaces*\Psr\Http\Client\RequestExceptionInterface,phpDocumentor\Descriptor\InterfaceDescriptor#$,%RequestExceptionInterface."
$Exception for when a request failed.	wExamples:
- Request is invalid (e.g. method is missing)
- Runtime request errors (e.g. the body stream is not seekable)

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 123  
 * parents)\Psr\Http\Client\ClientExceptionInterface#$5%ClientExceptionInterface(
 * methods
getRequest)phpDocumentor\Descriptor\MethodDescriptor#$8\Psr\Http\Client\RequestExceptionInterface::getRequest()%88" Returns the request.	bThe request object MAY be a different object from the one passed to ClientInterface::sendRequest()
return -phpDocumentor\Descriptor\Tag\ReturnDescriptor=	
  * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$"\Psr\Http\Message\RequestInterface%RequestInterface  123123F 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType@A#$B%C? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums