1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-e070c8976bf0ac15a4b196a6879f35be
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameMisspelling.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash d9d0ea369961ddba6d8a7ef2aff5692e * path4vendor/peckphp/peck/src/ValueObjects/Misspelling.php	 * sourceR<?php

declare(strict_types=1);

namespace Peck\ValueObjects;

/**
 * @internal
 */
final readonly class Misspelling
{
    /**
     * Creates a new instance of Issue.
     *
     * @param  array<int, string>  $suggestions
     */
    public function __construct(
        public string $word,
        public array $suggestions,
    ) {}
}
 * namespaceAliases\Peck\ValueObjectsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameValueObjects * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums