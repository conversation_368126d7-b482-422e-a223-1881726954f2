1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-154b277bcd9b3bb7708c50baa422746e
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameMissingInputException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash e411fa17c0216887b53f126ce1cd0093 * pathOvendor/rector/rector/vendor/symfony/console/Exception/MissingInputException.php	 * source <?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace RectorPrefix202507\Symfony\Component\Console\Exception;

/**
 * Represents failure to read input from stdin.
 *
 * <AUTHOR> Ostrolucký <<EMAIL>>
 */
class MissingInputException extends RuntimeException implements ExceptionInterface
{
}
 * namespaceAliases7\RectorPrefix202507\Symfony\Component\Console\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classesM\RectorPrefix202507\Symfony\Component\Console\Exception\MissingInputException(phpDocumentor\Descriptor\ClassDescriptor#$+%MissingInputException-",Represents failure to read input from stdin.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	2Gabriel Ostrolucký <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 234   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$H\RectorPrefix202507\Symfony\Component\Console\Exception\RuntimeException%RuntimeException
 * implementsJ\RectorPrefix202507\Symfony\Component\Console\Exception\ExceptionInterface#$>%ExceptionInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums