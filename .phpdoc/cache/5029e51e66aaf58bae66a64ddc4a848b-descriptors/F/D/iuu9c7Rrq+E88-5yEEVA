1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c34d561652dc023b10f30353b27838db
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Thanks.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash e59f0b7304b3f62029a452976984d080 * path*vendor/pestphp/pest/src/Console/Thanks.php	 * source
F<?php

declare(strict_types=1);

namespace Pest\Console;

use Pest\Bootstrappers\BootView;
use Pest\Support\View;
use Symfony\Component\Console\Helper\SymfonyQuestionHelper;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;

/**
 * @internal
 */
final readonly class Thanks
{
    /**
     * The support options.
     *
     * @var array<string, string>
     */
    private const FUNDING_MESSAGES = [
        'Star' => 'https://github.com/pestphp/pest',
        'YouTube' => 'https://youtube.com/@nunomaduro',
        'TikTok' => 'https://tiktok.com/@nunomaduro',
        'Twitch' => 'https://twitch.tv/enunomaduro',
        'LinkedIn' => 'https://linkedin.com/in/nunomaduro',
        'Instagram' => 'https://instagram.com/enunomaduro',
        'X' => 'https://x.com/enunomaduro',
        'Sponsor' => 'https://github.com/sponsors/nunomaduro',
    ];

    /**
     * Creates a new Console Command instance.
     */
    public function __construct(
        private InputInterface $input,
        private OutputInterface $output
    ) {
        // ..
    }

    /**
     * Executes the Console Command.
     */
    public function __invoke(): void
    {
        $bootstrapper = new BootView($this->output);
        $bootstrapper->boot();

        $wantsToSupport = false;

        if (getenv('PEST_NO_SUPPORT') !== 'true' && $this->input->isInteractive()) {
            $wantsToSupport = (new SymfonyQuestionHelper)->ask(
                new ArrayInput([]),
                $this->output,
                new ConfirmationQuestion(
                    ' <options=bold>Wanna show Pest some love by starring it on GitHub?</>',
                    false,
                )
            );

            View::render('components.new-line');

            foreach (self::FUNDING_MESSAGES as $message => $link) {
                View::render('components.two-column-detail', [
                    'left' => $message,
                    'right' => $link,
                ]);
            }

            View::render('components.new-line');
        }

        if ($wantsToSupport === true) {
            if (PHP_OS_FAMILY === 'Darwin') {
                exec('open https://github.com/pestphp/pest');
            }
            if (PHP_OS_FAMILY === 'Windows') {
                exec('start https://github.com/pestphp/pest');
            }
            if (PHP_OS_FAMILY === 'Linux') {
                exec('xdg-open https://github.com/pestphp/pest');
            }
        }
    }
}
 * namespaceAliases
\Pest\ConsolephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameConsole * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums