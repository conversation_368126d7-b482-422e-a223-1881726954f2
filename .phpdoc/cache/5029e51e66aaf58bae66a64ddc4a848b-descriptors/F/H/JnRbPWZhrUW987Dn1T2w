**********
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-929924065dc8c6e6197846791ad08f8c
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameCoreSetProvider.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 465777f727b764509e23f66b1f0c9d0f * path<vendor/rector/rector/src/Set/SetProvider/CoreSetProvider.php	 * sourceE<?php

declare (strict_types=1);
namespace Rector\Set\SetProvider;

use Rector\Set\Contract\SetInterface;
use Rector\Set\Contract\SetProviderInterface;
use Rector\Set\Enum\SetGroup;
use Rector\Set\ValueObject\ComposerTriggeredSet;
use Rector\Set\ValueObject\Set;
final class CoreSetProvider implements SetProviderInterface
{
    /**
     * @return SetInterface[]
     */
    public function provide() : array
    {
        return [new Set(SetGroup::CORE, 'Code Quality', __DIR__ . '/../../../config/set/code-quality.php'), new Set(SetGroup::CORE, 'Coding Style', __DIR__ . '/../../../config/set/coding-style.php'), new Set(SetGroup::CORE, 'Dead Code', __DIR__ . '/../../../config/set/dead-code.php'), new Set(SetGroup::CORE, 'DateTime to Carbon', __DIR__ . '/../../../config/set/datetime-to-carbon.php'), new Set(SetGroup::CORE, 'Instanceof', __DIR__ . '/../../../config/set/instanceof.php'), new Set(SetGroup::CORE, 'Early return', __DIR__ . '/../../../config/set/early-return.php'), new Set(SetGroup::CORE, 'Gmagick to Imagick', __DIR__ . '/../../../config/set/gmagick-to-imagick.php'), new Set(SetGroup::CORE, 'Naming', __DIR__ . '/../../../config/set/naming.php'), new Set(SetGroup::CORE, 'Privatization', __DIR__ . '/../../../config/set/privatization.php'), new Set(SetGroup::CORE, 'Strict Booleans', __DIR__ . '/../../../config/set/strict-booleans.php'), new Set(SetGroup::CORE, 'Type Declarations', __DIR__ . '/../../../config/set/type-declaration.php'), new ComposerTriggeredSet(SetGroup::NETTE_UTILS, 'nette/utils', '4.0', __DIR__ . '/../../../config/set/nette-utils/nette-utils4.php')];
    }
}
 * namespaceAliases\Rector\Set\SetProviderphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSetProvider * includes * constants * functions
 * classes'\Rector\Set\SetProvider\CoreSetProvider(phpDocumentor\Descriptor\ClassDescriptor#$+%CoreSetProvider-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methodsprovide)phpDocumentor\Descriptor\MethodDescriptor#$2\Rector\Set\SetProvider\CoreSetProvider::provide()%55" 
	

return -phpDocumentor\Descriptor\Tag\ReturnDescriptor8	
  * type%phpDocumentor\Reflection\Types\Array_ * valueType&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$!\Rector\Set\Contract\SetInterface%SetInterface
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|  ./0w./0A 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType;<%phpDocumentor\Reflection\Types\Mixed_ A BCD E F GH? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(I 
 * implements)\Rector\Set\Contract\SetProviderInterface#$W%SetProviderInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums