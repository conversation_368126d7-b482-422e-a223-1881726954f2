1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-2cd41f660eed2ce46bbe05d59ea8e451
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameup-to-cashier-14.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 553f2f8b3015b84239395005574deaa0 * pathXvendor/driftingly/rector-laravel/config/sets/packages/cashier/level/up-to-cashier-14.php	 * sourced<?php

declare(strict_types=1);

use Rector\Config\RectorConfig;
use RectorLaravel\Set\Packages\Cashier\CashierLevelSetList;
use RectorLaravel\Set\Packages\Cashier\CashierSetList;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->sets([CashierSetList::LARAVEL_CASHIER_140, CashierLevelSetList::UP_TO_LARAVEL_CASHIER_130]);
};
 * namespaceAliases * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums