1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-93bcc521a18fa82fdd73096febe13dd3
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameNumberComparator.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 778dca53481e2f49ff5ded4575a61c80 * path5vendor/symfony/finder/Comparator/NumberComparator.php	 * source
<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Finder\Comparator;

/**
 * NumberComparator compiles a simple comparison to an anonymous
 * subroutine, which you can call with a value to be tested again.
 *
 * Now this would be very pointless, if NumberCompare didn't understand
 * magnitudes.
 *
 * The target value may use magnitudes of kilobytes (k, ki),
 * megabytes (m, mi), or gigabytes (g, gi). Those suffixed
 * with an i use the appropriate 2**n version in accordance with the
 * IEC standard: http://physics.nist.gov/cuu/Units/binary.html
 *
 * Based on the Perl Number::Compare module.
 *
 * <AUTHOR> Potencier <<EMAIL>> PHP port
 * <AUTHOR> Clamp <<EMAIL>> Perl version
 * @copyright 2004-2005 Fabien Potencier <<EMAIL>>
 * @copyright 2002 Richard Clamp <<EMAIL>>
 *
 * @see http://physics.nist.gov/cuu/Units/binary.html
 */
class NumberComparator extends Comparator
{
    /**
     * @param string|null $test A comparison string or null
     *
     * @throws \InvalidArgumentException If the test is not understood
     */
    public function __construct(?string $test)
    {
        if (null === $test || !preg_match('#^\s*(==|!=|[<>]=?)?\s*([0-9\.]+)\s*([kmg]i?)?\s*$#i', $test, $matches)) {
            throw new \InvalidArgumentException(\sprintf('Don\'t understand "%s" as a number test.', $test ?? 'null'));
        }

        $target = $matches[2];
        if (!is_numeric($target)) {
            throw new \InvalidArgumentException(\sprintf('Invalid number "%s".', $target));
        }
        if (isset($matches[3])) {
            // magnitude
            switch (strtolower($matches[3])) {
                case 'k':
                    $target *= 1000;
                    break;
                case 'ki':
                    $target *= 1024;
                    break;
                case 'm':
                    $target *= 1000000;
                    break;
                case 'mi':
                    $target *= 1024 * 1024;
                    break;
                case 'g':
                    $target *= 1000000000;
                    break;
                case 'gi':
                    $target *= 1024 * 1024 * 1024;
                    break;
            }
        }

        parent::__construct($target, $matches[1] ?: '==');
    }
}
 * namespaceAliases$\Symfony\Component\Finder\ComparatorphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
Comparator * includes * constants * functions
 * classes5\Symfony\Component\Finder\Comparator\NumberComparator(phpDocumentor\Descriptor\ClassDescriptor#$+%NumberComparator-"}NumberComparator compiles a simple comparison to an anonymous
subroutine, which you can call with a value to be tested again.	lNow this would be very pointless, if NumberCompare didn't understand
magnitudes.

The target value may use magnitudes of kilobytes (k, ki),
megabytes (m, mi), or gigabytes (g, gi). Those suffixed
with an i use the appropriate 2**n version in accordance with the
IEC standard: http://physics.nist.gov/cuu/Units/binary.html

Based on the Perl Number::Compare module.
author 0	.Fabien Potencier <<EMAIL>> PHP port )phpDocumentor\Descriptor\Validation\Error * severityERROR * codeZTag "author" with body "<AUTHOR> Potencier <<EMAIL>> PHP port" has error  
 * context0	3Richard Clamp <<EMAIL>> Perl version 2345_Tag "author" with body "<AUTHOR> Clamp <<EMAIL>> Perl version" has error  7	copyright :	/2004-2005 Fabien Potencier <<EMAIL>> :	+2002 Richard Clamp <<EMAIL>> see *phpDocumentor\Descriptor\Tag\SeeDescriptor=	
 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference4phpDocumentor\Reflection\DocBlock\Tags\Reference\Url9 phpDocumentor\Reflection\DocBlock\Tags\Reference\Url uri-http://physics.nist.gov/cuu/Units/binary.html
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber#/ phpDocumentor\Reflection\Location columnNumber CDNE   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$D\Symfony\Component\Finder\Comparator\NumberComparator::__construct()%JJ" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptorM	A comparison string or null  * type'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ $phpDocumentor\Reflection\Types\Null_ 4 phpDocumentor\Reflection\Types\AggregatedType token| * variableNametestthrows -phpDocumentor\Descriptor\Tag\ThrowsDescriptorY	If the test is not understood P&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\InvalidArgumentException%InvalidArgumentException  CD*E0CDME
 	 * parent" * argumentsX+phpDocumentor\Descriptor\ArgumentDescriptor X
 
	"B
  "O"P 3 phpDocumentor\Descriptor\ArgumentDescriptor method"9P"D
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicfg	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReferenceGH
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(`#$/\Symfony\Component\Finder\Comparator\Comparator%&
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums