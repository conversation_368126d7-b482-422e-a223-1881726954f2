1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-2a2165b0d922113bb449a6e7cd61f72b
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameCliBasedNotifier.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ad98adfd529f80f4e4dde71e88dc6a87 * path;vendor/jolicode/jolinotif/src/Notifier/CliBasedNotifier.php	 * sourcev<?php

/*
 * This file is part of the JoliNotif project.
 *
 * (c) Loïck Piera <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Joli\JoliNotif\Notifier;

use Joli\JoliNotif\Driver\AbstractCliBasedDriver;
use Joli\JoliNotif\Notifier;

trigger_deprecation('jolicode/jolinotif', '2.7', 'The "%s" class is deprecated and will be removed in 3.0.', CliBasedNotifier::class);

/**
 * @deprecated since 2.7, will be removed in 3.0
 */
abstract class CliBasedNotifier extends AbstractCliBasedDriver implements Notifier
{
}
 * namespaceAliases\Joli\JoliNotif\NotifierphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameNotifier * includes * constants * functions
 * classes)\Joli\JoliNotif\Notifier\CliBasedNotifier(phpDocumentor\Descriptor\ClassDescriptor#$+%CliBasedNotifier-"
	


deprecated 1phpDocumentor\Descriptor\Tag\DeprecatedDescriptor.	!since 2.7, will be removed in 3.0 : phpDocumentor\Descriptor\Tag\DeprecatedDescriptor version

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 234   * readOnly * final * abstract
 * methods
 * properties(	 * parent#$-\Joli\JoliNotif\Driver\AbstractCliBasedDriver%AbstractCliBasedDriver
 * implements"#$"%&
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums