1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-5a1069b55677bff35c06454eeea7a417
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameMethodRequestMatcher.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 1efb433b75fd0dc6f8eb89362ed79daa * pathFvendor/symfony/http-foundation/RequestMatcher/MethodRequestMatcher.php	 * source<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpFoundation\RequestMatcher;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestMatcherInterface;

/**
 * Checks the HTTP method of a Request.
 *
 * <AUTHOR> Potencier <<EMAIL>>
 */
class MethodRequestMatcher implements RequestMatcherInterface
{
    /**
     * @var string[]
     */
    private array $methods = [];

    /**
     * @param string[]|string $methods An HTTP method or an array of HTTP methods
     *                                 Strings can contain a comma-delimited list of methods
     */
    public function __construct(array|string $methods)
    {
        $this->methods = array_reduce(array_map('strtoupper', (array) $methods), static fn (array $methods, string $method) => array_merge($methods, preg_split('/\s*,\s*/', $method)), []);
    }

    public function matches(Request $request): bool
    {
        if (!$this->methods) {
            return true;
        }

        return \in_array($request->getMethod(), $this->methods, true);
    }
}
 * namespaceAliases0\Symfony\Component\HttpFoundation\RequestMatcherphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRequestMatcher * includes * constants * functions
 * classesE\Symfony\Component\HttpFoundation\RequestMatcher\MethodRequestMatcher(phpDocumentor\Descriptor\ClassDescriptor#$+%MethodRequestMatcher-"$Checks the HTTP method of a Request.	

author -phpDocumentor\Descriptor\Tag\AuthorDescriptor/	%Fabien Potencier <<EMAIL>> 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 23.4   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$T\Symfony\Component\HttpFoundation\RequestMatcher\MethodRequestMatcher::__construct()%99" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor<	`An HTTP method or an array of HTTP methods
Strings can contain a comma-delimited list of methods  * type'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types %phpDocumentor\Reflection\Types\Array_ * valueType&phpDocumentor\Reflection\Types\String_ 
 * keyType  * defaultKeyType@A D &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|D HI * variableNamemethods  23!4E23$4? 	 * parent" * argumentsK+phpDocumentor\Descriptor\ArgumentDescriptor K
 
	"+
  "6"7 3 phpDocumentor\Descriptor\ArgumentDescriptor method""?"-
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicRS	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference67
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  matches:#$P\Symfony\Component\HttpFoundation\RequestMatcher\MethodRequestMatcher::matches()%__" 
	 
<  23&4F23-4
 L"MrequestN a
 
	 
  "H"I O"C?&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$)\Symfony\Component\HttpFoundation\Request%RequestP QRSTRSUV&phpDocumentor\Reflection\Types\Boolean X67YZ["B^  
 * propertiesK+phpDocumentor\Descriptor\PropertyDescriptor#$O\Symfony\Component\HttpFoundation\RequestMatcher\MethodRequestMatcher::$methods%KK+ 
	

var *phpDocumentor\Descriptor\Tag\VarDescriptorj	
 ?BCD E F@A D G HIJ
  234 234  L"U5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualYZ[\'PRIVATE^ ?"cP[] (L 
 * implements9\Symfony\Component\HttpFoundation\RequestMatcherInterface#$s%RequestMatcherInterface
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums