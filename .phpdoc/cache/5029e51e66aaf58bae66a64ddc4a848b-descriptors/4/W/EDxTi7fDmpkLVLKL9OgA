1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-c4e11b337fef446dbd26b72296050865
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameXdebugDriver.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash ff86be23896a58409e4ca0b6d87635e0 * path<vendor/phpunit/php-code-coverage/src/Driver/XdebugDriver.php	 * sourceM<?php declare(strict_types=1);
/*
 * This file is part of phpunit/php-code-coverage.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SebastianBergmann\CodeCoverage\Driver;

use const XDEBUG_CC_BRANCH_CHECK;
use const XDEBUG_CC_DEAD_CODE;
use const XDEBUG_CC_UNUSED;
use const XDEBUG_FILTER_CODE_COVERAGE;
use const XDEBUG_PATH_INCLUDE;
use function explode;
use function extension_loaded;
use function getenv;
use function in_array;
use function ini_get;
use function phpversion;
use function version_compare;
use function xdebug_get_code_coverage;
use function xdebug_info;
use function xdebug_set_filter;
use function xdebug_start_code_coverage;
use function xdebug_stop_code_coverage;
use SebastianBergmann\CodeCoverage\Data\RawCodeCoverageData;
use SebastianBergmann\CodeCoverage\Filter;

/**
 * @internal This class is not covered by the backward compatibility promise for phpunit/php-code-coverage
 *
 * @see https://xdebug.org/docs/code_coverage#xdebug_get_code_coverage
 *
 * @phpstan-type XdebugLinesCoverageType = array<int, int>
 * @phpstan-type XdebugBranchCoverageType = array{
 *     op_start: int,
 *     op_end: int,
 *     line_start: int,
 *     line_end: int,
 *     hit: int,
 *     out: array<int, int>,
 *     out_hit: array<int, int>,
 * }
 * @phpstan-type XdebugPathCoverageType = array{
 *     path: array<int, int>,
 *     hit: int,
 * }
 * @phpstan-type XdebugFunctionCoverageType = array{
 *     branches: array<int, XdebugBranchCoverageType>,
 *     paths: array<int, XdebugPathCoverageType>,
 * }
 * @phpstan-type XdebugFunctionsCoverageType = array<string, XdebugFunctionCoverageType>
 * @phpstan-type XdebugPathAndBranchesCoverageType = array{
 *     lines: XdebugLinesCoverageType,
 *     functions: XdebugFunctionsCoverageType,
 * }
 * @phpstan-type XdebugCodeCoverageWithoutPathCoverageType = array<string, XdebugLinesCoverageType>
 * @phpstan-type XdebugCodeCoverageWithPathCoverageType = array<string, XdebugPathAndBranchesCoverageType>
 */
final class XdebugDriver extends Driver
{
    /**
     * @throws XdebugNotAvailableException
     * @throws XdebugNotEnabledException
     */
    public function __construct(Filter $filter)
    {
        $this->ensureXdebugIsAvailable();
        $this->ensureXdebugCodeCoverageFeatureIsEnabled();

        if (!$filter->isEmpty()) {
            xdebug_set_filter(
                XDEBUG_FILTER_CODE_COVERAGE,
                XDEBUG_PATH_INCLUDE,
                $filter->files(),
            );
        }
    }

    public function canCollectBranchAndPathCoverage(): bool
    {
        return true;
    }

    public function canDetectDeadCode(): bool
    {
        return true;
    }

    public function start(): void
    {
        $flags = XDEBUG_CC_UNUSED;

        if ($this->detectsDeadCode() || $this->collectsBranchAndPathCoverage()) {
            $flags |= XDEBUG_CC_DEAD_CODE;
        }

        if ($this->collectsBranchAndPathCoverage()) {
            $flags |= XDEBUG_CC_BRANCH_CHECK;
        }

        xdebug_start_code_coverage($flags);
    }

    public function stop(): RawCodeCoverageData
    {
        $data = xdebug_get_code_coverage();

        xdebug_stop_code_coverage();

        if ($this->collectsBranchAndPathCoverage()) {
            /* @var XdebugCodeCoverageWithPathCoverageType $data */
            return RawCodeCoverageData::fromXdebugWithPathCoverage($data);
        }

        /* @var XdebugCodeCoverageWithoutPathCoverageType $data */
        return RawCodeCoverageData::fromXdebugWithoutPathCoverage($data);
    }

    public function nameAndVersion(): string
    {
        return 'Xdebug ' . phpversion('xdebug');
    }

    /**
     * @throws XdebugNotAvailableException
     */
    private function ensureXdebugIsAvailable(): void
    {
        if (!extension_loaded('xdebug')) {
            throw new XdebugNotAvailableException;
        }
    }

    /**
     * @throws XdebugNotEnabledException
     */
    private function ensureXdebugCodeCoverageFeatureIsEnabled(): void
    {
        if (version_compare(phpversion('xdebug'), '3.1', '>=')) {
            if (!in_array('coverage', xdebug_info('mode'), true)) {
                throw new XdebugNotEnabledException;
            }

            return;
        }

        $mode = getenv('XDEBUG_MODE');

        if ($mode === false || $mode === '') {
            $mode = ini_get('xdebug.mode');
        }

        if ($mode === false ||
            !in_array('coverage', explode(',', $mode), true)) {
            throw new XdebugNotEnabledException;
        }
    }
}
 * namespaceAliases&\SebastianBergmann\CodeCoverage\DriverphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDriver * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums