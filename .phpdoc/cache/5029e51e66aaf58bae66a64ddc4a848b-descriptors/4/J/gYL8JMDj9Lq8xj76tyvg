1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-d21162f3349b0a66f6f901a71e20cbc0
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameLocalizable.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 08e2b132af3ed6591adf3aeae101dd96 * path0vendor/illuminate/support/Traits/Localizable.php	 * sourceu<?php

namespace Illuminate\Support\Traits;

use Illuminate\Container\Container;

trait Localizable
{
    /**
     * Run the callback with the given locale.
     *
     * @param  string  $locale
     * @param  \Closure  $callback
     * @return mixed
     */
    public function withLocale($locale, $callback)
    {
        if (! $locale) {
            return $callback();
        }

        $app = Container::getInstance();

        $original = $app->getLocale();

        try {
            $app->setLocale($locale);

            return $callback();
        } finally {
            $app->setLocale($original);
        }
    }
}
 * namespaceAliases\Illuminate\Support\TraitsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameTraits * includes * constants * functions
 * classes
 * interfaces	 * traits&\Illuminate\Support\Traits\Localizable(phpDocumentor\Descriptor\TraitDescriptor#$-%Localizable/"

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 01"2    
 * methods
withLocale)phpDocumentor\Descriptor\MethodDescriptor#$4\Illuminate\Support\Traits\Localizable::withLocale()%44" 'Run the callback with the given locale.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type&phpDocumentor\Reflection\Types\String_  * variableNamelocale98	
 :&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\Closure%Closure<callbackreturn -phpDocumentor\Descriptor\Tag\ReturnDescriptorC	
 :%phpDocumentor\Reflection\Types\Mixed_   01201!2q 	 * parent" * arguments=+phpDocumentor\Descriptor\ArgumentDescriptor =
 
	"&
  "4"5 3 phpDocumentor\Descriptor\ArgumentDescriptor method":"(
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicLMBH B
 
	"*
  "4"5 I":",J KLMNLM	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnTypeE ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * usedTraits 
 * markers. phpDocumentor\Descriptor\FileDescriptor enums