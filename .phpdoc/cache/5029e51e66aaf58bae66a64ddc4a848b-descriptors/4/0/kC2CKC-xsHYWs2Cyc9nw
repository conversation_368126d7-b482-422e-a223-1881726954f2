1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-41a340e767dedef92f94b14453f65183
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameBroadcastsEventsAfterCommit.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash bdc5d7b9e33753e31b108eef6fd527f7 * pathCvendor/illuminate/database/Eloquent/BroadcastsEventsAfterCommit.php	 * source\<?php

namespace Illuminate\Database\Eloquent;

trait BroadcastsEventsAfterCommit
{
    use BroadcastsEvents;

    /**
     * Determine if the model event broadcast queued job should be dispatched after all transactions are committed.
     *
     * @return bool
     */
    public function broadcastAfterCommit()
    {
        return true;
    }
}
 * namespaceAliases\Illuminate\Database\EloquentphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameEloquent * includes * constants * functions
 * classes
 * interfaces	 * traits9\Illuminate\Database\Eloquent\BroadcastsEventsAfterCommit(phpDocumentor\Descriptor\TraitDescriptor#$-%BroadcastsEventsAfterCommit/"

	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber 012    
 * methodsbroadcastAfterCommit)phpDocumentor\Descriptor\MethodDescriptor#$Q\Illuminate\Database\Eloquent\BroadcastsEventsAfterCommit::broadcastAfterCommit()%44" lDetermine if the model event broadcast queued job should be dispatched after all transactions are committed.	

return -phpDocumentor\Descriptor\Tag\ReturnDescriptor8	
  * type&phpDocumentor\Reflection\Types\Boolean   012012X 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * usedTraits.\Illuminate\Database\Eloquent\BroadcastsEvents#$K%BroadcastsEvents 
 * markers. phpDocumentor\Descriptor\FileDescriptor enums