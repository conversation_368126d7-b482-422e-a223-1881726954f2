1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-596d0642e29841731d321fbb2d09c28a
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameannotations-to-attributes.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 5be79d375f01a915aa1d308f3d7e247f * path_vendor/rector/rector/vendor/rector/rector-symfony/config/sets/jms/annotations-to-attributes.php	 * source"<?php

declare (strict_types=1);
namespace RectorPrefix202507;

use Rector\Config\RectorConfig;
use Rector\Php80\Rector\Class_\AnnotationToAttributeRector;
use Rector\Php80\ValueObject\AnnotationToAttribute;
use Rector\Symfony\JMS\Rector\Class_\AccessTypeAnnotationToAttributeRector;
use Rector\Symfony\JMS\Rector\Property\AccessorAnnotationToAttributeRector;
/**
 * @see https://github.com/schmittjoh/serializer/pull/1320
 * @see https://github.com/schmittjoh/serializer/pull/1332
 * @see https://github.com/schmittjoh/serializer/pull/1337
 */
return static function (RectorConfig $rectorConfig) : void {
    $rectorConfig->ruleWithConfiguration(AnnotationToAttributeRector::class, [new AnnotationToAttribute('JMS\\Serializer\\Annotation\\AccessorOrder'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Discriminator'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Exclude'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\ExclusionPolicy'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Expose'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Groups'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Inline'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\MaxDepth'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\PostDeserialize'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\PostSerialize'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\PreSerialize'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\ReadOnly'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\ReadOnlyProperty'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\SerializedName'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Since'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\SkipWhenEmpty'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Type'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Until'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\VirtualProperty'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlAttributeMap'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlAttribute'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlDiscriminator'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlElement'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlKeyValuePairs'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlList'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlMap'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlNamespace'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlRoot'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlValue')]);
    $rectorConfig->rules([AccessTypeAnnotationToAttributeRector::class, AccessorAnnotationToAttributeRector::class]);
};
 * namespaceAliases\RectorPrefix202507phpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameRectorPrefix202507 * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums