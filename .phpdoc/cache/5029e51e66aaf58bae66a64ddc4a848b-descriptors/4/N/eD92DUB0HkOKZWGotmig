1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-54040ab6809bad121b590f52b3ef1e1e
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameStub.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash b720414d52e0bd8ceece84ff27af78b3 * pathJvendor/phpunit/phpunit/src/Framework/MockObject/Runtime/Interface/Stub.php	 * source<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) Sebastian Bergmann <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Framework\MockObject;

use PHPUnit\Framework\MockObject\Builder\InvocationStubber;

/**
 * @method InvocationStubber method($constraint)
 *
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
interface Stub
{
}
 * namespaceAliases\PHPUnit\Framework\MockObjectphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen name
MockObject * includes * constants * functions
 * classes
 * interfaces"\PHPUnit\Framework\MockObject\Stub,phpDocumentor\Descriptor\InterfaceDescriptor#$,%Stub."

	

method -phpDocumentor\Descriptor\Tag\MethodDescriptor/	
 9 phpDocumentor\Descriptor\Tag\MethodDescriptor methodName/8 phpDocumentor\Descriptor\Tag\MethodDescriptor arguments
constraint+phpDocumentor\Descriptor\ArgumentDescriptor 3
 
	 
       * type%phpDocumentor\Reflection\Types\Mixed_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadic9:7 phpDocumentor\Descriptor\Tag\MethodDescriptor response-phpDocumentor\Descriptor\Tag\ReturnDescriptorreturn	  5&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$7\PHPUnit\Framework\MockObject\Builder\InvocationStubber%InvocationStubber5 phpDocumentor\Descriptor\Tag\MethodDescriptor staticC phpDocumentor\Descriptor\Tag\MethodDescriptor hasReturnByReferenceno-named-arguments E	QParameter names are not covered by the backward compatibility promise for PHPUnit 
""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber GHI  
 * parents(
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums