1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-04c1c60db1d608742a905b7c7e9cdfae
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Filter.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash b60d6265454c244027f57f1c00f29b28 * path7vendor/myclabs/deep-copy/src/DeepCopy/Filter/Filter.php	 * source\<?php

namespace DeepCopy\Filter;

/**
 * Filter to apply to a property while copying an object
 */
interface Filter
{
    /**
     * Applies the filter to the object.
     *
     * @param object   $object
     * @param string   $property
     * @param callable $objectCopier
     */
    public function apply($object, $property, $objectCopier);
}
 * namespaceAliases\DeepCopy\FilterphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameFilter * includes * constants * functions
 * classes
 * interfaces\DeepCopy\Filter\Filter,phpDocumentor\Descriptor\InterfaceDescriptor#$,%&&"
5Filter to apply to a property while copying an object	


""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber /01  
 * parents(
 * methodsapply)phpDocumentor\Descriptor\MethodDescriptor#$ \DeepCopy\Filter\Filter::apply()%44" !Applies the filter to the object.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen  * variableNameobject98	
 :&phpDocumentor\Reflection\Types\String_ =property98	
 :(phpDocumentor\Reflection\Types\Callable_4 phpDocumentor\Reflection\Types\Callable_ returnType 4 phpDocumentor\Reflection\Types\Callable_ parameters=objectCopier  /01 /01X 	 * parent" * arguments>+phpDocumentor\Descriptor\ArgumentDescriptor >
 
	")
  "4"5 3 phpDocumentor\Descriptor\ArgumentDescriptor method" :"+
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicKL@G @
 
	"-
  "4"5 H" :"/I JKLMKLDG D
 
	"1
  "4"5 H" :"3I JKLMKL	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference * final * abstract
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write   	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums