1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-4543728879ec6326e76ef1c2b279f7bc
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * namePhpRedisLock.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 8be9fcd7e9e850377a3c5c5d21ea7610 * path(vendor/illuminate/cache/PhpRedisLock.php	 * source)<?php

namespace Illuminate\Cache;

use Illuminate\Redis\Connections\PhpRedisConnection;

class PhpRedisLock extends RedisLock
{
    /**
     * Create a new phpredis lock instance.
     *
     * @param  \Illuminate\Redis\Connections\PhpRedisConnection  $redis
     * @param  string  $name
     * @param  int  $seconds
     * @param  string|null  $owner
     */
    public function __construct(PhpRedisConnection $redis, string $name, int $seconds, ?string $owner = null)
    {
        parent::__construct($redis, $name, $seconds, $owner);
    }

    /**
     * {@inheritDoc}
     */
    public function release()
    {
        return (bool) $this->redis->eval(
            LuaScripts::releaseLock(),
            1,
            $this->name,
            ...$this->redis->pack([$this->owner])
        );
    }
}
 * namespaceAliases\Illuminate\CachephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameCache * includes * constants * functions
 * classes\Illuminate\Cache\PhpRedisLock(phpDocumentor\Descriptor\ClassDescriptor#$+%PhpRedisLock-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./"0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$-\Illuminate\Cache\PhpRedisLock::__construct()%55" $Create a new phpredis lock instance.	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor9	
  * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$0\Illuminate\Redis\Connections\PhpRedisConnection%PhpRedisConnection * variableNameredis:9	
 ;&phpDocumentor\Reflection\Types\String_ @name:9	
 ;&phpDocumentor\Reflection\Types\Integer @seconds:9	
 ;'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types B $phpDocumentor\Reflection\Types\Null_ 4 phpDocumentor\Reflection\Types\AggregatedType token|@owner  ./0m./0 	 * parent" * argumentsA+phpDocumentor\Descriptor\ArgumentDescriptor A
 
	"$
  "7"8 3 phpDocumentor\Descriptor\ArgumentDescriptor method";"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicRSCN C
 
	")
  "7"8 O";"+P QRSTRSEN E
 
	"-
  "7"8 O";"/P QRSTRSKN K
 
	"1
  "7"8 O";"3PnullQRSTRS	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  release6#$)\Illuminate\Cache\PhpRedisLock::release()%``" 
{@inheritDoc}	

  ./0K./!0% L"MVWX Y23Z[\"R_  
 * properties(L#$\Illuminate\Cache\RedisLock%	RedisLock
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums