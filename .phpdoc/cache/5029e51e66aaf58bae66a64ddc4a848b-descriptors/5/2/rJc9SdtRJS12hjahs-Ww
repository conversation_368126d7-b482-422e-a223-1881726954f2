1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-59800ac8a18da2af07dc6438d8988cb8
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameVirtualRequestStack.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 29093df45e7620c81d1ec4f14031fd83 * path8vendor/symfony/http-kernel/Debug/VirtualRequestStack.php	 * source,<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\Debug;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * A stack able to deal with virtual requests.
 *
 * @internal
 *
 * <AUTHOR> Pietri <<EMAIL>>
 */
final class VirtualRequestStack extends RequestStack
{
    public function __construct(
        private readonly RequestStack $decorated,
    ) {
    }

    public function push(Request $request): void
    {
        if ($request->attributes->has('_virtual_type')) {
            if ($this->decorated->getCurrentRequest()) {
                throw new \LogicException('Cannot mix virtual and HTTP requests.');
            }

            parent::push($request);

            return;
        }

        $this->decorated->push($request);
    }

    public function pop(): ?Request
    {
        return $this->decorated->pop() ?? parent::pop();
    }

    public function getCurrentRequest(): ?Request
    {
        return $this->decorated->getCurrentRequest() ?? parent::getCurrentRequest();
    }

    public function getMainRequest(): ?Request
    {
        return $this->decorated->getMainRequest() ?? parent::getMainRequest();
    }

    public function getParentRequest(): ?Request
    {
        return $this->decorated->getParentRequest() ?? parent::getParentRequest();
    }
}
 * namespaceAliases#\Symfony\Component\HttpKernel\DebugphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameDebug * includes * constants * functions
 * classes
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums