1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-e59d65ff9f957a014686c7f77edb35a2
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
StringSet.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 8ef993528b709209c9b472491cc8dd93 * pathAvendor/pestphp/pest-plugin-mutate/src/Mutators/Sets/StringSet.php	 * source2<?php

declare(strict_types=1);

namespace Pest\Mutate\Mutators\Sets;

use Pest\Mutate\Contracts\MutatorSet;
use Pest\Mutate\Mutators\Concerns\HasName;
use Pest\Mutate\Mutators\String\ConcatRemoveLeft;
use Pest\Mutate\Mutators\String\ConcatRemoveRight;
use Pest\Mutate\Mutators\String\ConcatSwitchSides;
use Pest\Mutate\Mutators\String\EmptyStringToNotEmpty;
use Pest\Mutate\Mutators\String\NotEmptyStringToEmpty;
use Pest\Mutate\Mutators\String\StrEndsWithToStrStartsWith;
use Pest\Mutate\Mutators\String\StrStartsWithToStrEndsWith;
use Pest\Mutate\Mutators\String\UnwrapChop;
use Pest\Mutate\Mutators\String\UnwrapChunkSplit;
use Pest\Mutate\Mutators\String\UnwrapHtmlentities;
use Pest\Mutate\Mutators\String\UnwrapHtmlEntityDecode;
use Pest\Mutate\Mutators\String\UnwrapHtmlspecialchars;
use Pest\Mutate\Mutators\String\UnwrapHtmlspecialcharsDecode;
use Pest\Mutate\Mutators\String\UnwrapLcfirst;
use Pest\Mutate\Mutators\String\UnwrapLtrim;
use Pest\Mutate\Mutators\String\UnwrapMd5;
use Pest\Mutate\Mutators\String\UnwrapNl2br;
use Pest\Mutate\Mutators\String\UnwrapRtrim;
use Pest\Mutate\Mutators\String\UnwrapStripTags;
use Pest\Mutate\Mutators\String\UnwrapStrIreplace;
use Pest\Mutate\Mutators\String\UnwrapStrPad;
use Pest\Mutate\Mutators\String\UnwrapStrRepeat;
use Pest\Mutate\Mutators\String\UnwrapStrReplace;
use Pest\Mutate\Mutators\String\UnwrapStrrev;
use Pest\Mutate\Mutators\String\UnwrapStrShuffle;
use Pest\Mutate\Mutators\String\UnwrapStrtolower;
use Pest\Mutate\Mutators\String\UnwrapStrtoupper;
use Pest\Mutate\Mutators\String\UnwrapSubstr;
use Pest\Mutate\Mutators\String\UnwrapTrim;
use Pest\Mutate\Mutators\String\UnwrapUcfirst;
use Pest\Mutate\Mutators\String\UnwrapUcwords;
use Pest\Mutate\Mutators\String\UnwrapWordwrap;

class StringSet implements MutatorSet
{
    use HasName;

    /**
     * {@inheritDoc}
     */
    public static function mutators(): array
    {
        return [
            ConcatRemoveLeft::class,
            ConcatRemoveRight::class,
            ConcatSwitchSides::class,
            EmptyStringToNotEmpty::class,
            // NotEmptyStringToEmpty::class,
            StrStartsWithToStrEndsWith::class,
            StrEndsWithToStrStartsWith::class,
            UnwrapChop::class,
            UnwrapChunkSplit::class,
            UnwrapHtmlentities::class,
            UnwrapHtmlEntityDecode::class,
            UnwrapHtmlspecialchars::class,
            UnwrapHtmlspecialcharsDecode::class,
            UnwrapLcfirst::class,
            UnwrapLtrim::class,
            UnwrapMd5::class,
            UnwrapNl2br::class,
            UnwrapRtrim::class,
            UnwrapStripTags::class,
            UnwrapStrIreplace::class,
            UnwrapStrPad::class,
            UnwrapStrRepeat::class,
            UnwrapStrReplace::class,
            UnwrapStrrev::class,
            UnwrapStrShuffle::class,
            UnwrapStrtolower::class,
            UnwrapStrtoupper::class,
            UnwrapSubstr::class,
            UnwrapTrim::class,
            UnwrapUcfirst::class,
            UnwrapUcwords::class,
            UnwrapWordwrap::class,
        ];
    }
}
 * namespaceAliases\Pest\Mutate\Mutators\SetsphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameSets * includes * constants * functions
 * classes$\Pest\Mutate\Mutators\Sets\StringSet(phpDocumentor\Descriptor\ClassDescriptor#$+%	StringSet-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber*/ phpDocumentor\Reflection\Location columnNumber ./V0   * readOnly * final * abstract
 * methodsmutators)phpDocumentor\Descriptor\MethodDescriptor#$0\Pest\Mutate\Mutators\Sets\StringSet::mutators()%55" 
{@inheritDoc}	

  ./10<./U0. 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Array_ * valueType%phpDocumentor\Reflection\Types\Mixed_ 
 * keyType  * defaultKeyType'phpDocumentor\Reflection\Types\Compound4 phpDocumentor\Reflection\Types\AggregatedType types &phpDocumentor\Reflection\Types\String_ &phpDocumentor\Reflection\Types\Integer 4 phpDocumentor\Reflection\Types\AggregatedType token|? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(9 
 * implements!\Pest\Mutate\Contracts\MutatorSet#$Q%
MutatorSet
 * usedTraits&\Pest\Mutate\Mutators\Concerns\HasName#$T%HasName 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums