1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-babbefd725477e2872526fbd0e46f9ee
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name UnexpectedNullMatchException.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash a9de2ea028776c731f1057b7fc14fc12 * pathNvendor/rector/rector/vendor/composer/pcre/src/UnexpectedNullMatchException.php	 * source<?php

/*
 * This file is part of composer/pcre.
 *
 * (c) Composer <https://github.com/composer>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */
namespace RectorPrefix202507\Composer\Pcre;

class UnexpectedNullMatchException extends PcreException
{
    public static function fromFunction($function, $pattern)
    {
        throw new \LogicException('fromFunction should not be called on ' . self::class . ', use ' . PcreException::class);
    }
}
 * namespaceAliases!\RectorPrefix202507\Composer\PcrephpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen namePcre * includes * constants * functions
 * classes>\RectorPrefix202507\Composer\Pcre\UnexpectedNullMatchException(phpDocumentor\Descriptor\ClassDescriptor#$+%UnexpectedNullMatchException-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber
/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methodsfromFunction)phpDocumentor\Descriptor\MethodDescriptor#$N\RectorPrefix202507\Composer\Pcre\UnexpectedNullMatchException::fromFunction()%55" 
	 
param  ./0T./0 	 * parent" * argumentsfunction+phpDocumentor\Descriptor\ArgumentDescriptor ;
 
	 
  " "! 3 phpDocumentor\Descriptor\ArgumentDescriptor method" * type%phpDocumentor\Reflection\Types\Mixed_ 
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicBCpattern< E
 
	 
  " "! =">? @ ABCDBC	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType? ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * properties(9#$/\RectorPrefix202507\Composer\Pcre\PcreException%
PcreException
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums