1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-b276a7d850b92abeaf8ed641c8de3883
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * name
Exception.php * namespace

 * packageApplication
 * summary'Copyright (c) 2018-2025 Andreas <PERSON> * description7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate{For the full copyright and license information, please view
the LICENSE.md file that was distributed with this source code.3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags * tags#phpDocumentor\Descriptor\Collection * itemssee *phpDocumentor\Descriptor\Tag\SeeDescriptor


 5 phpDocumentor\Descriptor\Tag\SeeDescriptor reference4phpDocumentor\Reflection\DocBlock\Tags\Reference\Url9 phpDocumentor\Reflection\DocBlock\Tags\Reference\Url uri+https://github.com/ergebnis/json-normalizerpackage &phpDocumentor\Descriptor\TagDescriptor

  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash f162228e789be5a54efcc14cf563ff00 * path;vendor/ergebnis/json-normalizer/src/Exception/Exception.php	 * sourcek<?php

declare(strict_types=1);

/**
 * Copyright (c) 2018-2025 Andreas Möller
 *
 * For the full copyright and license information, please view
 * the LICENSE.md file that was distributed with this source code.
 *
 * @see https://github.com/ergebnis/json-normalizer
 */

namespace Ergebnis\Json\Normalizer\Exception;

interface Exception extends \Throwable
{
}
 * namespaceAliases#\Ergebnis\Json\Normalizer\ExceptionphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen*$ phpDocumentor\Reflection\Fqsen name	Exception * includes * constants * functions
 * classes
 * interfaces-\Ergebnis\Json\Normalizer\Exception\Exception,phpDocumentor\Descriptor\InterfaceDescriptor+,4-..*


 ""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber  678 !" 
 * parents
\Throwable+,:-	Throwable0
 * methods 	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums