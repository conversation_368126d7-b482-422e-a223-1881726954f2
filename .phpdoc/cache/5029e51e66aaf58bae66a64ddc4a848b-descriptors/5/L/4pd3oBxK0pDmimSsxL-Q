1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-782b318280b57b28334837226bcfc47d
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameUnwrapArrayMap.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash 1f1793f210b69649ec4369acf77d392d * pathGvendor/pestphp/pest-plugin-mutate/src/Mutators/Array/UnwrapArrayMap.php	 * source<?php

declare(strict_types=1);

namespace Pest\Mutate\Mutators\Array;

use Pest\Mutate\Mutators\Abstract\AbstractFunctionCallUnwrapMutator;
use PhpParser\Node;
use PhpParser\Node\Expr\FuncCall;

class UnwrapArrayMap extends AbstractFunctionCallUnwrapMutator
{
    public const SET = 'Array';

    public const DESCRIPTION = 'Unwraps `array_map` calls.';

    public const DIFF = <<<'DIFF'
        $a = array_map(fn ($value) => $value + 1, [1, 2, 3]);  // [tl! remove]
        $a = [1, 2, 3];  // [tl! add]
        DIFF;

    public static function functionName(): string
    {
        return 'array_map';
    }

    public static function mutate(Node $node): Node
    {
        /** @var FuncCall $node */
        return $node->args[1]->value; // @phpstan-ignore-line
    }
}
 * namespaceAliases\Pest\Mutate\Mutators\ArrayphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen nameArray * includes * constants * functions
 * classes*\Pest\Mutate\Mutators\Array\UnwrapArrayMap(phpDocumentor\Descriptor\ClassDescriptor#$+%UnwrapArrayMap-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./ 0   * readOnly * final * abstract
 * methodsfunctionName)phpDocumentor\Descriptor\MethodDescriptor#$:\Pest\Mutate\Mutators\Array\UnwrapArrayMap::functionName()%55" 
	 
  ./0./0b 	 * parent" * arguments	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType&phpDocumentor\Reflection\Types\String_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  mutate6#$4\Pest\Mutate\Mutators\Array\UnwrapArrayMap::mutate()%DD" 
	 
param  ./0i./0 8"9node+phpDocumentor\Descriptor\ArgumentDescriptor G
 
	 
  "*"+ 3 phpDocumentor\Descriptor\ArgumentDescriptor method"% * type&phpDocumentor\Reflection\Types\Object_- phpDocumentor\Reflection\Types\Object_ fqsen#$\PhpParser\Node%Node
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicQR:;KL#$M%N=23>?@"$C  
 * properties(SET+phpDocumentor\Descriptor\ConstantDescriptor#$/\Pest\Mutate\Mutators\Array\UnwrapArrayMap::SET%UU+ 
	 
  ./
0 ./
0   * value'Array'2>?@"$C 8"J  DESCRIPTIONV#$7\Pest\Mutate\Mutators\Array\UnwrapArrayMap::DESCRIPTION%ZZ+ 
	 
  ./0 ./0  X'Unwraps `array_map` calls.'2>?@"$C 8"J  DIFFV#$0\Pest\Mutate\Mutators\Array\UnwrapArrayMap::DIFF%]]+ 
	 
  ./0 ./0  Xs<<<'DIFF'
$a = array_map(fn ($value) => $value + 1, [1, 2, 3]);  // [tl! remove]
$a = [1, 2, 3];  // [tl! add]
DIFF2>?@"$C 8"J  8#$@\Pest\Mutate\Mutators\Abstract\AbstractFunctionCallUnwrapMutator%!AbstractFunctionCallUnwrapMutator
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums