1755757226
5029e51e66aaf58bae66a64ddc4a848b-phpdoc%3AphpDocumentor-projectDescriptor-files-1d2bb047e6120dcf1bc9cace867be5e4
   'phpDocumentor\Descriptor\FileDescriptor * fqsen 5 phpDocumentor\Descriptor\DescriptorAbstract metadata  * nameParserConfig.php * namespace

 * packageApplication
 * summary
 * description  * tags#phpDocumentor\Descriptor\Collection * itemspackage &phpDocumentor\Descriptor\TagDescriptor
	7phpDocumentor\Descriptor\DocBlock\DescriptionDescriptorD phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description-phpDocumentor\Reflection\DocBlock\Description; phpDocumentor\Reflection\DocBlock\Description bodyTemplate3 phpDocumentor\Reflection\DocBlock\Description tagsC phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags  * fileDescriptor  * line  * startLocation  * endLocation 	 * errors * inheritedElement  * hash c85a224d2a27878cf0287086e6ff2668 * path1vendor/phpstan/phpdoc-parser/src/ParserConfig.php	 * source'<?php declare(strict_types = 1);

namespace PHPStan\PhpDocParser;

class ParserConfig
{

	public bool $useLinesAttributes;

	public bool $useIndexAttributes;

	public bool $useCommentsAttributes;

	/**
	 * @param array{lines?: bool, indexes?: bool, comments?: bool} $usedAttributes
	 */
	public function __construct(array $usedAttributes)
	{
		$this->useLinesAttributes = $usedAttributes['lines'] ?? false;
		$this->useIndexAttributes = $usedAttributes['indexes'] ?? false;
		$this->useCommentsAttributes = $usedAttributes['comments'] ?? false;
	}

}
 * namespaceAliases\PHPStan\PhpDocParserphpDocumentor\Reflection\Fqsen% phpDocumentor\Reflection\Fqsen fqsen"$ phpDocumentor\Reflection\Fqsen namePhpDocParser * includes * constants * functions
 * classes"\PHPStan\PhpDocParser\ParserConfig(phpDocumentor\Descriptor\ClassDescriptor#$+%ParserConfig-"
	 

""  !phpDocumentor\Reflection\Location- phpDocumentor\Reflection\Location lineNumber/ phpDocumentor\Reflection\Location columnNumber ./0   * readOnly * final * abstract
 * methods__construct)phpDocumentor\Descriptor\MethodDescriptor#$1\PHPStan\PhpDocParser\ParserConfig::__construct()%55" 
	

param ,phpDocumentor\Descriptor\Tag\ParamDescriptor8	
  * type/phpDocumentor\Reflection\PseudoTypes\ArrayShape6 phpDocumentor\Reflection\PseudoTypes\ArrayShape items 3phpDocumentor\Reflection\PseudoTypes\ArrayShapeItem3 phpDocumentor\Reflection\PseudoTypes\ShapeItem keylines5 phpDocumentor\Reflection\PseudoTypes\ShapeItem value&phpDocumentor\Reflection\Types\Boolean 8 phpDocumentor\Reflection\PseudoTypes\ShapeItem optional=>indexes@A B=>comments@A B * variableNameusedAttributes  ./0 ./0" 	 * parent" * argumentsF+phpDocumentor\Descriptor\ArgumentDescriptor F
 
	"$
  "."/ 3 phpDocumentor\Descriptor\ArgumentDescriptor method":"&
 * default  * byReference2phpDocumentor\Descriptor\ValueObjects\IsApplicable9 phpDocumentor\Descriptor\ValueObjects\IsApplicable value
 * isVariadicMN	 * static5 phpDocumentor\Descriptor\MethodDescriptor returnType%phpDocumentor\Reflection\Types\Mixed_ ? phpDocumentor\Descriptor\MethodDescriptor hasReturnByReference23
 * visibility0phpDocumentor\Descriptor\ValueObjects\Visibility6 phpDocumentor\Descriptor\ValueObjects\Visibility read8phpDocumentor\Descriptor\ValueObjects\VisibilityModifier'PUBLIC7 phpDocumentor\Descriptor\ValueObjects\Visibility write  
 * propertiesuseLinesAttributes+phpDocumentor\Descriptor\PropertyDescriptor#$7\PHPStan\PhpDocParser\ParserConfig::$useLinesAttributes%[[+ 
	 
var  ./0 ./0  G"P5 phpDocumentor\Descriptor\PropertyDescriptor readOnly6 phpDocumentor\Descriptor\PropertyDescriptor writeOnly2 phpDocumentor\Descriptor\PropertyDescriptor hooks6 phpDocumentor\Descriptor\PropertyDescriptor isVirtualTUV":Y :A K  useIndexAttributes\#$7\PHPStan\PhpDocParser\ParserConfig::$useIndexAttributes%cc+ 
	 
^  ./
0 ./
0  G"P_`abTUV":Y :A K  useCommentsAttributes\#$:\PHPStan\PhpDocParser\ParserConfig::$useCommentsAttributes%ee+ 
	 
^  ./0 ./0  G"P_`abTUV":Y :A K  (G 
 * implements
 * usedTraits 
 * interfaces	 * traits
 * markers. phpDocumentor\Descriptor\FileDescriptor enums