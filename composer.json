{"name": "s-a-c/validate-links", "description": "Comprehensive PHP Link Validation and Remediation Tools - Laravel Zero Edition", "type": "project", "keywords": ["link-validation", "documentation", "laravel-zero", "cli"], "license": "MIT", "authors": [{"name": "StandAloneComplex", "email": "<EMAIL>"}], "bin": ["validate-links"], "require": {"php": "^8.4", "ext-json": "*", "ext-mbstring": "*", "ext-curl": "*", "illuminate/http": "^12.0", "laravel-zero/framework": "^12.0", "laravel/prompts": "^0.3", "symfony/dom-crawler": "^7.2"}, "require-dev": {"driftingly/rector-laravel": "^2.0", "ergebnis/composer-normalize": "^2.47", "friendsofphp/php-cs-fixer": "^3.84", "larastan/larastan": "^3.6", "laravel/pint": "^1.22", "mockery/mockery": "^1.6.12", "nunomaduro/phpinsights": "^2.13", "orklah/psalm-strict-equality": "^3.1", "peckphp/peck": "^0.1.3", "pestphp/pest": "^3.8.2", "pestphp/pest-plugin-arch": "^3.1", "pestphp/pest-plugin-stressless": "^3.1", "pestphp/pest-plugin-type-coverage": "^3.6", "phpstan/phpstan-deprecation-rules": "^2.0", "psalm/plugin-laravel": "^3.0", "rector/rector": "^2.1", "rector/type-perfect": "^2.1", "roave/psalm-html-output": "^1.1", "roave/security-advisories": "dev-latest", "vimeo/psalm": "^6.13"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"test": "pest", "test:coverage": "pest --coverage --coverage-html=coverage-html", "test:unit": "pest tests/Unit/", "test:feature": "pest tests/Feature/", "analyse": "phpstan analyse app/ --level=8", "cs-fix": "pint", "cs-check": "pint --test", "refactor": "rector process", "refactor-dry": "rector process --dry-run", "refactor-types": "rector process --config=rector-type-safety.php", "refactor-types-dry": "rector process --config=rector-type-safety.php --dry-run", "quality": ["@cs-fix", "@analyse", "@refactor-dry", "@test"], "migrate:setup": ["php artisan config:publish validate-links", "php artisan vendor:publish --tag=validate-links-config"]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "infection/extension-installer": true, "pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}