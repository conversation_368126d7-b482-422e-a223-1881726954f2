# Application Configuration
APP_NAME="Validate Links"
APP_ENV=local
APP_DEBUG=true
APP_VERSION=1.0.0

# Link Validation Configuration
VALIDATE_LINKS_CACHE_ENABLED=true
VALIDATE_LINKS_CACHE_TTL=3600
VALIDATE_LINKS_CASE_SENSITIVE=false
VALIDATE_LINKS_CHECK_EXTERNAL=false
VALIDATE_LINKS_COLORS=true
VALIDATE_LINKS_CONCURRENCY=5
VALIDATE_LINKS_CONCURRENT_REQUESTS=10
VALIDATE_LINKS_FILES_PER_CHUNK=100
VALIDATE_LINKS_INCLUDE_HIDDEN=false
VALIDATE_LINKS_MAX_BROKEN=50
VALIDATE_LINKS_MAX_DEPTH=0
VALIDATE_LINKS_MAX_EXECUTION_TIME=300
VALIDATE_LINKS_MAX_REDIRECTS=5
VALIDATE_LINKS_MEMORY_LIMIT=256M
VALIDATE_LINKS_PARALLEL_BATCH_SIZE=25
VALIDATE_LINKS_PARALLEL_ENABLED=true
VALIDATE_LINKS_PARALLEL_WORKERS=4
VALIDATE_LINKS_TIMEOUT=30
VALIDATE_LINKS_USER_AGENT="ValidateLinks/1.0"

# Cache Configuration
CACHE_DRIVER=file
CACHE_TTL=3600

# Security Configuration
SECURITY_ALLOW_LOCALHOST=true
SECURITY_ALLOW_PRIVATE_IPS=false
SECURITY_BLOCKED_DOMAINS=""
SECURITY_ALLOWED_PROTOCOLS="http,https,ftp"

# Output Configuration
DEFAULT_OUTPUT_FORMAT=console
REPORT_OUTPUT_PATH="./reports"

# Performance Configuration
MEMORY_LIMIT=512M
MAX_EXECUTION_TIME=300

# Logging Configuration
LOG_CHANNEL=single
LOG_LEVEL=info
