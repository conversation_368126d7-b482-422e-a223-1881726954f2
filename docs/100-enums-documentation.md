# 10. Enums Documentation

**Date:** July 22, 2025  
**Project:** Laravel Zero validate-links Implementation  
**Compliance:** `.ai/guidelines.md` and `.ai/guidelines/` standards

## 10.1. Overview

This document provides comprehensive documentation for PHP 8.4+ enums used in the validate-links Laravel Zero application. Enums provide type safety and better code organization for enumerated values throughout the application.

### 10.1.1. Enum Implementation Strategy

The application uses PHP 8.4+ backed enums to replace string constants and improve type safety:

**Benefits of Enum Usage:**
- **Type Safety:** Compile-time validation of enumerated values
- **IDE Support:** Better autocomplete and refactoring capabilities
- **Documentation:** Self-documenting code with clear value constraints
- **Extensibility:** Easy addition of methods and properties to enum cases

**Enum Categories:**
- **Validation Scopes:** Define types of link validation to perform
- **Output Formats:** Specify report generation formats
- **Link Status:** Represent validation results and error states
- **Command Options:** Standardize command-line option values

## 10.2. ValidationScope Enum

### 10.2.1. Purpose and Usage

**Purpose:** Define the scope of link validation operations with type safety.

**Location:** `app/Enums/ValidationScope.php`

**Complete Implementation:**

```php
<?php

declare(strict_types=1);

namespace App\Enums;

enum ValidationScope: string
{
    case INTERNAL = 'internal';
    case EXTERNAL = 'external';
    case ANCHOR = 'anchor';
    case CROSS_REFERENCE = 'cross-reference';
    case ALL = 'all';

    /**
     * Get all scope values as array.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all scope names as array.
     */
    public static function names(): array
    {
        return array_column(self::cases(), 'name');
    }

    /**
     * Check if scope includes external validation.
     */
    public function includesExternal(): bool
    {
        return $this === self::EXTERNAL || $this === self::ALL;
    }

    /**
     * Check if scope includes internal validation.
     */
    public function includesInternal(): bool
    {
        return $this === self::INTERNAL || $this === self::ALL;
    }

    /**
     * Check if scope includes anchor validation.
     */
    public function includesAnchor(): bool
    {
        return $this === self::ANCHOR || $this === self::ALL;
    }

    /**
     * Check if scope includes cross-reference validation.
     */
    public function includesCrossReference(): bool
    {
        return $this === self::CROSS_REFERENCE || $this === self::ALL;
    }

    /**
     * Get human-readable description.
     */
    public function getDescription(): string
    {
        return match($this) {
            self::INTERNAL => 'Validate internal file and directory links',
            self::EXTERNAL => 'Validate external HTTP/HTTPS links',
            self::ANCHOR => 'Validate anchor links within documents',
            self::CROSS_REFERENCE => 'Validate cross-references between files',
            self::ALL => 'Validate all link types (comprehensive validation)'
        };
    }

    /**
     * Get validation priority (lower number = higher priority).
     */
    public function getPriority(): int
    {
        return match($this) {
            self::INTERNAL => 1,
            self::ANCHOR => 2,
            self::CROSS_REFERENCE => 3,
            self::EXTERNAL => 4,
            self::ALL => 0
        };
    }

    /**
     * Create from string value with validation.
     */
    public static function fromString(string $value): self
    {
        return self::from(strtolower($value));
    }

    /**
     * Get scopes that should be included for this scope.
     */
    public function getIncludedScopes(): array
    {
        return match($this) {
            self::ALL => [self::INTERNAL, self::EXTERNAL, self::ANCHOR, self::CROSS_REFERENCE],
            default => [$this]
        };
    }
}
```

### 10.2.2. Usage Examples

```php
// Basic usage
$scope = ValidationScope::INTERNAL;
echo $scope->value; // 'internal'
echo $scope->getDescription(); // 'Validate internal file and directory links'

// Type-safe command options
function validateWithScope(ValidationScope $scope): void
{
    if ($scope->includesExternal()) {
        // Handle external validation
    }
}

// Array operations
$allScopes = ValidationScope::values(); // ['internal', 'external', 'anchor', 'cross-reference', 'all']
$scopeNames = ValidationScope::names(); // ['INTERNAL', 'EXTERNAL', 'ANCHOR', 'CROSS_REFERENCE', 'ALL']

// Validation configuration
$config = ValidationConfig::withDefaults([
    'scope' => ValidationScope::ALL->getIncludedScopes()
]);

// Command line integration
$inputScope = ValidationScope::fromString($input); // Type-safe conversion
```

## 10.3. OutputFormat Enum

### 10.3.1. Purpose and Usage

**Purpose:** Define output formats for reports and validation results.

**Location:** `app/Enums/OutputFormat.php`

**Complete Implementation:**

```php
<?php

declare(strict_types=1);

namespace App\Enums;

enum OutputFormat: string
{
    case CONSOLE = 'console';
    case JSON = 'json';
    case HTML = 'html';
    case MARKDOWN = 'markdown';
    case XML = 'xml';
    case CSV = 'csv';

    /**
     * Get file extension for format.
     */
    public function getExtension(): string
    {
        return match($this) {
            self::JSON => 'json',
            self::HTML => 'html',
            self::MARKDOWN => 'md',
            self::XML => 'xml',
            self::CSV => 'csv',
            self::CONSOLE => 'txt'
        };
    }

    /**
     * Get MIME type for format.
     */
    public function getMimeType(): string
    {
        return match($this) {
            self::JSON => 'application/json',
            self::HTML => 'text/html',
            self::MARKDOWN => 'text/markdown',
            self::XML => 'application/xml',
            self::CSV => 'text/csv',
            self::CONSOLE => 'text/plain'
        };
    }

    /**
     * Check if format supports structured data.
     */
    public function isStructured(): bool
    {
        return match($this) {
            self::JSON, self::XML, self::CSV => true,
            self::HTML, self::MARKDOWN, self::CONSOLE => false
        };
    }

    /**
     * Check if format supports styling.
     */
    public function supportsFormatting(): bool
    {
        return match($this) {
            self::HTML, self::MARKDOWN, self::CONSOLE => true,
            self::JSON, self::XML, self::CSV => false
        };
    }

    /**
     * Get formatter class name.
     */
    public function getFormatterClass(): string
    {
        return match($this) {
            self::CONSOLE => 'App\\Services\\Formatters\\ConsoleFormatter',
            self::JSON => 'App\\Services\\Formatters\\JsonFormatter',
            self::HTML => 'App\\Services\\Formatters\\HtmlFormatter',
            self::MARKDOWN => 'App\\Services\\Formatters\\MarkdownFormatter',
            self::XML => 'App\\Services\\Formatters\\XmlFormatter',
            self::CSV => 'App\\Services\\Formatters\\CsvFormatter'
        };
    }

    /**
     * Get default filename for format.
     */
    public function getDefaultFilename(): string
    {
        return match($this) {
            self::CONSOLE => 'validation-output.txt',
            self::JSON => 'validation-report.json',
            self::HTML => 'validation-report.html',
            self::MARKDOWN => 'VALIDATION_REPORT.md',
            self::XML => 'validation-report.xml',
            self::CSV => 'validation-data.csv'
        };
    }

    /**
     * Check if format is suitable for CI/CD integration.
     */
    public function isCiCdFriendly(): bool
    {
        return match($this) {
            self::JSON, self::XML, self::CSV => true,
            self::HTML, self::MARKDOWN, self::CONSOLE => false
        };
    }
}
```

### 10.3.2. Usage Examples

```php
// Format selection
$format = OutputFormat::JSON;
$filename = $format->getDefaultFilename(); // 'validation-report.json'
$mimeType = $format->getMimeType(); // 'application/json'

// Conditional logic based on format capabilities
if ($format->isStructured()) {
    // Handle structured data output
    $data = $result->toArray();
} else {
    // Handle formatted text output
    $data = $result->toFormattedString();
}

// Formatter instantiation
$formatterClass = $format->getFormatterClass();
$formatter = app($formatterClass);

// CI/CD integration check
if ($format->isCiCdFriendly()) {
    // Use for automated processing
    $this->info('Format suitable for CI/CD integration');
}
```

## 10.4. LinkStatus Enum

### 10.4.1. Purpose and Usage

**Purpose:** Represent the validation status of individual links with detailed error information.

**Location:** `app/Enums/LinkStatus.php`

**Complete Implementation:**

```php
<?php

declare(strict_types=1);

namespace App\Enums;

enum LinkStatus: string
{
    case VALID = 'valid';
    case BROKEN = 'broken';
    case TIMEOUT = 'timeout';
    case FORBIDDEN = 'forbidden';
    case NOT_FOUND = 'not_found';
    case INVALID_FORMAT = 'invalid_format';
    case SECURITY_VIOLATION = 'security_violation';
    case REDIRECT_LOOP = 'redirect_loop';
    case SSL_ERROR = 'ssl_error';
    case DNS_ERROR = 'dns_error';

    /**
     * Check if status indicates a problem.
     */
    public function isBroken(): bool
    {
        return $this !== self::VALID;
    }

    /**
     * Check if status indicates a temporary issue.
     */
    public function isTemporary(): bool
    {
        return match($this) {
            self::TIMEOUT, self::DNS_ERROR => true,
            default => false
        };
    }

    /**
     * Check if status indicates a security issue.
     */
    public function isSecurityIssue(): bool
    {
        return match($this) {
            self::SECURITY_VIOLATION, self::SSL_ERROR => true,
            default => false
        };
    }

    /**
     * Get human-readable description.
     */
    public function getDescription(): string
    {
        return match($this) {
            self::VALID => 'Link is valid and accessible',
            self::BROKEN => 'Link is broken or inaccessible',
            self::TIMEOUT => 'Request timed out',
            self::FORBIDDEN => 'Access forbidden (403)',
            self::NOT_FOUND => 'Resource not found (404)',
            self::INVALID_FORMAT => 'Invalid URL format',
            self::SECURITY_VIOLATION => 'Security policy violation',
            self::REDIRECT_LOOP => 'Redirect loop detected',
            self::SSL_ERROR => 'SSL/TLS certificate error',
            self::DNS_ERROR => 'DNS resolution failed'
        };
    }

    /**
     * Get severity level (1 = low, 5 = critical).
     */
    public function getSeverity(): int
    {
        return match($this) {
            self::VALID => 0,
            self::TIMEOUT, self::DNS_ERROR => 2,
            self::NOT_FOUND, self::INVALID_FORMAT => 3,
            self::FORBIDDEN, self::REDIRECT_LOOP => 4,
            self::BROKEN, self::SECURITY_VIOLATION, self::SSL_ERROR => 5
        };
    }

    /**
     * Get recommended action.
     */
    public function getRecommendedAction(): string
    {
        return match($this) {
            self::VALID => 'No action required',
            self::TIMEOUT => 'Retry validation or increase timeout',
            self::FORBIDDEN => 'Check access permissions or authentication',
            self::NOT_FOUND => 'Update link URL or remove broken link',
            self::INVALID_FORMAT => 'Fix URL syntax and format',
            self::SECURITY_VIOLATION => 'Review security policies and URL safety',
            self::REDIRECT_LOOP => 'Check redirect configuration',
            self::SSL_ERROR => 'Verify SSL certificate validity',
            self::DNS_ERROR => 'Check domain name and DNS configuration',
            self::BROKEN => 'Investigate and fix underlying issue'
        };
    }

    /**
     * Get console color for status display.
     */
    public function getConsoleColor(): string
    {
        return match($this) {
            self::VALID => 'green',
            self::TIMEOUT, self::DNS_ERROR => 'yellow',
            self::NOT_FOUND, self::INVALID_FORMAT => 'orange',
            default => 'red'
        };
    }
}
```

### 10.4.3. Usage Examples

```php
// Status evaluation
$status = LinkStatus::NOT_FOUND;
if ($status->isBroken()) {
    $this->error("Link validation failed: " . $status->getDescription());
    $this->info("Recommended action: " . $status->getRecommendedAction());
}

// Severity-based handling
$severity = $status->getSeverity();
if ($severity >= 4) {
    // Handle critical issues
    $this->logCriticalError($status);
}

// Console output with colors
$color = $status->getConsoleColor();
$this->line("<fg={$color}>{$status->value}</>");

// Temporary issue retry logic
if ($status->isTemporary()) {
    // Implement retry mechanism
    $this->retryValidation($link);
}
```

---

*This enums documentation provides comprehensive type-safe enumeration implementations for the validate-links application.*

---

## 📖 Navigation

**[⬅️ Previous: Implementation Plan](090-implementation-plan.md)** | **[🏠 Documentation Home](README.md)** | **[🔝 Top](#10-enums-documentation)**
