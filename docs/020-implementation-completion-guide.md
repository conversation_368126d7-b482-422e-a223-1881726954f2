# 2. Implementation Completion Guide

**Date:** July 21, 2025  
**Project:** Laravel Zero validate-links Implementation  
**Compliance:** `.ai/guidelines.md` and `.ai/guidelines/` standards

## 2.1. Overview

This guide provides step-by-step instructions for completing the Laravel Zero validate-links project implementation. Based on the [Project Status Assessment](010-project-status.md), the project is approximately 75-80% complete with critical gaps in service implementations, interactive features, and comprehensive testing.

### 2.1.0. Documentation Cross-References

This implementation guide serves as the **single source of truth** for all source code implementations. For additional context and detailed explanations, refer to these complementary documentation files:

**📚 Related Documentation:**
- **[Complete Code Documentation](060-code-documentation.md)** - Detailed class documentation with usage patterns
- **[Testing Documentation Suite](070-testing-documentation.md)** - Comprehensive testing strategies and examples
- **[CI/CD Implementation Guide](080-cicd-documentation.md)** - Deployment and automation procedures
- **[Architecture Overview](030-architecture-overview.md)** - System design and architectural patterns
- **[API Reference](040-api-reference.md)** - Command-line interface documentation
- **[Usage Guide](050-usage-guide.md)** - User scenarios and practical examples

**🔗 Quick Navigation:**
- [Service Implementations](#23-phase-1-complete-service-implementations) → [Code Documentation: Services](060-code-documentation.md#63-service-contracts-documentation)
- [Exception Classes](#224-exception-classes-implementation) → [Code Documentation: Exceptions](060-code-documentation.md#66-exception-classes-documentation)
- [Testing Implementation](#24-phase-3-expand-test-coverage) → [Testing Documentation](070-testing-documentation.md)
- [CI/CD Setup](#26-phase-5-production-deployment-preparation) → [CI/CD Guide](080-cicd-documentation.md)

### 2.1.1. Prerequisites

**Required Environment:**
- PHP 8.4 or higher
- Composer 2.x
- Git version control
- Laravel Zero framework knowledge
- Basic understanding of dependency injection

**Development Tools:**
- PHPStan for static analysis
- Pest for testing
- Pint for code formatting
- PHP Insights for code quality

## 2.2. Complete Source Code Repository

This section provides complete, copy-paste-ready source code for all files in the validate-links project. Every class, configuration file, and script is included with full implementations that are syntactically correct and compatible with Laravel 12.20+ and PHP 8.4+.

### 2.2.1. Configuration Files

#### **composer.json - Complete Project Dependencies**

```json
{
    "name": "validate-links/validate-links",
    "description": "A Laravel Zero application for validating links in documentation and markdown files",
    "keywords": [
        "laravel-zero",
        "console",
        "cli",
        "link-validation",
        "documentation",
        "markdown"
    ],
    "homepage": "https://github.com/validate-links/validate-links",
    "type": "project",
    "license": "MIT",
    "authors": [
        {
            "name": "Validate Links Team",
            "email": "<EMAIL>"
        }
    ],
    "require": {
        "php": "^8.4",
        "ext-json": "*",
        "ext-mbstring": "*",
        "ext-curl": "*",
        "illuminate/http": "^12.0",
        "laravel-zero/framework": "^12.20",
        "laravel/prompts": "^0.3",
        "symfony/dom-crawler": "^7.2",
        "guzzlehttp/guzzle": "^7.8",
        "league/commonmark": "^2.5",
        "spatie/laravel-data": "^4.10",
        "nunomaduro/termwind": "^2.2"
    },
    "require-dev": {
        "fakerphp/faker": "^1.23",
        "laravel/pint": "^1.18",
        "mockery/mockery": "^1.6",
        "nunomaduro/collision": "^8.5",
        "larastan/larastan": "^2.9",
        "orklah/psalm-strict-equality": "^3.1",
        "peckphp/peck": "^0.1.3",
        "pestphp/pest": "^3.8.2",
        "pestphp/pest-plugin-arch": "^3.1",
        "pestphp/pest-plugin-stressless": "^3.1",
        "phpstan/extension-installer": "^1.4",
        "phpstan/phpstan-deprecation-rules": "^1.2",
        "phpstan/phpstan-phpunit": "^1.4",
        "rector/rector": "^1.2",
        "nunomaduro/phpinsights": "^2.11"
    },
    "autoload": {
        "psr-4": {
            "App\\": "app/",
            "Database\\Factories\\": "database/factories/",
            "Database\\Seeders\\": "database/seeders/"
        }
    },
    "autoload-dev": {
        "psr-4": {
            "Tests\\": "tests/"
        }
    },
    "scripts": {
        "post-autoload-dump": [
            "@php validate-links app:build --ansi"
        ],
        "analyse": [
            "@php vendor/bin/phpstan analyse"
        ],
        "test": [
            "@php vendor/bin/pest"
        ],
        "test-coverage": [
            "@php vendor/bin/pest --coverage"
        ],
        "format": [
            "@php vendor/bin/pint"
        ],
        "refactor": [
            "@php vendor/bin/rector"
        ],
        "insights": [
            "@php vendor/bin/phpinsights"
        ]
    },
    "minimum-stability": "stable",
    "prefer-stable": true,
    "bin": [
        "validate-links"
    ]
}
```

#### **.env.example - Environment Configuration Template**

```bash
# Application Configuration
APP_NAME="Validate Links"
APP_ENV=local
APP_DEBUG=true
APP_VERSION=1.0.0

# Link Validation Configuration
VALIDATE_LINKS_TIMEOUT=30
VALIDATE_LINKS_MAX_REDIRECTS=5
VALIDATE_LINKS_USER_AGENT="ValidateLinks/1.0"
VALIDATE_LINKS_CONCURRENT_REQUESTS=10

# Cache Configuration
CACHE_DRIVER=file
CACHE_TTL=3600

# Security Configuration
SECURITY_ALLOW_LOCALHOST=true
SECURITY_ALLOW_PRIVATE_IPS=false
SECURITY_BLOCKED_DOMAINS=""
SECURITY_ALLOWED_PROTOCOLS="http,https,ftp"

# Output Configuration
DEFAULT_OUTPUT_FORMAT=console
REPORT_OUTPUT_PATH="./reports"

# Performance Configuration
MEMORY_LIMIT=512M
MAX_EXECUTION_TIME=300

# Logging Configuration
LOG_CHANNEL=single
LOG_LEVEL=info
```

#### **config/validate-links.php - Application Configuration**

```php
<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Link Validation Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for the validate-links
    | application. These settings control how links are validated, cached,
    | and reported.
    |
    */

    'timeout' => (int) env('VALIDATE_LINKS_TIMEOUT', 30),
    'max_redirects' => (int) env('VALIDATE_LINKS_MAX_REDIRECTS', 5),
    'user_agent' => env('VALIDATE_LINKS_USER_AGENT', 'ValidateLinks/1.0'),
    'concurrent_requests' => (int) env('VALIDATE_LINKS_CONCURRENT_REQUESTS', 10),

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    */

    'cache' => [
        'driver' => env('CACHE_DRIVER', 'file'),
        'ttl' => (int) env('CACHE_TTL', 3600),
        'prefix' => 'validate_links_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */

    'security' => [
        'allow_localhost' => env('SECURITY_ALLOW_LOCALHOST', true),
        'allow_private_ips' => env('SECURITY_ALLOW_PRIVATE_IPS', false),
        'blocked_domains' => array_filter(explode(',', env('SECURITY_BLOCKED_DOMAINS', ''))),
        'allowed_protocols' => array_filter(explode(',', env('SECURITY_ALLOWED_PROTOCOLS', 'http,https,ftp'))),
        'max_file_size' => 50 * 1024 * 1024, // 50MB
    ],

    /*
    |--------------------------------------------------------------------------
    | Output Configuration
    |--------------------------------------------------------------------------
    */

    'output' => [
        'default_format' => env('DEFAULT_OUTPUT_FORMAT', 'console'),
        'report_path' => env('REPORT_OUTPUT_PATH', './reports'),
        'formats' => [
            'console' => [
                'class' => \App\Services\Formatters\ConsoleFormatter::class,
                'options' => [
                    'colors' => true,
                    'verbose' => false,
                ],
            ],
            'json' => [
                'class' => \App\Services\Formatters\JsonFormatter::class,
                'options' => [
                    'pretty_print' => true,
                    'include_metadata' => true,
                ],
            ],
            'html' => [
                'class' => \App\Services\Formatters\HtmlFormatter::class,
                'options' => [
                    'template' => 'default',
                    'include_css' => true,
                ],
            ],
            'markdown' => [
                'class' => \App\Services\Formatters\MarkdownFormatter::class,
                'options' => [
                    'include_toc' => true,
                    'github_flavored' => true,
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    */

    'performance' => [
        'memory_limit' => env('MEMORY_LIMIT', '512M'),
        'max_execution_time' => (int) env('MAX_EXECUTION_TIME', 300),
        'chunk_size' => 100,
        'batch_size' => 50,
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Scopes
    |--------------------------------------------------------------------------
    */

    'scopes' => [
        'internal' => [
            'enabled' => true,
            'check_anchors' => true,
            'follow_redirects' => false,
        ],
        'external' => [
            'enabled' => true,
            'check_anchors' => false,
            'follow_redirects' => true,
            'timeout' => 15,
        ],
        'anchor' => [
            'enabled' => true,
            'github_style' => true,
            'case_sensitive' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | File Patterns
    |--------------------------------------------------------------------------
    */

    'patterns' => [
        'include' => [
            '*.md',
            '*.markdown',
            '*.rst',
            '*.txt',
        ],
        'exclude' => [
            'node_modules/**',
            'vendor/**',
            '.git/**',
            'build/**',
            'dist/**',
        ],
    ],
];
```

#### **bootstrap/providers.php - Service Provider Registration**

```php
<?php

declare(strict_types=1);

return [
    App\Providers\AppServiceProvider::class,
    App\Providers\ValidateLinksServiceProvider::class,
];
```

#### **.github/workflows/ci.yml - Complete CI/CD Pipeline**

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php-version: [8.4]
        dependency-version: [prefer-lowest, prefer-stable]

    name: PHP ${{ matrix.php-version }} - ${{ matrix.dependency-version }}

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite
        coverage: xdebug

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v4
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ matrix.php-version }}-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-${{ matrix.php-version }}-

    - name: Install dependencies
      run: |
        composer update --${{ matrix.dependency-version }} --prefer-dist --no-interaction --no-suggest

    - name: Run PHPStan
      run: vendor/bin/phpstan analyse

    - name: Run Pint
      run: vendor/bin/pint --test

    - name: Run Pest Tests
      run: vendor/bin/pest --coverage --min=90

    - name: Run PHP Insights
      run: vendor/bin/phpinsights --no-interaction --min-quality=90 --min-complexity=90 --min-architecture=90 --min-style=90

  security:
    runs-on: ubuntu-latest
    name: Security Audit

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.4'
        extensions: dom, curl, libxml, mbstring, zip

    - name: Install dependencies
      run: composer install --prefer-dist --no-interaction

    - name: Security Audit
      run: composer audit

  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.4'
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite

    - name: Install dependencies
      run: composer install --prefer-dist --no-interaction --no-dev --optimize-autoloader

    - name: Build application
      run: php validate-links app:build --build-version=${{ github.sha }}

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: validate-links-build
        path: builds/
```

#### **phpstan.neon - Static Analysis Configuration**

```neon
includes:
    - vendor/larastan/larastan/extension.neon

parameters:
    level: 9
    paths:
        - app/
        - config/
        - bootstrap/
    excludePaths:
        - app/Providers/AppServiceProvider.php
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    ignoreErrors:
        - '#Call to an undefined method Illuminate\\Testing\\PendingCommand::#'
```

#### **pint.json - Code Style Configuration**

```json
{
    "preset": "laravel",
    "rules": {
        "simplified_null_return": true,
        "not_operator_with_successor_space": true,
        "ordered_imports": {
            "sort_algorithm": "alpha"
        },
        "no_unused_imports": true,
        "array_syntax": {
            "syntax": "short"
        }
    },
    "exclude": [
        "bootstrap/cache",
        "storage",
        "vendor"
    ]
}
```

#### **rector.php - Code Modernization Configuration**

```php
<?php

declare(strict_types=1);

use Rector\Config\RectorConfig;
use Rector\Set\ValueObject\LevelSetList;
use Rector\Set\ValueObject\SetList;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->paths([
        __DIR__ . '/app',
        __DIR__ . '/config',
        __DIR__ . '/tests',
    ]);

    $rectorConfig->sets([
        LevelSetList::UP_TO_PHP_84,
        SetList::CODE_QUALITY,
        SetList::DEAD_CODE,
        SetList::EARLY_RETURN,
        SetList::TYPE_DECLARATION,
    ]);

    $rectorConfig->skip([
        __DIR__ . '/bootstrap/cache',
        __DIR__ . '/storage',
        __DIR__ . '/vendor',
    ]);
};
```

## 2.3. Phase 1: Analysis & Planning (Week 1)

This phase corresponds to tasks 1.0-1.4 in the [Implementation Plan](090-implementation-plan.md#phase-1-analysis--planning-week-1).

### 2.3.1. Task 1.1: Development Environment Configuration

**📋 Implementation Plan Reference:** [Task 1.1](090-implementation-plan.md#phase-1-analysis--planning-week-1)

#### **System Requirements**

**PHP Requirements:**
- **PHP Version:** ^8.4
- **Required Extensions:**
  - `ext-json` - JSON processing
  - `ext-mbstring` - Multibyte string handling
  - `ext-curl` - HTTP client functionality

**Composer Requirements:**
- **Composer Version:** 2.0 or higher
- **Minimum Stability:** dev (with prefer-stable: true)

### 2.2.2. Production Dependencies

#### Core Framework

| Package | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `laravel-zero/framework` | ^12.0 | CLI application framework | [Laravel Zero Docs](https://laravel-zero.com) |
| `laravel/prompts` | ^0.3 | Interactive CLI prompts | [Laravel Prompts](https://laravel.com/docs/prompts) |
| `illuminate/http` | ^12.0 | HTTP client functionality | [Laravel HTTP](https://laravel.com/docs/http-client) |
| `symfony/dom-crawler` | ^7.2 | HTML/XML parsing and traversal | [Symfony DomCrawler](https://symfony.com/doc/current/components/dom_crawler.html) |

#### Installation Commands

```bash
# Install production dependencies only
composer install --no-dev --optimize-autoloader

# Install all dependencies (development)
composer install
```

### 2.2.3. Development Dependencies

#### Code Quality Tools

**Laravel Pint (Code Formatting):**

| Package | Version | Purpose |
|---------|---------|---------|
| `laravel/pint` | ^1.22 | PHP code style fixer based on PHP-CS-Fixer |

**Usage:**
```bash
# Fix code style issues
composer cs-fix

# Check code style without fixing
composer cs-check
```

**PHPStan/Larastan (Static Analysis):**

| Package | Version | Purpose |
|---------|---------|---------|
| `larastan/larastan` | ^3.6 | Laravel-specific PHPStan rules |
| `phpstan/phpstan-deprecation-rules` | ^2.0 | Deprecation detection rules |

**Usage:**
```bash
# Run static analysis
composer analyse

# Run with specific level
vendor/bin/phpstan analyse app/ --level=8
```

**Rector (Automated Refactoring):**

| Package | Version | Purpose |
|---------|---------|---------|
| `rector/rector` | ^2.1 | Automated code refactoring |
| `rector/type-perfect` | ^2.1 | Type declaration improvements |
| `driftingly/rector-laravel` | ^2.0 | Laravel-specific Rector rules |

**Usage:**
```bash
# Dry run (preview changes)
vendor/bin/rector process --dry-run

# Apply changes
vendor/bin/rector process
```

**Psalm (Static Analysis):**

| Package | Version | Purpose |
|---------|---------|---------|
| `vimeo/psalm` | ^6.13 | Advanced static analysis |
| `psalm/plugin-laravel` | ^3.0 | Laravel-specific Psalm plugin |
| `orklah/psalm-strict-equality` | ^3.1 | Strict equality checks |
| `roave/psalm-html-output` | ^1.1 | HTML report generation |

**Usage:**
```bash
# Run Psalm analysis
vendor/bin/psalm

# Generate HTML report
vendor/bin/psalm --report=psalm-report.html
```

#### Testing Framework

**Pest (Testing Framework):**

| Package | Version | Purpose |
|---------|---------|---------|
| `pestphp/pest` | ^3.8.2 | Modern PHP testing framework |
| `pestphp/pest-plugin-arch` | ^3.1 | Architecture testing |
| `pestphp/pest-plugin-type-coverage` | ^3.6 | Type coverage analysis |
| `pestphp/pest-plugin-stressless` | ^3.1 | Stress testing |

**Usage:**

```bash
# Run all tests
composer test

# Run with coverage
composer test:coverage

# Run specific test suite
composer test:unit
composer test:feature
```

### 2.2.4. IDE Integration

#### PHPStorm Configuration

1. **Install Laravel Plugin:** File → Settings → Plugins → Laravel
2. **Configure PHP Interpreter:** File → Settings → PHP → CLI Interpreter
3. **Enable Code Quality Tools:**
   - PHPStan: File → Settings → PHP → Quality Tools → PHPStan
   - Psalm: File → Settings → PHP → Quality Tools → Psalm
   - PHP CS Fixer: File → Settings → PHP → Quality Tools → PHP CS Fixer

#### VS Code Configuration

**Required Extensions:**

- PHP Intelephense
- Laravel Extension Pack
- Pest Snippets

**Settings Configuration (.vscode/settings.json):**

```json
{
    "php.validate.executablePath": "/usr/bin/php",
    "php.suggest.basic": false,
    "intelephense.files.maxSize": 5000000,
    "pest-snippets.showOutput": true
}
```

### 2.2.5. Development Workflow

#### Daily Development Commands

```bash
# Start development session
composer install
composer cs-fix
composer analyse

# Run tests before commit
composer test
composer test:coverage

# Final quality check
composer insights
vendor/bin/psalm
```

#### Pre-commit Checklist

- [ ] Code style fixed (`composer cs-fix`)
- [ ] Static analysis passed (`composer analyse`)
- [ ] All tests passing (`composer test`)
- [ ] Type coverage ≥95% (`composer test:type-coverage`)
- [ ] Code coverage ≥80% (`composer test:coverage`)

## 2.3. Phase 1: Complete Service Implementations

**📖 Cross-References:**
- **[Service Contracts Documentation](060-code-documentation.md#63-service-contracts-documentation)** - Interface definitions and usage patterns
- **[Service Testing](070-testing-documentation.md#75-service-testing)** - Testing strategies for service implementations
- **[Architecture Overview](030-architecture-overview.md#33-service-layer-architecture)** - Service layer design patterns

### 2.3.1. SecurityValidationService Implementation

**Status:** Missing concrete implementation
**Priority:** Critical
**Estimated Time:** 4-6 hours

**📋 Implementation Checklist:**
- [ ] Complete SecurityValidationService class implementation
- [ ] Add comprehensive unit tests ([Testing Guide](070-testing-documentation.md#72-unit-testing))
- [ ] Verify service registration in provider
- [ ] Test security validation scenarios

**🔗 Related Documentation:**
- [SecurityValidationInterface Contract](060-code-documentation.md#632-securityvalidationinterface)
- [Security Testing Examples](070-testing-documentation.md#721-service-unit-tests)

**Step 1: Create SecurityValidationService.php**

```php
<?php
// app/Services/SecurityValidationService.php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\SecurityValidationInterface;
use InvalidArgumentException;
use RuntimeException;

class SecurityValidationService implements SecurityValidationInterface
{
    private array $blockedDomains;
    private array $allowedProtocols;
    private int $maxFileSize;
    private array $blockedPaths;

    public function __construct()
    {
        $this->blockedDomains = config('validate-links.security.blocked_domains', []);
        $this->allowedProtocols = config('validate-links.security.allowed_protocols', ['http', 'https']);
        $this->maxFileSize = config('validate-links.security.max_file_size', 10 * 1024 * 1024);
        $this->blockedPaths = config('validate-links.security.blocked_paths', []);
    }

    public function validatePath(string $path): bool
    {
        // Prevent path traversal attacks
        $normalizedPath = realpath($path);
        
        if ($normalizedPath === false) {
            return false;
        }

        // Check for blocked path patterns
        foreach ($this->blockedPaths as $blockedPattern) {
            if (fnmatch($blockedPattern, $normalizedPath)) {
                return false;
            }
        }

        // Ensure path is within allowed boundaries
        $basePath = getcwd();
        return str_starts_with($normalizedPath, $basePath);
    }

    public function validateUrl(string $url): bool
    {
        // Validate URL format
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        $parsedUrl = parse_url($url);
        
        // Check protocol
        if (!in_array($parsedUrl['scheme'] ?? '', $this->allowedProtocols)) {
            return false;
        }

        // Check blocked domains
        $host = $parsedUrl['host'] ?? '';
        foreach ($this->blockedDomains as $blockedDomain) {
            if (str_ends_with($host, $blockedDomain)) {
                return false;
            }
        }

        return true;
    }

    public function validateFileSize(string $filePath): bool
    {
        if (!file_exists($filePath)) {
            return false;
        }

        $fileSize = filesize($filePath);
        return $fileSize !== false && $fileSize <= $this->maxFileSize;
    }

    public function sanitizePath(string $path): string
    {
        // Remove dangerous characters and normalize
        $sanitized = preg_replace('/[^a-zA-Z0-9\/_.-]/', '', $path);
        return trim($sanitized, '/');
    }

    public function isPathTraversalAttempt(string $path): bool
    {
        return str_contains($path, '..') || 
               str_contains($path, '~') || 
               str_starts_with($path, '/');
    }
}
```

**Step 2: Update Service Provider Registration**

```php
// app/Providers/ValidateLinksServiceProvider.php
public function register(): void
{
    $this->app->singleton(SecurityValidationInterface::class, SecurityValidationService::class);
    // ... other registrations
}
```

**Step 3: Create Unit Tests**

```php
<?php
// tests/Unit/Services/SecurityValidationServiceTest.php

declare(strict_types=1);

use App\Services\SecurityValidationService;

describe('SecurityValidationService', function () {
    beforeEach(function () {
        $this->service = new SecurityValidationService();
    });

    describe('validatePath', function () {
        it('validates safe paths', function () {
            expect($this->service->validatePath('./docs/test.md'))->toBeTrue();
        });

        it('rejects path traversal attempts', function () {
            expect($this->service->validatePath('../../../etc/passwd'))->toBeFalse();
        });
    });

    describe('validateUrl', function () {
        it('validates https URLs', function () {
            expect($this->service->validateUrl('https://example.com'))->toBeTrue();
        });

        it('rejects invalid protocols', function () {
            expect($this->service->validateUrl('ftp://example.com'))->toBeFalse();
        });
    });
});
```

### 2.2.2. ReportingService Implementation

**Status:** Missing concrete implementation  
**Priority:** Critical  
**Estimated Time:** 6-8 hours

**Step 1: Create ReportingService.php**

```php
<?php
// app/Services/ReportingService.php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\ReportingInterface;
use App\Services\Contracts\StatisticsInterface;
use App\Services\Formatters\ConsoleFormatter;
use App\Services\Formatters\JsonFormatter;
use App\Services\Formatters\MarkdownFormatter;
use App\Services\Formatters\HtmlFormatter;
use App\Services\ValueObjects\ValidationResult;
use Illuminate\Console\Command;
use InvalidArgumentException;

class ReportingService implements ReportingInterface
{
    private array $formatters;
    private StatisticsInterface $statistics;

    public function __construct(StatisticsInterface $statistics)
    {
        $this->statistics = $statistics;
        $this->formatters = [
            'console' => new ConsoleFormatter(),
            'json' => new JsonFormatter(),
            'markdown' => new MarkdownFormatter(),
            'html' => new HtmlFormatter(),
        ];
    }

    public function generateReport(array $results, string $format): string
    {
        // Validate the format
        if (!isset($this->formatters[$format])) {
            throw new InvalidArgumentException("Unsupported format: {$format}");
        }

        $formatter = $this->formatters[$format];
        $statistics = $this->statistics->getStatistics();

        // Generate the report using the specified formatter
        return $formatter->format($results, $statistics);
    }

    public function formatForConsole(array $results): string
    {
        return $this->formatters['console']->format($results, $this->statistics->getStatistics());
    }

    public function formatAsJson(array $results): string
    {
        return $this->formatters['json']->format($results, $this->statistics->getStatistics());
    }

    public function formatAsHtml(array $results): string
    {
        return $this->formatters['html']->format($results, $this->statistics->getStatistics());
    }

    public function formatAsMarkdown(array $results): string
    {
        return $this->formatters['markdown']->format($results, $this->statistics->getStatistics());
    }

    public function saveReport(string $content, string $filePath): bool
    {
        $directory = dirname($filePath);

        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        return file_put_contents($filePath, $content) !== false;
    }

    public function getAvailableFormats(): array
    {
        return array_keys($this->formatters);
    }

    public function supportsFormat(string $format): bool
    {
        return isset($this->formatters[$format]);
    }

    public function addFormatter(string $name, object $formatter): void
    {
        $this->formatters[$name] = $formatter;
    }

    public function generateSummary(array $results): array
    {
        $summary = [
            'total_files' => 0,
            'total_links' => 0,
            'broken_links' => 0,
            'external_links' => 0,
            'internal_links' => 0,
            'anchor_links' => 0,
        ];

        foreach ($results as $fileResult) {
            $summary['total_files']++;
            $summary['total_links'] += count($fileResult['links'] ?? []);

            foreach ($fileResult['links'] ?? [] as $link) {
                if ($link['status'] === 'broken') {
                    $summary['broken_links']++;
                }

                match ($link['type']) {
                    'external' => $summary['external_links']++,
                    'internal' => $summary['internal_links']++,
                    'anchor' => $summary['anchor_links']++,
                    default => null,
                };
            }
        }

        return $summary;
    }


}
```

**Step 2: Complete Formatter Implementations**

Ensure all formatter classes in `app/Services/Formatters/` are fully implemented with proper formatting logic.

### 2.2.3. Complete Partial Service Implementations

**LinkValidationService Enhancement**

```php
// app/Services/LinkValidationService.php - Add missing methods

public function validateCrossReferences(array $files): array
{
    $allAnchors = $this->extractAllAnchors($files);
    $crossReferences = [];

    foreach ($files as $file) {
        $links = $this->extractLinks($file);
        
        foreach ($links as $link) {
            if ($this->isCrossReference($link)) {
                $crossReferences[] = $this->validateCrossReference($link, $allAnchors);
            }
        }
    }

    return $crossReferences;
}

private function extractAllAnchors(array $files): array
{
    $anchors = [];
    
    foreach ($files as $file) {
        $content = file_get_contents($file);
        $fileAnchors = $this->gitHubAnchorService->extractAnchors($content);
        $anchors[$file] = $fileAnchors;
    }

    return $anchors;
}

private function validateCrossReference(array $link, array $allAnchors): array
{
    // Implementation for cross-reference validation
    $targetFile = $this->resolveTargetFile($link['url']);
    $targetAnchor = $this->extractAnchorFromUrl($link['url']);

    if (!isset($allAnchors[$targetFile])) {
        return [
            'url' => $link['url'],
            'status' => 'broken',
            'reason' => 'Target file not found',
        ];
    }

    if ($targetAnchor && !in_array($targetAnchor, $allAnchors[$targetFile])) {
        return [
            'url' => $link['url'],
            'status' => 'broken',
            'reason' => 'Target anchor not found',
        ];
    }

    return [
        'url' => $link['url'],
        'status' => 'valid',
        'reason' => null,
    ];
}
```

### 2.2.3. GitHubAnchorService Implementation

**Status:** Missing concrete implementation
**Priority:** High
**Estimated Time:** 3-4 hours

**📋 Implementation Checklist:**
- [ ] Complete GitHubAnchorService class implementation
- [ ] Add comprehensive unit tests for anchor generation
- [ ] Verify GitHub anchor compatibility
- [ ] Test anchor validation scenarios

**🔗 Related Documentation:**
- [GitHubAnchorInterface Contract](060-code-documentation.md#635-githubanchorinterface)
- [Anchor Testing Examples](070-testing-documentation.md#721-service-unit-tests)

**Step 1: Create GitHubAnchorService.php**

```php
<?php
// app/Services/GitHubAnchorService.php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\GitHubAnchorInterface;

class GitHubAnchorService implements GitHubAnchorInterface
{
    /**
     * Generate GitHub-style anchor from heading text.
     */
    public function generateAnchor(string $headingText): string
    {
        // Remove markdown formatting
        $text = preg_replace('/[*_`~]/', '', $headingText);

        // Convert to lowercase
        $text = strtolower($text);

        // Replace spaces and special characters with hyphens
        $text = preg_replace('/[^a-z0-9\-]/', '-', $text);

        // Remove multiple consecutive hyphens
        $text = preg_replace('/-+/', '-', $text);

        // Remove leading and trailing hyphens
        return trim($text, '-');
    }

    /**
     * Extract all anchors from markdown content.
     */
    public function extractAnchors(string $content): array
    {
        $anchors = [];

        // Extract headings (# ## ### etc.)
        preg_match_all('/^(#{1,6})\s+(.+)$/m', $content, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $level = strlen($match[1]);
            $text = trim($match[2]);
            $anchor = $this->generateAnchor($text);

            $anchors[] = [
                'text' => $text,
                'anchor' => $anchor,
                'level' => $level,
                'line' => substr_count($content, "\n", 0, strpos($content, $match[0])) + 1
            ];
        }

        return $anchors;
    }

    /**
     * Validate anchor exists in content.
     */
    public function validateAnchor(string $anchor, string $content): bool
    {
        $anchors = $this->extractAnchors($content);
        $normalizedAnchor = $this->normalizeAnchor($anchor);

        foreach ($anchors as $anchorData) {
            if ($this->normalizeAnchor($anchorData['anchor']) === $normalizedAnchor) {
                return true;
            }
        }

        return false;
    }

    /**
     * Normalize anchor for comparison.
     */
    public function normalizeAnchor(string $anchor): string
    {
        // Remove leading # if present
        $anchor = ltrim($anchor, '#');

        // Convert to lowercase for comparison
        return strtolower($anchor);
    }

    /**
     * Check if anchor follows GitHub conventions.
     */
    public function isValidGitHubAnchor(string $anchor): bool
    {
        // Remove leading # if present
        $anchor = ltrim($anchor, '#');

        // GitHub anchors should only contain lowercase letters, numbers, and hyphens
        return preg_match('/^[a-z0-9\-]+$/', $anchor) === 1;
    }

    /**
     * Generate anchor map for content.
     */
    public function generateAnchorMap(string $content): array
    {
        $anchors = $this->extractAnchors($content);
        $map = [];

        foreach ($anchors as $anchorData) {
            $map[$anchorData['anchor']] = [
                'text' => $anchorData['text'],
                'level' => $anchorData['level'],
                'line' => $anchorData['line']
            ];
        }

        return $map;
    }

    /**
     * Find duplicate anchors in content.
     */
    public function findDuplicateAnchors(string $content): array
    {
        $anchors = $this->extractAnchors($content);
        $anchorCounts = [];
        $duplicates = [];

        foreach ($anchors as $anchorData) {
            $anchor = $anchorData['anchor'];

            if (!isset($anchorCounts[$anchor])) {
                $anchorCounts[$anchor] = [];
            }

            $anchorCounts[$anchor][] = $anchorData;
        }

        foreach ($anchorCounts as $anchor => $occurrences) {
            if (count($occurrences) > 1) {
                $duplicates[$anchor] = $occurrences;
            }
        }

        return $duplicates;
    }
}
```

**Step 2: Register Service in Provider**

```php
// app/Providers/AppServiceProvider.php

public function register(): void
{
    $this->app->singleton(GitHubAnchorInterface::class, GitHubAnchorService::class);
    // ... other registrations
}
```

### 2.2.4. StatisticsService Implementation

**Status:** Missing concrete implementation
**Priority:** High
**Estimated Time:** 2-3 hours

**📋 Implementation Checklist:**
- [ ] Complete StatisticsService class implementation
- [ ] Add comprehensive unit tests for statistics collection
- [ ] Verify statistics accuracy and performance
- [ ] Test statistics reporting scenarios

**🔗 Related Documentation:**
- [StatisticsInterface Contract](060-code-documentation.md#633-statisticsinterface)
- [Statistics Testing Examples](070-testing-documentation.md#721-service-unit-tests)

**Step 1: Create StatisticsService.php**

```php
<?php
// app/Services/StatisticsService.php

declare(strict_types=1);

namespace App\Services;

use App\Services\Contracts\StatisticsInterface;

class StatisticsService implements StatisticsInterface
{
    private array $statistics;
    private array $brokenLinks;
    private array $processedFiles;

    public function __construct()
    {
        $this->reset();
    }

    /**
     * Reset all statistics counters.
     */
    public function reset(): void
    {
        $this->statistics = [
            'files_processed' => 0,
            'total_links' => 0,
            'broken_links' => 0,
            'external_links' => 0,
            'internal_links' => 0,
            'anchor_links' => 0,
            'start_time' => microtime(true),
            'end_time' => null,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
        ];

        $this->brokenLinks = [];
        $this->processedFiles = [];
    }

    /**
     * Increment file processing counter.
     */
    public function incrementFilesProcessed(): void
    {
        $this->statistics['files_processed']++;
    }

    /**
     * Add link statistics for a specific type.
     */
    public function addLinkStats(string $type, int $total, int $broken): void
    {
        $this->statistics['total_links'] += $total;
        $this->statistics['broken_links'] += $broken;

        match ($type) {
            'external' => $this->statistics['external_links'] += $total,
            'internal' => $this->statistics['internal_links'] += $total,
            'anchor' => $this->statistics['anchor_links'] += $total,
            default => null,
        };
    }

    /**
     * Record a broken link with details.
     */
    public function recordBrokenLink(string $link, string $file, string $reason, string $type): void
    {
        $this->brokenLinks[] = [
            'url' => $link,
            'file' => $file,
            'reason' => $reason,
            'type' => $type,
            'timestamp' => microtime(true),
        ];
    }

    /**
     * Get complete statistics array.
     */
    public function getStatistics(): array
    {
        $stats = $this->statistics;

        // Calculate execution time if not already set
        if ($stats['end_time'] === null) {
            $stats['execution_time'] = microtime(true) - $stats['start_time'];
        } else {
            $stats['execution_time'] = $stats['end_time'] - $stats['start_time'];
        }

        // Update memory usage
        $stats['current_memory'] = memory_get_usage(true);
        $stats['peak_memory'] = memory_get_peak_usage(true);

        // Calculate success rate
        if ($stats['total_links'] > 0) {
            $stats['success_rate'] = round(
                (($stats['total_links'] - $stats['broken_links']) / $stats['total_links']) * 100,
                2
            );
        } else {
            $stats['success_rate'] = 100.0;
        }

        // Add performance metrics
        if ($stats['execution_time'] > 0) {
            $stats['files_per_second'] = round($stats['files_processed'] / $stats['execution_time'], 2);
            $stats['links_per_second'] = round($stats['total_links'] / $stats['execution_time'], 2);
        } else {
            $stats['files_per_second'] = 0;
            $stats['links_per_second'] = 0;
        }

        return $stats;
    }

    /**
     * Get total broken links count.
     */
    public function getTotalBrokenLinks(): int
    {
        return $this->statistics['broken_links'];
    }

    /**
     * Get processed files list.
     */
    public function getProcessedFiles(): array
    {
        return $this->processedFiles;
    }

    /**
     * Record a processed file.
     */
    public function recordProcessedFile(string $filePath, array $results): void
    {
        $this->processedFiles[] = [
            'path' => $filePath,
            'results' => $results,
            'timestamp' => microtime(true),
        ];

        $this->incrementFilesProcessed();
    }

    /**
     * Mark statistics collection as complete.
     */
    public function markComplete(): void
    {
        $this->statistics['end_time'] = microtime(true);
    }

    /**
     * Get broken links details.
     */
    public function getBrokenLinks(): array
    {
        return $this->brokenLinks;
    }

    /**
     * Get summary statistics for reporting.
     */
    public function getSummary(): array
    {
        $stats = $this->getStatistics();

        return [
            'total_files' => $stats['files_processed'],
            'total_links' => $stats['total_links'],
            'broken_links' => $stats['broken_links'],
            'success_rate' => $stats['success_rate'],
            'execution_time' => $stats['execution_time'],
            'performance' => [
                'files_per_second' => $stats['files_per_second'],
                'links_per_second' => $stats['links_per_second'],
                'memory_usage' => $this->formatBytes($stats['current_memory']),
                'peak_memory' => $this->formatBytes($stats['peak_memory']),
            ],
        ];
    }

    /**
     * Format bytes into human-readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor(log($bytes, 1024));

        return sprintf('%.2f %s', $bytes / (1024 ** $factor), $units[$factor]);
    }
}
```

**Step 2: Register Service in Provider**

```php
// app/Providers/AppServiceProvider.php

public function register(): void
{
    $this->app->singleton(StatisticsInterface::class, StatisticsService::class);
    // ... other registrations
}
```

### 2.2.5. Exception Classes Implementation

**Status:** Partially implemented
**Priority:** Critical
**Estimated Time:** 2-3 hours

**📖 Cross-References:**
- **[Exception Classes Documentation](060-code-documentation.md#66-exception-classes-documentation)** - Detailed exception usage patterns
- **[Exception Testing](070-testing-documentation.md#721-service-unit-tests)** - Unit tests for exception handling
- **[Error Handling in CI/CD](080-cicd-documentation.md#86-security-and-compliance-pipeline)** - Exception monitoring in pipelines

**📋 Implementation Checklist:**
- [ ] Complete ValidateLinksException abstract base class
- [ ] Implement concrete exception classes (ValidationException, SecurityException, ConfigurationException)
- [ ] Complete Handler class with console formatting
- [ ] Add comprehensive exception unit tests
- [ ] Verify exception logging and reporting

The application uses a custom exception hierarchy for proper error handling and user feedback. All exception classes must be fully implemented with complete error context and handling capabilities.

#### ValidateLinksException (Abstract Base Class)

**Location:** `app/Exceptions/ValidateLinksException.php`

**Complete Implementation:**

```php
<?php

declare(strict_types=1);

namespace App\Exceptions;

use Exception;
use JetBrains\PhpStorm\Pure;

/**
 * Abstract base class for all validate-links specific exceptions.
 *
 * Provides common functionality for error context, severity levels,
 * and standardized error codes across the application.
 */
abstract class ValidateLinksException extends Exception
{
    /**
     * Additional context information for the exception.
     *
     * @var array<string, mixed>
     */
    protected array $context = [];

    /**
     * Create a new ValidateLinksException instance.
     *
     * @param string $message The exception message
     * @param array<string, mixed> $context Additional context information
     * @param int $code The exception code
     * @param Exception|null $previous Previous exception for chaining
     */
    #[Pure]
    public function __construct(string $message, array $context = [], int $code = 0, ?Exception $previous = null)
    {
        $this->context = $context;
        parent::__construct($message, $code, $previous);
    }

    /**
     * Get the application-specific error code.
     *
     * @return string Unique error code for this exception type
     */
    abstract public function getErrorCode(): string;

    /**
     * Get the severity level of this exception.
     *
     * @return string Severity level (low, medium, high, critical)
     */
    abstract public function getSeverity(): string;

    /**
     * Get the additional context information.
     *
     * @return array<string, mixed> Context data
     */
    final public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Add context information to the exception.
     *
     * @param string $key Context key
     * @param mixed $value Context value
     * @return self
     */
    public function addContext(string $key, mixed $value): self
    {
        $this->context[$key] = $value;
        return $this;
    }

    /**
     * Get a formatted error message with context.
     *
     * @return string Formatted error message
     */
    public function getFormattedMessage(): string
    {
        $message = "[{$this->getErrorCode()}] {$this->getMessage()}";

        if (!empty($this->context)) {
            $contextStr = json_encode($this->context, JSON_PRETTY_PRINT);
            $message .= "\nContext: {$contextStr}";
        }

        return $message;
    }

    /**
     * Check if this exception should be logged.
     *
     * @return bool True if exception should be logged
     */
    public function shouldLog(): bool
    {
        return in_array($this->getSeverity(), ['high', 'critical']);
    }

    /**
     * Get recommended user action for this exception.
     *
     * @return string User-friendly action recommendation
     */
    public function getRecommendedAction(): string
    {
        return 'Please check the error details and try again.';
    }
}
```

#### Concrete Exception Classes

**ValidationException - For validation-specific errors:**

```php
<?php

declare(strict_types=1);

namespace App\Exceptions;

/**
 * Exception thrown when link validation fails.
 */
class ValidationException extends ValidateLinksException
{
    public function getErrorCode(): string
    {
        return 'VALIDATION_ERROR';
    }

    public function getSeverity(): string
    {
        return 'medium';
    }

    public function getRecommendedAction(): string
    {
        return 'Check the file paths and link formats, then retry validation.';
    }

    /**
     * Create exception for invalid file path.
     */
    public static function invalidPath(string $path): self
    {
        return new self(
            "Invalid file path: {$path}",
            ['path' => $path, 'type' => 'invalid_path']
        );
    }

    /**
     * Create exception for broken link.
     */
    public static function brokenLink(string $url, string $file, string $reason): self
    {
        return new self(
            "Broken link detected: {$url}",
            [
                'url' => $url,
                'file' => $file,
                'reason' => $reason,
                'type' => 'broken_link'
            ]
        );
    }

    /**
     * Create exception for validation timeout.
     */
    public static function timeout(string $url, int $timeout): self
    {
        return new self(
            "Validation timeout for URL: {$url}",
            [
                'url' => $url,
                'timeout' => $timeout,
                'type' => 'timeout'
            ]
        );
    }
}
```

**SecurityException - For security-related errors:**

```php
<?php

declare(strict_types=1);

namespace App\Exceptions;

/**
 * Exception thrown when security violations are detected.
 */
class SecurityException extends ValidateLinksException
{
    public function getErrorCode(): string
    {
        return 'SECURITY_VIOLATION';
    }

    public function getSeverity(): string
    {
        return 'critical';
    }

    public function getRecommendedAction(): string
    {
        return 'Review the security policies and ensure all paths and URLs are safe.';
    }

    /**
     * Create exception for path traversal attempt.
     */
    public static function pathTraversal(string $path): self
    {
        return new self(
            "Path traversal attempt detected: {$path}",
            [
                'path' => $path,
                'type' => 'path_traversal',
                'security_risk' => 'high'
            ]
        );
    }

    /**
     * Create exception for blocked domain.
     */
    public static function blockedDomain(string $url, string $domain): self
    {
        return new self(
            "Access to blocked domain: {$domain}",
            [
                'url' => $url,
                'domain' => $domain,
                'type' => 'blocked_domain'
            ]
        );
    }

    /**
     * Create exception for unsafe protocol.
     */
    public static function unsafeProtocol(string $url, string $protocol): self
    {
        return new self(
            "Unsafe protocol detected: {$protocol}",
            [
                'url' => $url,
                'protocol' => $protocol,
                'type' => 'unsafe_protocol'
            ]
        );
    }
}
```

**ConfigurationException - For configuration errors:**

```php
<?php

declare(strict_types=1);

namespace App\Exceptions;

/**
 * Exception thrown when configuration errors occur.
 */
class ConfigurationException extends ValidateLinksException
{
    public function getErrorCode(): string
    {
        return 'CONFIGURATION_ERROR';
    }

    public function getSeverity(): string
    {
        return 'high';
    }

    public function getRecommendedAction(): string
    {
        return 'Check the configuration files and environment settings.';
    }

    /**
     * Create exception for missing configuration.
     */
    public static function missingConfiguration(string $key): self
    {
        return new self(
            "Missing required configuration: {$key}",
            [
                'config_key' => $key,
                'type' => 'missing_config'
            ]
        );
    }

    /**
     * Create exception for invalid configuration value.
     */
    public static function invalidValue(string $key, mixed $value, string $expected): self
    {
        return new self(
            "Invalid configuration value for {$key}: expected {$expected}",
            [
                'config_key' => $key,
                'provided_value' => $value,
                'expected_type' => $expected,
                'type' => 'invalid_config_value'
            ]
        );
    }
}
```

#### Exception Handler Implementation

**Location:** `app/Exceptions/Handler.php`

**Complete Implementation:**

```php
<?php

declare(strict_types=1);

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Console\OutputStyle;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

/**
 * Custom exception handler for the validate-links application.
 *
 * Provides specialized handling for ValidateLinksException instances
 * with proper console formatting and context display.
 */
final class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        ValidationException::class,
    ];

    /**
     * Render an exception into a console response.
     *
     * @param mixed $request The request instance (not used in console)
     * @param Throwable $exception The exception to render
     * @return mixed
     */
    public function render($request, Throwable $exception)
    {
        if ($exception instanceof ValidateLinksException) {
            return $this->renderValidateLinksException($exception);
        }

        return parent::render($request, $exception);
    }

    /**
     * Render a ValidateLinksException with proper console formatting.
     *
     * @param ValidateLinksException $exception The exception to render
     * @return void
     */
    private function renderValidateLinksException(ValidateLinksException $exception): void
    {
        $output = $this->getConsoleOutput();

        // Display error header with severity-based coloring
        $color = $this->getSeverityColor($exception->getSeverity());
        $output->writeln("<fg={$color};options=bold>Error [{$exception->getErrorCode()}]: {$exception->getMessage()}</>");

        // Display context information if available
        if ($context = $exception->getContext()) {
            $output->writeln('<comment>Context:</comment>');
            foreach ($context as $key => $value) {
                $formattedValue = is_array($value) ? json_encode($value) : (string) $value;
                $output->writeln("  <info>{$key}:</info> {$formattedValue}");
            }
        }

        // Display recommended action
        $action = $exception->getRecommendedAction();
        if ($action) {
            $output->writeln("<info>Recommended action:</info> {$action}");
        }

        // Display stack trace in debug mode
        if (config('app.debug')) {
            $output->writeln('<comment>Stack trace:</comment>');
            $output->writeln($exception->getTraceAsString());
        }

        // Log the exception if required
        if ($exception->shouldLog()) {
            $this->report($exception);
        }
    }

    /**
     * Get console output instance.
     *
     * @return OutputInterface
     */
    private function getConsoleOutput(): OutputInterface
    {
        $kernel = app('Illuminate\Contracts\Console\Kernel');
        return $kernel->output();
    }

    /**
     * Get color for severity level.
     *
     * @param string $severity The severity level
     * @return string Console color name
     */
    private function getSeverityColor(string $severity): string
    {
        return match ($severity) {
            'low' => 'blue',
            'medium' => 'yellow',
            'high' => 'magenta',
            'critical' => 'red',
            default => 'white'
        };
    }

    /**
     * Report or log an exception.
     *
     * @param Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        if ($exception instanceof ValidateLinksException) {
            // Custom logging for ValidateLinksException
            logger()->error($exception->getFormattedMessage(), [
                'exception' => get_class($exception),
                'error_code' => $exception->getErrorCode(),
                'severity' => $exception->getSeverity(),
                'context' => $exception->getContext(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ]);
            return;
        }

        parent::report($exception);
    }
}
```

## 2.3. Phase 2: Implement Laravel Prompts Integration

### 2.3.1. Interactive Command Enhancement

**Status:** Partially implemented  
**Priority:** High  
**Estimated Time:** 6-8 hours

**Step 1: Enhance ValidateCommand with Interactive Features**

```php
<?php
// app/Commands/ValidateCommand.php - Add interactive methods

use function Laravel\Prompts\{confirm, select, multiselect, text, progress};

protected function handleInteractive(): int
{
    $this->info('🔗 Interactive Link Validation Setup');
    $this->newLine();

    // Gather paths
    $paths = $this->gatherPaths();
    
    // Gather validation scope
    $scope = $this->gatherValidationScope();
    
    // Gather output preferences
    $outputConfig = $this->gatherOutputConfiguration();
    
    // Gather advanced options
    $advancedOptions = $this->gatherAdvancedOptions();

    // Confirm configuration
    if (!$this->confirmConfiguration($paths, $scope, $outputConfig, $advancedOptions)) {
        $this->warn('Validation cancelled.');
        return self::FAILURE;
    }

    // Execute validation with real-time feedback
    return $this->executeInteractiveValidation($paths, $scope, $outputConfig, $advancedOptions);
}

private function gatherPaths(): array
{
    $paths = [];
    
    do {
        $path = text(
            label: 'Enter path to validate',
            placeholder: './docs',
            required: true,
            validate: fn (string $value) => is_dir($value) || is_file($value) 
                ? null 
                : 'Path must exist'
        );
        
        $paths[] = $path;
        
        $addMore = confirm('Add another path?', false);
    } while ($addMore);

    return $paths;
}

private function gatherValidationScope(): array
{
    return multiselect(
        label: 'What types of links should be validated?',
        options: [
            'internal' => 'Internal links (within project)',
            'anchor' => 'Anchor links (headings)',
            'cross-reference' => 'Cross-references between files',
            'external' => 'External links (internet)',
        ],
        default: ['internal', 'anchor'],
        required: true,
        hint: 'Use space to select, enter to confirm'
    );
}

private function gatherOutputConfiguration(): array
{
    $format = select(
        label: 'Output format',
        options: [
            'console' => 'Console (colored output)',
            'json' => 'JSON (machine readable)',
            'markdown' => 'Markdown (documentation)',
            'html' => 'HTML (web report)',
        ],
        default: 'console'
    );

    $outputFile = null;
    if ($format !== 'console') {
        $saveToFile = confirm('Save output to file?', true);
        
        if ($saveToFile) {
            $outputFile = text(
                label: 'Output file path',
                placeholder: "./reports/validation-report.{$format}",
                required: true
            );
        }
    }

    return [
        'format' => $format,
        'file' => $outputFile,
    ];
}

private function executeInteractiveValidation(array $paths, array $scope, array $outputConfig, array $options): int
{
    $totalFiles = $this->countFiles($paths);
    
    $progress = progress(
        label: 'Validating links',
        steps: $totalFiles
    );

    $results = [];
    $fileCount = 0;

    foreach ($paths as $path) {
        $files = $this->getFilesFromPath($path);
        
        foreach ($files as $file) {
            $progress->label("Validating: " . basename($file));
            
            $fileResults = $this->linkValidationService->validateFile($file, $scope);
            $results[] = $fileResults;
            
            $fileCount++;
            $progress->advance();
            
            // Show real-time broken link alerts
            if ($this->hasBrokenLinks($fileResults)) {
                $this->warn("⚠️  Broken links found in: {$file}");
            }
        }
    }

    $progress->finish();

    // Generate and display/save report
    $report = $this->reportingService->generateReport($results, $outputConfig['format']);
    
    if ($outputConfig['file']) {
        $this->reportingService->saveReport($report, $outputConfig['file']);
        $this->info("Report saved to: {$outputConfig['file']}");
    } else {
        $this->line($report);
    }

    // Show summary
    $summary = $this->reportingService->generateSummary($results);
    $this->displaySummary($summary);

    return $summary['broken_links'] > 0 ? self::FAILURE : self::SUCCESS;
}
```

### 2.3.2. Interactive Configuration Command

**Step 1: Enhance ConfigCommand**

```php
<?php
// app/Commands/ConfigCommand.php - Add interactive configuration

protected function handleInteractiveConfig(): int
{
    $this->info('🔧 Interactive Configuration Setup');
    $this->newLine();

    $config = [];

    // Validation defaults
    $config['defaults'] = $this->configureDefaults();
    
    // Security settings
    $config['security'] = $this->configureSecurity();
    
    // Performance settings
    $config['performance'] = $this->configurePerformance();
    
    // Output preferences
    $config['output'] = $this->configureOutput();

    // Save configuration
    if ($this->saveConfiguration($config)) {
        $this->info('✅ Configuration saved successfully!');
        return self::SUCCESS;
    }

    $this->error('❌ Failed to save configuration.');
    return self::FAILURE;
}

private function configureDefaults(): array
{
    return [
        'max_depth' => (int) text(
            label: 'Maximum directory traversal depth (0 = unlimited)',
            default: '0',
            validate: fn ($value) => is_numeric($value) && $value >= 0 
                ? null 
                : 'Must be a non-negative number'
        ),
        
        'timeout' => (int) text(
            label: 'External link timeout (seconds)',
            default: '30',
            validate: fn ($value) => is_numeric($value) && $value > 0 
                ? null 
                : 'Must be a positive number'
        ),
        
        'max_broken' => (int) text(
            label: 'Maximum broken links before stopping (0 = unlimited)',
            default: '50',
            validate: fn ($value) => is_numeric($value) && $value >= 0 
                ? null 
                : 'Must be a non-negative number'
        ),
    ];
}
```

## 2.4. Phase 3: Expand Test Coverage

**📖 Cross-References:**
- **[Testing Documentation Suite](070-testing-documentation.md)** - Complete testing strategies and examples
- **[CI/CD Testing Pipeline](080-cicd-documentation.md#82-main-testing-pipeline)** - Automated testing in CI/CD
- **[Code Quality Pipeline](080-cicd-documentation.md#83-code-quality-pipeline)** - Quality assurance automation

### 2.4.1. Comprehensive Unit Tests

**Status:** Partial coverage
**Priority:** High
**Estimated Time:** 8-10 hours

**📋 Testing Implementation Checklist:**
- [ ] Unit tests for all service classes ([Unit Testing Guide](070-testing-documentation.md#72-unit-testing))
- [ ] Feature tests for command workflows ([Feature Testing](070-testing-documentation.md#74-feature-testing))
- [ ] Performance benchmarks ([Performance Testing](070-testing-documentation.md#76-performance-testing))
- [ ] Integration tests ([Integration Testing](070-testing-documentation.md#77-integration-testing))

**🔗 Related Documentation:**
- [Complete Testing Examples](070-testing-documentation.md#72-unit-testing)
- [Test Data and Helpers](070-testing-documentation.md#78-test-data-and-helpers)

**Step 1: Create Service Test Suite**

```php
// tests/Unit/Services/LinkValidationServiceTest.php
<?php

use App\Services\LinkValidationService;
use App\Services\SecurityValidationService;
use App\Services\GitHubAnchorService;

describe('LinkValidationService', function () {
    beforeEach(function () {
        $this->securityService = mock(SecurityValidationService::class);
        $this->anchorService = mock(GitHubAnchorService::class);
        
        $this->service = new LinkValidationService(
            $this->securityService,
            $this->anchorService
        );
    });

    describe('validateFile', function () {
        it('validates internal links correctly', function () {
            $testFile = __DIR__ . '/../../fixtures/test.md';
            file_put_contents($testFile, '[Link](./other.md)');
            
            $result = $this->service->validateFile($testFile, ['internal']);
            
            expect($result)->toHaveKey('file', $testFile);
            expect($result['links'])->toHaveCount(1);
            
            unlink($testFile);
        });

        it('handles broken internal links', function () {
            $testFile = __DIR__ . '/../../fixtures/test.md';
            file_put_contents($testFile, '[Broken](./nonexistent.md)');
            
            $result = $this->service->validateFile($testFile, ['internal']);
            
            expect($result['links'][0]['status'])->toBe('broken');
            
            unlink($testFile);
        });
    });

    describe('validateExternalLinks', function () {
        it('validates external URLs', function () {
            $links = [
                ['url' => 'https://httpbin.org/status/200', 'type' => 'external'],
                ['url' => 'https://httpbin.org/status/404', 'type' => 'external'],
            ];

            $results = $this->service->validateExternalLinks($links);

            expect($results[0]['status'])->toBe('valid');
            expect($results[1]['status'])->toBe('broken');
        });
    });
});
```

**Step 2: Create Feature Tests**

```php
// tests/Feature/ValidateCommandTest.php
<?php

use Illuminate\Support\Facades\File;

describe('ValidateCommand', function () {
    beforeEach(function () {
        $this->testDir = __DIR__ . '/../fixtures/validation-test';
        File::makeDirectory($this->testDir, 0755, true, true);
    });

    afterEach(function () {
        File::deleteDirectory($this->testDir);
    });

    it('validates a simple markdown file', function () {
        $testFile = $this->testDir . '/test.md';
        file_put_contents($testFile, '# Test\n[Valid Link](https://example.com)');

        $this->artisan('validate', ['path' => [$testFile]])
            ->expectsOutput('Validation completed')
            ->assertExitCode(0);
    });

    it('detects broken internal links', function () {
        $testFile = $this->testDir . '/test.md';
        file_put_contents($testFile, '[Broken](./nonexistent.md)');

        $this->artisan('validate', ['path' => [$testFile]])
            ->expectsOutput('Broken links found')
            ->assertExitCode(1);
    });

    it('supports interactive mode', function () {
        $testFile = $this->testDir . '/test.md';
        file_put_contents($testFile, '# Test');

        $this->artisan('validate', ['--interactive' => true])
            ->expectsQuestion('Enter path to validate', $testFile)
            ->expectsChoice('What types of links should be validated?', ['internal'], [
                'internal' => 'Internal links (within project)',
                'anchor' => 'Anchor links (headings)',
                'cross-reference' => 'Cross-references between files',
                'external' => 'External links (internet)',
            ])
            ->assertExitCode(0);
    });
});
```

### 2.4.2. Integration Tests

**Step 1: Create End-to-End Tests**

```php
// tests/Feature/IntegrationTest.php
<?php

describe('Full Validation Workflow', function () {
    beforeEach(function () {
        $this->setupTestProject();
    });

    afterEach(function () {
        $this->cleanupTestProject();
    });

    it('validates a complete project structure', function () {
        // Create test project with various link types
        $this->createTestFiles([
            'README.md' => '# Project\n[Docs](./docs/index.md)\n[External](https://example.com)',
            'docs/index.md' => '# Docs\n[Back](../README.md)\n## Section {#section}',
            'docs/guide.md' => '# Guide\n[Section](./index.md#section)',
        ]);

        $this->artisan('validate', [
            'path' => [$this->testProjectPath],
            '--scope' => ['internal', 'anchor', 'cross-reference'],
            '--format' => 'json',
        ])
        ->assertExitCode(0);
    });
});
```

## 2.5. Phase 4: Performance Optimization

### 2.5.1. Implement Caching

**Step 1: Add Caching to External Link Validation**

```php
// app/Services/LinkValidationService.php - Add caching

use Illuminate\Support\Facades\Cache;

public function validateExternalLinks(array $links): array
{
    $results = [];
    
    foreach ($links as $link) {
        $cacheKey = 'link_validation_' . md5($link['url']);
        
        $result = Cache::remember($cacheKey, now()->addHours(24), function () use ($link) {
            return $this->performExternalValidation($link);
        });
        
        $results[] = $result;
    }
    
    return $results;
}
```

### 2.5.2. Implement Concurrent Processing

**Step 1: Add Concurrent External Link Validation**

```php
// app/Services/LinkValidationService.php - Add concurrency

use Illuminate\Support\Collection;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;

public function validateExternalLinksConcurrently(array $links, int $concurrency = 10): array
{
    $client = new Client(['timeout' => $this->timeout]);
    $requests = [];
    
    foreach ($links as $index => $link) {
        $requests[$index] = new Request('HEAD', $link['url']);
    }

    $results = [];
    
    $pool = new Pool($client, $requests, [
        'concurrency' => $concurrency,
        'fulfilled' => function ($response, $index) use (&$results, $links) {
            $results[$index] = [
                'url' => $links[$index]['url'],
                'status' => $response->getStatusCode() < 400 ? 'valid' : 'broken',
                'status_code' => $response->getStatusCode(),
            ];
        },
        'rejected' => function ($reason, $index) use (&$results, $links) {
            $results[$index] = [
                'url' => $links[$index]['url'],
                'status' => 'broken',
                'reason' => $reason->getMessage(),
            ];
        },
    ]);

    $promise = $pool->promise();
    $promise->wait();

    return $results;
}
```

## 2.6. Phase 5: Production Deployment Preparation

**📖 Cross-References:**
- **[CI/CD Implementation Guide](080-cicd-documentation.md)** - Complete deployment pipeline documentation
- **[Deployment Pipeline](080-cicd-documentation.md#85-deployment-pipeline)** - Automated deployment workflows
- **[Security and Compliance](080-cicd-documentation.md#86-security-and-compliance-pipeline)** - Security scanning and compliance

**📋 Deployment Checklist:**
- [ ] Production environment configuration
- [ ] CI/CD pipeline setup ([CI/CD Guide](080-cicd-documentation.md#82-main-testing-pipeline))
- [ ] Security scanning implementation ([Security Pipeline](080-cicd-documentation.md#861-security-scanning-workflow))
- [ ] Performance monitoring ([Performance Testing](080-cicd-documentation.md#862-performance-monitoring))
- [ ] Build and distribution automation

### 2.6.1. Environment Configuration

**🔗 Related Documentation:**
- [Complete CI/CD Workflows](080-cicd-documentation.md#82-main-testing-pipeline)
- [Deployment Configuration](080-cicd-documentation.md#852-deployment-configuration)

**Step 1: Create Production Configuration**

```php
<?php
// config/validate-links.php - Add production settings

declare(strict_types=1);

return [
    'environment' => env('APP_ENV', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Default Validation Settings
    |--------------------------------------------------------------------------
    |
    | These configuration options control the default behavior of the
    | link validation engine. All values can be overridden via CLI options.
    |
    */
    'defaults' => [
        'case_sensitive' => env('VALIDATE_LINKS_CASE_SENSITIVE', false),
        'check_external' => env('VALIDATE_LINKS_CHECK_EXTERNAL', false),
        'concurrency' => env('VALIDATE_LINKS_CONCURRENCY', 10),
        'include_hidden' => env('VALIDATE_LINKS_INCLUDE_HIDDEN', false),
        'max_broken' => env('VALIDATE_LINKS_MAX_BROKEN', 50),
        'max_depth' => env('VALIDATE_LINKS_MAX_DEPTH', 0),
        'max_files' => env('VALIDATE_LINKS_MAX_FILES', 0),
        'timeout' => env('VALIDATE_LINKS_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Scopes
    |--------------------------------------------------------------------------
    |
    | Define the types of links that can be validated. Each scope
    | corresponds to a specific validation strategy.
    |
    */
    'scopes' => [
        'internal' => [
            'name' => 'Internal file links',
            'description' => 'Links to files within the same project',
            'enabled' => true,
        ],
        'anchor' => [
            'name' => 'Anchor/heading links',
            'description' => 'Links to headings within markdown files',
            'enabled' => true,
        ],
        'cross-reference' => [
            'name' => 'Cross-reference links',
            'description' => 'Links between different files in the project',
            'enabled' => true,
        ],
        'external' => [
            'name' => 'External HTTP/HTTPS links',
            'description' => 'Links to external websites and resources',
            'enabled' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Output Formatters
    |--------------------------------------------------------------------------
    |
    | Register the available output formatters for validation results.
    | Each formatter handles a specific output format.
    |
    */
    'formatters' => [
        'console' => App\Services\Formatters\ConsoleFormatter::class,
        'json' => App\Services\Formatters\JsonFormatter::class,
        'markdown' => App\Services\Formatters\MarkdownFormatter::class,
        'html' => App\Services\Formatters\HtmlFormatter::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Configure security constraints for file processing and link validation.
    |
    */
    'security' => [
        'allowed_protocols' => ['http', 'https'],
        'max_file_size' => 10 * 1024 * 1024, // 10MB
        'allowed_extensions' => ['.md', '.mdc', '.markdown', '.html', '.htm', '.txt', '.rst'],
        'blocked_domains' => [
            'localhost',
            '127.0.0.1',
            '0.0.0.0',
        ],
        'allowed_base_paths' => [
            // Will be populated dynamically
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | External Link Validation
    |--------------------------------------------------------------------------
    |
    | Configuration for validating external HTTP/HTTPS links.
    |
    */
    'external' => [
        'user_agent' => 'validate-links/2.0 (+https://github.com/s-a-c/validate-links)',
        'follow_redirects' => true,
        'max_redirects' => 5,
        'verify_ssl' => true,
        'timeout' => 30,
        'retry_attempts' => 3,
        'retry_delay' => 1000, // milliseconds
        'concurrent_requests' => 10,
        'rate_limit' => [
            'enabled' => true,
            'requests_per_second' => 5,
            'burst_limit' => 20,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | File Processing
    |--------------------------------------------------------------------------
    |
    | Configuration for file discovery and processing.
    |
    */
    'files' => [
        'default_patterns' => [
            '**/*.md',
            '**/*.mdc',
            '**/*.markdown',
            '**/*.html',
            '**/*.htm',
        ],
        'exclude_patterns' => [
            'node_modules/**',
            'vendor/**',
            '.git/**',
            'build/**',
            'dist/**',
            'coverage/**',
        ],
        'hidden_files' => [
            'include_by_default' => false,
            'specific_includes' => [
                '.ai/**/*.md',
                '.github/**/*.md',
                '.docs/**/*.md',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | GitHub Integration
    |--------------------------------------------------------------------------
    |
    | Settings for GitHub-specific features like anchor generation.
    |
    */
    'github' => [
        'anchor_generation' => [
            'algorithm' => 'github-compatible',
            'case_sensitive' => false,
            'preserve_unicode' => true,
        ],
        'api' => [
            'base_url' => 'https://api.github.com',
            'timeout' => 30,
            'rate_limit_aware' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Reporting Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for report generation and output formatting.
    |
    */
    'reporting' => [
        'console' => [
            'colors' => env('VALIDATE_LINKS_COLORS', true),
            'progress_bar' => true,
            'show_statistics' => true,
            'verbose_errors' => false,
        ],
        'file_output' => [
            'create_directories' => true,
            'overwrite_existing' => true,
            'backup_existing' => false,
        ],
        'statistics' => [
            'track_performance' => true,
            'include_memory_usage' => true,
            'detailed_timing' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Optimization settings for large documentation sets.
    |
    */

    'cache' => [
        'enabled' => env('VALIDATE_LINKS_CACHE_ENABLED', true),
        'store' => env('VALIDATE_LINKS_CACHE_STORE', 'file'),
        'ttl' => env('VALIDATE_LINKS_CACHE_TTL', 86400), // 24 hours
    ],

    'logging' => [
        'enabled' => env('VALIDATE_LINKS_LOGGING_ENABLED', true),
        'channel' => env('VALIDATE_LINKS_LOG_CHANNEL', 'single'),
        'level' => env('VALIDATE_LINKS_LOG_LEVEL', 'info'),
    ],

    'performance' => [
        'chunk_size' => env('VALIDATE_LINKS_FILES_PER_CHUNK', 100), // Files per chunk
        'max_execution_time' => env('VALIDATE_LINKS_MAX_EXECUTION_TIME', 300),
        'memory_limit' => env('VALIDATE_LINKS_MEMORY_LIMIT', '512M'),
        'parallel_processing' => [
            'enabled' => env('VALIDATE_LINKS_PARALLEL_ENABLED', true),
            'max_workers' => env('VALIDATE_LINKS_PARALLEL_WORKERS', 4),
            'batch_size' => env('VALIDATE_LINKS_PARALLEL_BATCH_SIZE', 25),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Link Remediation (Fix Mode)
    |--------------------------------------------------------------------------
    |
    | Configuration for automatic link fixing capabilities.
    |
    */
    'remediation' => [
        'enabled' => false,
        'backup_files' => true,
        'backup_directory' => storage_path('app/validate-links/backups'),
        'strategies' => [
            'case_mismatch' => true,
            'extension_correction' => true,
            'path_normalization' => true,
            'anchor_generation' => true,
        ],
        'interactive_mode' => [
            'confirm_changes' => true,
            'show_diff' => true,
            'batch_approval' => false,
        ],
    ],
];
```

**Step 2: Create Production Environment File**

```bash
# .env.production
APP_ENV=production
APP_DEBUG=false

VALIDATE_LINKS_CACHE_ENABLED=true
VALIDATE_LINKS_CACHE_TTL=86400
VALIDATE_LINKS_CONCURRENCY=20
VALIDATE_LINKS_TIMEOUT=15
VALIDATE_LINKS_MEMORY_LIMIT=1024M
```

### 2.6.2. Build and Distribution

**Step 1: Create Build Script**

```bash
#!/bin/bash
# scripts/build-production.sh

set -e

echo "🏗️  Building Laravel Zero validate-links for production..."

# Install production dependencies
composer install --no-dev --optimize-autoloader --no-interaction

# Run quality checks
composer test:all
composer phpstan
composer pint:test

# Build binary
php validate-links app:build validate-links --build-version=${BUILD_VERSION:-dev}

# Create distribution package
tar -czf validate-links-${BUILD_VERSION:-dev}.tar.gz \
    --exclude='.git*' \
    --exclude='tests' \
    --exclude='node_modules' \
    --exclude='*.md' \
    validate-links

echo "✅ Build completed: validate-links-${BUILD_VERSION:-dev}.tar.gz"
```

## 2.7. Comprehensive Testing Documentation

### 2.7.1. Test Suite Architecture

**Framework Overview:**
- **Testing Framework:** Pest PHP (^3.8.2)
- **Base Framework:** PHPUnit (underlying)
- **Mocking Framework:** Mockery (^1.6.12)
- **Architecture Testing:** Pest Plugin Arch (^3.1)
- **Type Coverage:** Pest Plugin Type Coverage (^3.6)
- **Stress Testing:** Pest Plugin Stressless (^3.1)

**Test Directory Structure:**

```
tests/
├── Feature/           # End-to-end functionality tests
│   └── InspireCommandTest.php
├── Unit/              # Individual class/method isolation tests
│   ├── ExampleTest.php
│   ├── LinkValidationServiceTest.php
│   ├── ValidateCommandTest.php
│   └── Services/
│       └── SecurityValidationServiceTest.php
├── Pest.php           # Pest configuration and helpers
└── TestCase.php       # Base test case class
```

### 2.7.2. Test Type Boundaries

#### Unit Tests (`tests/Unit/`)

**Purpose:** Test individual classes and methods in isolation

**Scope:**

- Single class or method testing
- Mock all external dependencies
- Fast execution (< 100ms per test)
- No file system or network operations
- No database interactions

**Examples:**

- Value Object behavior testing
- Service method logic validation
- Command option parsing
- Formatter output generation

**Test Pattern:**

```php
it('validates internal links', function () {
    $service = app(LinkValidationService::class);
    $config = new ValidationConfig(
        paths: ['path/to/docs'],
        scope: ['internal'],
        checkExternal: false
    );
    
    $result = $service->validate($config);
    
    expect($result)->toBeInstanceOf(ValidationResult::class);
    expect($result->isSuccessful())->toBeTrue();
});
```

#### Feature Tests (`tests/Feature/`)

**Purpose:** Test complete user workflows and command interactions

**Scope:**

- Full command execution testing
- File system interactions allowed
- Multiple service integration
- Real configuration usage
- Output format validation

**Examples:**
- Complete validation workflows
- Command option combinations
- Output format generation
- Error handling scenarios

**Test Pattern:**

```php
it('validates documentation with broken links', function () {
    // Create test files with known broken links
    $testDir = 'tests/fixtures/broken-links';
    
    $this->artisan('validate', ['path' => $testDir])
        ->expectsOutput('🔗 Validating links in tests/fixtures/broken-links')
        ->expectsOutput('❌ Found 2 broken links')
        ->assertExitCode(1);
});
```

#### Integration Tests (Proposed)

**Purpose:** Test service interactions and external dependencies

**Scope:**

- Service-to-service communication
- External API interactions (mocked)
- Configuration integration
- Performance under load

### 2.7.3. Coverage Requirements

**Code Coverage Targets:**

- **Minimum Code Coverage:** 80%
- **Target Code Coverage:** 95%
- **Type Coverage:** 95% (enforced by Pest Type Coverage plugin)

**Coverage Configuration:**

```xml
<!-- phpunit.xml -->
<coverage>
    <report>
        <clover outputFile="coverage/clover.xml"/>
        <html outputDirectory="coverage/html"/>
        <text outputFile="coverage/coverage.txt"/>
    </report>
</coverage>
```

### 2.7.4. Test Execution Instructions

#### Running All Tests

```bash
# Run complete test suite
composer test

# Run with coverage
composer test:coverage

# Run with type coverage
composer test:type-coverage
```

#### Running Specific Test Types

```bash
# Unit tests only
vendor/bin/pest tests/Unit

# Feature tests only
vendor/bin/pest tests/Feature

# Specific test file
vendor/bin/pest tests/Unit/LinkValidationServiceTest.php
```

#### Parallel Testing

```bash
# Run tests in parallel (8 processes)
vendor/bin/pest --parallel

# Custom process count
vendor/bin/pest --parallel --processes=4
```

### 2.7.5. Implementation Verification Checklist

**Service Implementation:**

- [ ] SecurityValidationService fully implemented
- [ ] ReportingService fully implemented  
- [ ] All service contracts have concrete implementations
- [ ] Service provider registrations complete
- [ ] Dependency injection working correctly

**Interactive Features:**

- [ ] Laravel Prompts integration complete
- [ ] Interactive validation workflow functional
- [ ] Interactive configuration setup working
- [ ] Real-time progress feedback implemented
- [ ] User-friendly error messages

**Testing Coverage:**

- [ ] Unit tests for all services (>90% coverage)
- [ ] Feature tests for all commands
- [ ] Integration tests for full workflows
- [ ] Performance tests for large datasets
- [ ] Edge case testing complete
- [ ] Type coverage >95%
- [ ] All tests passing in parallel execution

**Performance:**

- [ ] Caching implemented for external links
- [ ] Concurrent processing functional
- [ ] Memory usage optimized
- [ ] Large file handling efficient

**Production Readiness:**

- [ ] Production configuration complete
- [ ] Build process functional
- [ ] Distribution packages created
- [ ] Deployment scripts tested

### 2.7.2. Final Testing Script

```bash
#!/bin/bash
# scripts/verify-implementation.sh

echo "🧪 Verifying Laravel Zero validate-links implementation..."

# Run comprehensive test suite
echo "Running test suite..."
composer test:all

# Test interactive features
echo "Testing interactive mode..."
echo -e "docs\ninternal\nconsole\nn\ny" | php validate-links validate --interactive

# Test all output formats
echo "Testing output formats..."
php validate-links validate docs --format=json --output=test-report.json
php validate-links validate docs --format=markdown --output=test-report.md
php validate-links validate docs --format=html --output=test-report.html

# Test configuration
echo "Testing configuration..."
php validate-links config --show

# Performance test
echo "Running performance test..."
time php validate-links validate docs --scope=all

# Build test
echo "Testing build process..."
php validate-links app:build validate-links-test

echo "✅ Implementation verification complete!"
```

## 2.8. Timeline and Milestones

### 2.8.1. Detailed Implementation Schedule

**Week 1 (Days 1-3): Core Services**

- Day 1: SecurityValidationService implementation and testing
- Day 2: ReportingService implementation and testing
- Day 3: Complete partial service implementations

**Week 1 (Days 4-5): Interactive Features**

- Day 4: Laravel Prompts integration in ValidateCommand
- Day 5: Interactive ConfigCommand and testing

**Week 2 (Days 6-8): Testing and Performance**

- Day 6: Comprehensive unit and feature tests
- Day 7: Integration tests and performance optimization
- Day 8: Caching and concurrent processing

**Week 2 (Days 9-10): Production Preparation**

- Day 9: Production configuration and build process
- Day 10: Final testing, verification, and documentation

### 2.8.2. Success Criteria

**Functional Requirements:**

- All service contracts have complete implementations
- Interactive CLI experience fully functional
- All output formats working correctly
- Comprehensive test coverage (>90%)
- Performance optimizations implemented

**Quality Requirements:**

- All quality gates passing (PHPStan, Pint, Insights)
- No critical security vulnerabilities
- Memory usage optimized for large projects
- Error handling comprehensive and user-friendly

**Production Requirements:**

- Build process creates functional binaries
- Distribution packages work on target platforms
- Configuration system supports all environments
- Documentation complete and accurate

## 2.9. Troubleshooting Common Issues

### 2.9.1. Service Registration Issues

**Problem:** Services not being injected correctly

**Solution:**

## 2.7. Service Registration and Dependency Injection

**Status:** Critical for proper application functionality
**Priority:** High
**Estimated Time:** 1-2 hours

### 2.7.1. Understanding Laravel Service Registration

Laravel's service container is the heart of dependency injection. Understanding the registration process is crucial for proper application architecture.

**🔗 Related Documentation:**
- [Architecture Overview - Service Provider Architecture](030-architecture-overview.md#333-service-provider-architecture)
- [Laravel Service Container Documentation](https://laravel.com/docs/12.x/container)

### 2.7.2. Service Provider Registration Methods

#### **register() Method**
The `register()` method is called **first** and should only bind services into the container. No other services should be accessed here.

```php
public function register(): void
{
    // ✅ CORRECT: Bind interfaces to implementations
    $this->app->singleton(LinkValidationInterface::class, LinkValidationService::class);

    // ✅ CORRECT: Register with factory closure
    $this->app->singleton(ReportingService::class, function ($app) {
        return new ReportingService(
            $app->make(StatisticsInterface::class)
        );
    });

    // ❌ INCORRECT: Don't access other services here
    // $config = $this->app->make(ConfigurationService::class); // This could fail!
}
```

#### **boot() Method**
The `boot()` method is called **after** all providers have been registered. Use this for configuration, event listeners, and accessing other services.

```php
public function boot(): void
{
    // ✅ CORRECT: Access other services safely
    $config = $this->app->make(ConfigurationService::class);

    // ✅ CORRECT: Publish configuration files
    $this->publishes([
        __DIR__.'/../../config/validate-links.php' => config_path('validate-links.php'),
    ], 'validate-links-config');

    // ✅ CORRECT: Register event listeners
    Event::listen(ValidationCompleted::class, ValidationCompletedListener::class);
}
```

### 2.7.3. Provider Registration in bootstrap/providers.php

**Laravel 12 Approach:** Services are registered in the providers array in `bootstrap/providers.php`:

```php
<?php
// bootstrap/providers.php

return [
    App\Providers\AppServiceProvider::class,
    App\Providers\ValidateLinksServiceProvider::class,
];
```

**Why This Approach:**
- **Performance:** Providers are loaded only when needed
- **Organization:** Clear separation of concerns
- **Maintainability:** Easy to add/remove providers
- **Testing:** Individual providers can be tested in isolation

**Laravel 12 Enhancements:**
- **Improved Performance:** Enhanced service container resolution
- **Better Developer Experience:** Improved error messages and debugging
- **Enhanced Security:** Updated security features and best practices
- **Modern PHP Support:** Full PHP 8.4+ compatibility with latest features

### 2.7.4. Complete Service Provider Implementation

```php
<?php
// app/Providers/ValidateLinksServiceProvider.php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;

// Service Contracts
use App\Services\Contracts\LinkValidationInterface;
use App\Services\Contracts\SecurityValidationInterface;
use App\Services\Contracts\ReportingInterface;
use App\Services\Contracts\StatisticsInterface;
use App\Services\Contracts\GitHubAnchorInterface;

// Service Implementations
use App\Services\LinkValidationService;
use App\Services\SecurityValidationService;
use App\Services\ReportingService;
use App\Services\StatisticsService;
use App\Services\GitHubAnchorService;

// Events and Listeners
use App\Events\ValidationCompleted;
use App\Listeners\ValidationCompletedListener;

class ValidateLinksServiceProvider extends ServiceProvider
{
    /**
     * Register services into the container.
     *
     * This method is called FIRST, before boot().
     * Only bind services here - don't access other services.
     */
    public function register(): void
    {
        // Core service interfaces - Order matters for dependencies!
        $this->registerCoreServices();
        $this->registerFormatterServices();
        $this->registerUtilityServices();
    }

    /**
     * Bootstrap services after all providers are registered.
     *
     * This method is called AFTER all register() methods.
     * Safe to access other services here.
     */
    public function boot(): void
    {
        $this->publishConfiguration();
        $this->registerEventListeners();
        $this->configureServices();
    }

    /**
     * Register core validation services.
     */
    private function registerCoreServices(): void
    {
        // Statistics service (no dependencies)
        $this->app->singleton(StatisticsInterface::class, StatisticsService::class);

        // GitHub anchor service (no dependencies)
        $this->app->singleton(GitHubAnchorInterface::class, GitHubAnchorService::class);

        // Security validation service (no dependencies)
        $this->app->singleton(SecurityValidationInterface::class, SecurityValidationService::class);

        // Link validation service (depends on security and anchor services)
        $this->app->singleton(LinkValidationInterface::class, function ($app) {
            return new LinkValidationService(
                $app->make(SecurityValidationInterface::class),
                $app->make(GitHubAnchorInterface::class)
            );
        });

        // Reporting service (depends on statistics)
        $this->app->singleton(ReportingInterface::class, function ($app) {
            return new ReportingService(
                $app->make(StatisticsInterface::class)
            );
        });
    }

    /**
     * Register formatter services.
     */
    private function registerFormatterServices(): void
    {
        $this->app->singleton('validate-links.formatters.console', ConsoleFormatter::class);
        $this->app->singleton('validate-links.formatters.json', JsonFormatter::class);
        $this->app->singleton('validate-links.formatters.html', HtmlFormatter::class);
        $this->app->singleton('validate-links.formatters.markdown', MarkdownFormatter::class);
    }

    /**
     * Register utility services.
     */
    private function registerUtilityServices(): void
    {
        // Configuration service
        $this->app->singleton(ConfigurationInterface::class, function ($app) {
            return new ConfigurationService(config('validate-links'));
        });

        // Cache service
        $this->app->singleton(CacheInterface::class, function ($app) {
            return new CacheService(
                $app->make('cache.store'),
                config('validate-links.cache.ttl', 3600)
            );
        });
    }

    /**
     * Publish configuration files.
     */
    private function publishConfiguration(): void
    {
        $this->publishes([
            __DIR__.'/../../config/validate-links.php' => config_path('validate-links.php'),
        ], 'validate-links-config');
    }

    /**
     * Register event listeners.
     */
    private function registerEventListeners(): void
    {
        Event::listen(ValidationCompleted::class, ValidationCompletedListener::class);
    }

    /**
     * Configure services after registration.
     */
    private function configureServices(): void
    {
        // Configure reporting service with formatters
        $reportingService = $this->app->make(ReportingInterface::class);
        $reportingService->addFormatter('console', $this->app->make('validate-links.formatters.console'));
        $reportingService->addFormatter('json', $this->app->make('validate-links.formatters.json'));
        $reportingService->addFormatter('html', $this->app->make('validate-links.formatters.html'));
        $reportingService->addFormatter('markdown', $this->app->make('validate-links.formatters.markdown'));
    }
}
```

### 2.9.2. Laravel Prompts Issues

**Problem:** Interactive prompts not working in certain terminals

**Solution:**

```php
// Add fallback for non-interactive environments
protected function handleInteractive(): int
{
    if (!$this->input->isInteractive()) {
        $this->warn('Interactive mode requires a TTY. Falling back to standard mode.');
        return $this->handleStandard();
    }
    
    // ... interactive logic
}
```

### 2.9.3. Performance Issues

**Problem:** Slow validation on large projects

**Solution:**

```php
// Implement progress tracking and memory management
protected function validateLargeProject(array $files): array
{
    $results = [];
    $batchSize = 100;
    
    foreach (array_chunk($files, $batchSize) as $batch) {
        $batchResults = $this->processBatch($batch);
        $results = array_merge($results, $batchResults);
        
        // Clear memory
        gc_collect_cycles();
    }
    
    return $results;
}
```

### 2.7.5. AppServiceProvider vs Custom Service Providers

#### **When to Use AppServiceProvider**
Use `app/Providers/AppServiceProvider.php` for:
- **Framework-level services** (logging, debugging, etc.)
- **Third-party package bindings**
- **Global application concerns**

```php
<?php
// app/Providers/AppServiceProvider.php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // ✅ Framework-level services
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
        }

        // ✅ Global utilities
        $this->app->singleton('app.version', fn() => '1.0.0');
    }

    public function boot(): void
    {
        // ✅ Global configuration
        if ($this->app->environment('production')) {
            URL::forceScheme('https');
        }
    }
}
```

#### **When to Use Custom Service Providers**
Use `app/Providers/ValidateLinksServiceProvider.php` for:
- **Domain-specific services** (validation, reporting, etc.)
- **Business logic services**
- **Feature-specific concerns**

```php
<?php
// app/Providers/ValidateLinksServiceProvider.php
// (Complete implementation shown above)
```

### 2.7.6. Service Registration Best Practices

#### **1. Dependency Order Matters**
```php
// ✅ CORRECT: Register dependencies first
$this->app->singleton(StatisticsInterface::class, StatisticsService::class);
$this->app->singleton(ReportingInterface::class, function ($app) {
    return new ReportingService($app->make(StatisticsInterface::class));
});

// ❌ INCORRECT: Dependent service registered first
$this->app->singleton(ReportingInterface::class, function ($app) {
    return new ReportingService($app->make(StatisticsInterface::class)); // May fail!
});
$this->app->singleton(StatisticsInterface::class, StatisticsService::class);
```

#### **2. Use Singleton for Stateful Services**
```php
// ✅ CORRECT: Singleton for services that maintain state
$this->app->singleton(StatisticsInterface::class, StatisticsService::class);

// ✅ CORRECT: Bind for stateless services (new instance each time)
$this->app->bind(ValidationRequest::class, ValidationRequest::class);
```

#### **3. Factory Closures for Complex Dependencies**
```php
// ✅ CORRECT: Use closure for complex instantiation
$this->app->singleton(ReportingInterface::class, function ($app) {
    $service = new ReportingService($app->make(StatisticsInterface::class));

    // Configure formatters
    $service->addFormatter('console', new ConsoleFormatter());
    $service->addFormatter('json', new JsonFormatter());

    return $service;
});
```

#### **4. Interface Binding Pattern**
```php
// ✅ CORRECT: Always bind interfaces to implementations
$this->app->singleton(LinkValidationInterface::class, LinkValidationService::class);

// ❌ INCORRECT: Don't bind concrete classes to themselves
$this->app->singleton(LinkValidationService::class, LinkValidationService::class);
```

### 2.7.7. Testing Service Registration

```php
<?php
// tests/Unit/Providers/ValidateLinksServiceProviderTest.php

use Tests\TestCase;
use App\Providers\ValidateLinksServiceProvider;
use App\Services\Contracts\LinkValidationInterface;
use App\Services\LinkValidationService;

class ValidateLinksServiceProviderTest extends TestCase
{
    public function test_services_are_registered(): void
    {
        // Arrange
        $provider = new ValidateLinksServiceProvider($this->app);

        // Act
        $provider->register();

        // Assert
        $this->assertTrue($this->app->bound(LinkValidationInterface::class));
        $this->assertInstanceOf(
            LinkValidationService::class,
            $this->app->make(LinkValidationInterface::class)
        );
    }

    public function test_singleton_services_return_same_instance(): void
    {
        // Arrange
        $provider = new ValidateLinksServiceProvider($this->app);
        $provider->register();

        // Act
        $instance1 = $this->app->make(LinkValidationInterface::class);
        $instance2 = $this->app->make(LinkValidationInterface::class);

        // Assert
        $this->assertSame($instance1, $instance2);
    }
}
```

---

## 📖 Navigation

**[⬅️ Previous: Project Status](010-project-status.md)** | **[Next: Architecture Overview ➡️](030-architecture-overview.md)** | **[🔝 Top](#2-implementation-completion-guide)**
